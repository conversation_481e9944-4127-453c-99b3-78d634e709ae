import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').createTable('agency_team_invites', (table) => {
    table.increments('id').primary();
    table.integer('agency_id').notNullable().references('id').inTable('prf.profile');
    table.string('email', 255).notNullable();
    table.integer('agent_profile_id').references('id').inTable('prf.profile');
    table.string('invite_token', 255).notNullable().unique();
    table.string('status', 50).notNullable().defaultTo('invited');
    table.timestamp('invited_at', { useTz: true }).defaultTo(knex.fn.now());
    table.timestamp('expires_at', { useTz: true }).defaultTo(
        knex.raw("CURRENT_TIMESTAMP + interval '24 hours'")
    );
    table.timestamp('responded_at', { useTz: true });
  });

  await knex.schema.withSchema('agn').createTable('agent_agency_mapping', (table) => {
    table.increments('id').primary();
    table.integer('agency_id').notNullable().references('id').inTable('prf.profile');
    table.integer('agent_id').notNullable().references('id').inTable('prf.profile');
    table.timestamp('created_at', { useTz: true }).defaultTo(knex.fn.now());
    table.unique(['agency_id', 'agent_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').dropTableIfExists('agent_agency_mapping');
  await knex.schema.withSchema('agn').dropTableIfExists('agency_team_invites');
}
