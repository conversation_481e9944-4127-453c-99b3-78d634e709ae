import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Make agent_id nullable to support unregistered users
  await knex.schema.withSchema('prf').alterTable('tickets', (table) => {
    table.integer('agent_id').nullable().alter();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('prf').alterTable('tickets', (table) => {
    table.integer('agent_id').notNullable().alter();
  });
} 