{"id": "5da8f4cf-51bf-46d0-ac93-6407b45ad5c0", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"list.languages": {"name": "languages", "schema": "list", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "localName": {"name": "localName", "type": "text", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "statusId": {"name": "statusId", "type": "integer", "primaryKey": false, "notNull": true}, "createdOn": {"name": "createdOn", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": false}, "modifiedOn": {"name": "modifiedOn", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {"list": "list"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}