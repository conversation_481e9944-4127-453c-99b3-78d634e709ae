/agent/services:
  get:
    tags:
      - Agent Services
    summary: Get filtered services
    operationId: getFilteredServices
    description: Fetch services with filters like status, agentId, pricing, and search.
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: pageSize
        in: query
        schema:
          type: integer
          default: 10
      - name: search
        in: query
        schema:
          type: string
      - name: pricing
        in: query
        schema:
          type: string
          enum: [free, paid, ""]
      - name: statusId
        in: query
        schema:
          type: integer
          default: 20
    responses:
      "200":
        description: Services fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: object
                  properties:
                    services:
                      type: array
                      items:
                        $ref: "#/components/schemas/Service"
                    pagination:
                      $ref: "#/components/schemas/Pagination"

  post:
    tags:
      - Agent Services
    summary: Create or update a service
    operationId: createOrUpdateService
    security:
      - cookieAuth: []
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              id:
                type: integer
                description: ID of the service (for updates; leave empty to create new)
              title:
                type: string
                description: Service title
              isRemote:
                type: boolean
              locationIds:
                type: array
                items:
                  type: integer
              duration:
                type: integer
                description: Duration (from types table)
              isFree:
                type: boolean
              price:
                type: number
              specialOffer:
                type: string
              description:
                type: string
              serviceIds:
                type: array
                items:
                  type: integer
              websiteUrl:
                type: string
                format: uri
              servicePhotos:
                type: array
                items:
                  type: string
                  format: binary
    responses:
      "201":
        description: Service created or updated
      "400":
        description: Bad Request


/agent/services/{id}:
  get:
    tags:
      - Agent Services
    summary: Get service by ID
    operationId: getServiceById
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Service found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Service"
      "404":
        description: Service not found

  delete:
    tags:
      - Agent Services
    summary: Delete a service
    operationId: deleteService
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "204":
        description: Deleted successfully
      "404":
        description: Service not found

/agent/services/{id}/status:
  put:
    tags:
      - Agent Services
    summary: Update service status
    operationId: updateServiceStatus
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              status:
                type: integer
                default: 20
                description: select status 23 "Draft", 22 "Publish", 27 "Unpublished"
    responses:
      "200":
        description: Status updated

/agent/services/{id}/photos:
  patch:
    tags:
      - Agent Services
    summary: Update service photos
    operationId: updateServicePhotos
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              removePhotoIds:
                type: array
                items:
                  type: integer
              servicePhotos:
                type: array
                items:
                  type: string
                  format: binary
    responses:
      "200":
        description: Photos updated

/other/missions-and-types:
  get:
    tags:
      - Agent Services
    summary: Get missions and types
    operationId: getMissionsAndTypes
    description: Returns available missions and types
    responses:
      "200":
        description: Missions and types list
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string

components:
  schemas:
    Service:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        isRemote:
          type: boolean
        locations:
          type: array
          items:
            type: object
            properties:
              value:
                type: integer
              label:
                type: string
        services:
          type: array
          items:
            type: object
            properties:
              value:
                type: integer
              label:
                type: string
        duration:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        isFree:
          type: boolean
        price:
          type: number
        specialOffer:
          type: string
        description:
          type: string
        websiteUrl:
          type: string
        statusName:
          type: string
        images:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              url:
                type: string
