/agent/profile:
  get:
    tags:
      - Agent Profile
    summary: Get profile
    operationId: getAgentProfile
    description: Retrieve the agent profile information.
    security:
      - cookieAuth: []

    responses:
      "200":
        description: Profile retrieved successfully
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Profile"

/agent/profile/basic:
  put:
    tags:
      - Agent Profile
    summary: Update agent basic profile
    operationId: updateAgentProfileBasic
    description: Update basic information of the agent profile.
    security:
      - cookieAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ProfileBasicUpdate"
    responses:
      "200":
        description: Profile basic info updated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Profile basic info updated successfully

/agent/profile/info:
  put:
    tags:
      - Agent Profile
    summary: Update agent contact info
    operationId: updateAgentProfileContact
    description: Update contact information of the agent profile.
    security:
      - cookieAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ProfileContactUpdate"
    responses:
      "200":
        description: Profile updated
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Profile status updated successfully

/agents/auth/change-password:
  post:
    tags:
      - Agent Profile
    summary: Change agent password
    operationId: changeAgentPassword
    description: Change the password for the authenticated agent.
    security:
      - cookieAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ChangePasswordRequest"
    responses:
      "200":
        description: Password updated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Password updated successfully.

/agent/profile/status/{status}:
  put:
    tags:
      - Agent Profile
    summary: Update agent profile status
    operationId: updateAgentProfileStatus
    description: Update the status of the agent profile.
    security:
      - cookieAuth: []
    parameters:
      - name: status
        in: path
        required: true
        schema:
          type: string
    responses:
      "200":
        description: Profile updated
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Profile status updated successfully

/agent/profile/professional:
  put:
    tags:
      - Agent Profile
    summary: Update agent professional info
    operationId: updateAgentProfileProfessional
    description: Update professional information of the agent profile (experience, details, languages, specialization).
    security:
      - cookieAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ProfileProfessionalUpdate"
    responses:
      "200":
        description: Profile updated
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Profile status updated successfully

components:
  schemas:
    Profile:
      type: object
      required:
        - id
        - name
        - email
        - phone
        - status
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
        phone:
          type: string
        status:
          type: string
        details:
          type: string
          nullable: true
        experience:
          type: integer
          nullable: true
        languages:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              name:
                type: string
              localName:
                type: string
              code:
                type: string
          nullable: true
        specialization:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              name:
                type: string
          nullable: true

    ProfileBasicUpdate:
      type: object
      properties:
        profileImage:
          type: string
          description: URL or path to the profile image
        firstName:
          type: string
        lastName:
          type: string
        middleName:
          type: string
          nullable: true
        nationality:
          type: string
        gender:
          type: string

    ProfileContactUpdate:
      type: object
      properties:
        locationId:
          type: integer
        address:
          type: string

    ProfilePasswordUpdate:
      type: object
      required:
        - oldPassword
        - newPassword
        - confirmPassword
      properties:
        oldPassword:
          type: string
        newPassword:
          type: string
        confirmPassword:
          type: string

    ChangePasswordRequest:
      type: object
      required:
        - currentPassword
        - newPassword
        - confirmPassword
      properties:
        currentPassword:
          type: string
          description: Current password of the user
        newPassword:
          type: string
          description: New password (minimum 8 characters with uppercase, lowercase, number and special character)
        confirmPassword:
          type: string
          description: Confirmation of new password

    ProfileProfessionalUpdate:
      type: object
      properties:
        details:
          type: string
          nullable: true
        experience:
          type: integer
          nullable: true
        languages:
          type: array
          items:
            type: integer
          nullable: true
        specialization:
          type: array
          items:
            type: string
          nullable: true
