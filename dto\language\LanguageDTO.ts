import { z } from "zod";

export const LanguageSchema = z.object({
  id: z.number({error: "Language ID is required"}),
  name: z.string({error: "Language name is required"}).min(1, "Language name cannot not be empty"),
  localName: z.string().optional(),
  code: z.string().optional(),
  statusId: z.number({ error: "Language status id is required" }),
  createdOn: z.coerce.date().optional(),
  modifiedOn: z.coerce.date().optional(),
});

// Use for typing only
export type LanguageDTO = z.infer<typeof LanguageSchema>;