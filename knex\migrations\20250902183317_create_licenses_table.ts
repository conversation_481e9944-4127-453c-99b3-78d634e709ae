import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").createTable("licenses", (table) => {
    table.increments("id").primary();
    table.integer("profileId").notNullable();
    table.string("agencyId").notNullable();
    table.string("operationArea").notNullable();
    table.integer("roleId").nullable(); // optional, no FK
    table.string("licenseNumber").notNullable();
    table.date("licenseExpiryDate").notNullable();

    table.specificType("licenseFile", "text[]").notNullable();

    table.timestamp("createdOn", { useTz: true }).defaultTo(knex.fn.now());
    table.integer("createdBy").notNullable();

    // Foreign keys
    table
      .foreign("profileId")
      .references("id")
      .inTable("prf.profile")
      .onDelete("CASCADE");

    table
      .foreign("createdBy")
      .references("id")
      .inTable("sec.login")
      .onDelete("CASCADE");
  });

  // Unique index (profile + licenseNumber)
  await knex.schema.raw(`
    CREATE UNIQUE INDEX idx_licenses_profile_license
    ON agn."licenses"("profileId", "licenseNumber");
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw(
    `DROP INDEX IF EXISTS agn.idx_licenses_profile_license`
  );

  await knex.schema.withSchema("agn").dropTableIfExists("licenses");
}
