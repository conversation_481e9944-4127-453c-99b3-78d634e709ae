/admin/properties:
  get:
    tags:
      - Admin Properties
    summary: Get filtered properties
    operationId: adminGetFilteredProperties
    description: Fetch properties using filters like status, type, location, and listing type.
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: pageSize
        in: query
        schema:
          type: integer
          default: 10
      - name: status
        in: query
        schema:
          type: string
      - name: propertyTypeId
        in: query
        schema:
          type: integer
      - name: locationId
        in: query
        schema:
          type: integer
      - name: listingType
        in: query
        schema:
          type: integer
    responses:
      "200":
        description: Properties fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: object
                  properties:
                    properties:
                      type: array
                      items:
                        $ref: "#/components/schemas/Property"
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    statusCounts:
                      type: array
                      items:
                        $ref: "#/components/schemas/StatusCount"

/admin/properties/{id}:
  get:
    tags:
      - Admin Properties
    summary: Get property by ID
    operationId: adminGetPropertyById
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Property found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Property"
      "404":
        description: Property not found

  delete:
    tags:
      - Admin Properties
    summary: Delete a property
    operationId: adminDeleteProperty
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Property deleted
      "404":
        description: Property not found

/admin/properties/{id}/status:
  put:
    tags:
      - Admin Properties
    summary: Update property status
    operationId: adminUpdatePropertyStatus
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              status:
                type: string
    responses:
      "200":
        description: Status updated

/admin/properties/{id}/flag:
  put:
    tags:
      - Admin Properties
    summary: Toggle a flag (isFeatured or isVerified)
    operationId: adminTogglePropertyFlag
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              column:
                type: string
                enum: [isFeatured, isVerified]
    responses:
      "200":
        description: Flag toggled

/admin/properties/note/{id}:
  get:
    tags:
      - Admin Properties
    summary: Get notes for a property
    operationId: adminGetPropertyNotes
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Notes fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      note:
                        type: string
                      created_at:
                        type: string
                        format: date-time
                      created_by:
                        type: integer

/admin/properties/{id}/notes:
  post:
    tags:
      - Admin Properties
    summary: Create a note for a property
    operationId: adminCreatePropertyNote
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              note:
                type: string
    responses:
      "201":
        description: Note created successfully
      "400":
        description: Note cannot be empty
