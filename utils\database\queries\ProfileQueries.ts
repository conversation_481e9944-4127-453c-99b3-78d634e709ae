import { TABLE } from "../table";

export const ProfileQueries = {
 
SELECT_PROFILE_WITH_LOGIN: `SELECT 
  -- Profile info
  p.id AS "profileId", 
  p."statusId" AS "profileStatusId", 
  p."accountType" AS "accountRole", 
  p."firstName",
  p."experience", 
  p."description",
  p."middleName",
  p."lastName",  
  p."locationId",
  p."address",
  p."email",
  p."phone", 
  p."statusId", 
  p."createdOn", 
  p."designation",
  p."shortDescription",
  p."description", 
  p."accountType",
  p."association", 
  p."profileImage",
  p."isProfileCompleted",
  -- Agent languages (array of objects with id, name, localName, code)
COALESCE(
  (
    SELECT json_agg(
      json_build_object(
        'id', lang.id,
        'name', lang.name,
        'localName', lang."localName",
        'code', lang.code
      )
    )
    FROM "${TABLE.AGENT_LANGUAGE}" al
    JOIN list.languages lang ON lang.id = al."languageId"
    WHERE al."profileId" = p.id
  ),
  '[]'
) AS languages,
    -- Agent specialization (array of strings)
     COALESCE(
      (
        SELECT json_agg(
          json_build_object(
            'id', aspec.id,
            'name', aspec."name"
          )
        )
        FROM agn.specialization aspec
        WHERE aspec."profileId" = p.id AND aspec."statusId" = 1
      ),
      '[]'
    ) AS specialization,

  -- Login info
  l.id AS "loginId",
  l."statusId" AS "loginStatusId",
  l."username", 
  l."lastLogin", 
  l."isActivated",

  -- Role info
  lr."roleId",
  r."name" AS "roleName",   
 
  -- Profile status + location
  s.name AS "profileStatus",
  lo."name" AS location,

  --  Properties count
  (
    SELECT COUNT(*) 
    FROM agn.properties ap 
    WHERE ap."agencyId" = p.id
  ) AS properties_count,

  --  Services count
  (
    SELECT COUNT(*) 
    FROM agn.services asv 
    WHERE asv."profileId" = p.id
  ) AS services_count,


 --  Agency name via referral
(
  SELECT ag."name"
  FROM agn.referrals rf
  JOIN agencies ag ON ag.id = rf."userId"   
  WHERE rf."email" = p."email"
  LIMIT 1
) AS "referralAgencyName",


  -- Agent details (only required fields)
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'emiratesId', x."emiratesId",
          'visa', x."visa",
          'passport', x."passport",
          'emiratesIdExpiry', x."emiratesIdExpiry",
          'nationality', x."nationality",
          'termsAgree', x."termsAgree",
          'accuracyConfirm', x."accuracyConfirm",
          'communicationConsent', x."communicationConsent",
          'gender', x."gender"
        )
      )
      FROM (
        SELECT DISTINCT 
          al."emiratesId",
          al."visa",
          al."passport",
          al."emiratesIdExpiry",
          al."nationality",
          al."termsAgree",
          al."accuracyConfirm",
          al."communicationConsent",
          al."gender"
        FROM ${TABLE.AGENT_DETAILS} al
        WHERE al."profile_id" = p.id
      ) x
    ),
    '[]'
  ) AS agentdetails,

   

  -- Industry missions (parent services: only id, name)
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', ls_main.id,
          'name', ls_main.name
        )
      )
      FROM (
        SELECT DISTINCT ls_main.id, ls_main.name
        FROM list.${TABLE.SERVICE} ls_main
        JOIN prf.${TABLE.SERVICE} s2 ON ls_main.id = s2."serviceId"
        WHERE s2."profileId" = p.id AND ls_main."parentId" IS NULL
      ) ls_main
    ),
    '[]'
  ) AS industry_mission,

  -- Industry subcategories (child services: only id, name)
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'id', ls_sub.id,
          'name', ls_sub.name
        )
      )
      FROM (
        SELECT DISTINCT ls_sub.id, ls_sub.name
        FROM list.${TABLE.SERVICE} ls_sub
        JOIN prf.${TABLE.SERVICE} s2 ON ls_sub.id = s2."serviceId"
        WHERE s2."profileId" = p.id AND ls_sub."parentId" IS NOT NULL
      ) ls_sub
    ),
    '[]'
  ) AS industry_subcategory,

  -- Agent licenses (only selected fields)
  COALESCE(
    (
      SELECT json_agg(
        json_build_object(
          'roletype', al1."roletype",
          'licenseFile', al1."licenseFile",
          'licenseNumber', al1."licenseNumber",
          'hasLicense', al1."hasLicense",
          'licenseexpiryDate', al1."licenseexpiryDate"
        )
      )
      FROM (
        SELECT DISTINCT 
          al1."roletype",
          al1."licenseFile",
          al1."licenseNumber",
          al1."hasLicense",
          al1."licenseexpiryDate"
        FROM agentlicenses al1 
        WHERE al1."agentId" = p.id
      ) al1
    ),
    '[]'
  ) AS agentlicenses,

  -- Subscription JSON
(
  SELECT json_build_object(
    'id', sub.id,
    'packageTypeId', pt.id,
    'packageName', pt.name,
    'userType', pt."userType",
    'price', pt.price,
    'currency', pt.currency
  )
  FROM list.subscription sub
  LEFT JOIN look.packagetype pt ON pt.id = sub."packageTypeId"
  WHERE sub."profileId" = p.id 
    AND sub."statusId" = 1
  LIMIT 1
) AS subscription

FROM "${TABLE.PROFILE_TABLE}" p
LEFT JOIN "${TABLE.LOGIN_TABLE}" l ON p.id = l."profileId"
LEFT JOIN "${TABLE.STATUSES}" s ON p."statusId" = s.id
LEFT JOIN "${TABLE.LOGIN_ROLE}" lr ON l.id = lr."loginId"
LEFT JOIN "${TABLE.ROLES}" r ON r.id = lr."roleId"
LEFT JOIN list."${TABLE.LOCATION}" lo ON lo.id = p."locationId"

WHERE p.id = $1
GROUP BY 
  p.id, l.id, lr."roleId", lr."statusId", r."name", s.name, lo."name";

`,
 
    UPDATE_PROFILE_BASIC: `
      UPDATE "${TABLE.PROFILE_TABLE}"
      SET
        "profileImage" = $1,
        "firstName" = $2,
        "middleName" = $3,
        "lastName" = $4
      WHERE id = $5
      RETURNING *;

    `,
    UPDATE_PROFILE_GENDER: `
    
      UPDATE ${TABLE.AGENT_DETAILS}
      SET
        "nationality" = $1,
        "gender" = $2
      WHERE "profile_id" = $3
      RETURNING *;

    `,
  
  UPDATE_PROFILE_CONTACT: `
    UPDATE "${TABLE.PROFILE_TABLE}"
    SET
      "locationId" = $1,
      "address" = $2
    WHERE id = $3
    RETURNING *;
  `,

    GET_PROFILE_PASSWORD: `
    SELECT "passwordHash"
    FROM "${TABLE.LOGIN_TABLE}"
    WHERE "profileId" = $1;
  `,
  UPDATE_PROFILE_PASSWORD: `
    UPDATE "${TABLE.LOGIN_TABLE}"
    SET
      "passwordHash" = $1
    WHERE "profileId" = $2
    RETURNING *;
  `,

  UPDATE_PROFILE_STATUS: `
    UPDATE "${TABLE.PROFILE_TABLE}"
    SET
      "statusId" = $1
    WHERE id = $2
    RETURNING *;
  `,
  UPDATE_PROFILE_PROFESSIONAL_INFO: `
    UPDATE "${TABLE.PROFILE_TABLE}"
    SET
      "experience" = $1,
      "description" = $2
    WHERE "id" = $3
    RETURNING *;
  `,
  DELETE_AGENT_LANGUAGE: `
    DELETE FROM "${TABLE.AGENT_LANGUAGE}"
    WHERE "profileId" = $1;
  `,
  ADD_UPDATE_PROFILE_LANGUAGES: `
  INSERT INTO "${TABLE.AGENT_LANGUAGE}" ("languageId", "profileId")
  VALUES ($1, $2);
  `,
  DELETE_AGENT_SPECIALIZATION: `
    DELETE FROM "${TABLE.AGENT_SPECIALIZATION}"
    WHERE "profileId" = $1;
  `,
  ADD_UPDATE_PROFILE_SPECIALIZATION: `
  INSERT INTO "${TABLE.AGENT_SPECIALIZATION}" ("name", "profileId")
  VALUES ($1, $2);
  `,
  SET_SPECIALIZATION_STATUS: `
  UPDATE "${TABLE.AGENT_SPECIALIZATION}"
  SET "statusId" = $1
  WHERE "name" = $2 AND "profileId" = $3
  RETURNING *;
  `,
  SELECT_AGENT_SPECIALIZATION: `
  SELECT "name"
  FROM "${TABLE.AGENT_SPECIALIZATION}"
  WHERE "profileId" = $1;
`,
FETCH_ALL_AGENT_SPECIALIZATION:`SELECT "name", "statusId" FROM "${TABLE.AGENT_SPECIALIZATION}" WHERE "profileId" = $1`
}