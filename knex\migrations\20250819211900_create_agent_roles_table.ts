import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").createTable("agentRoles", (table) => {
    table.increments("id").primary();
    table.integer("profileId").notNullable();
    table.integer("serviceId").notNullable();
    table.timestamp("createdOn", { useTz: true }).defaultTo(knex.fn.now());
    table.integer("createdBy").notNullable();

    table.foreign("profileId").references("id").inTable("prf.profile").onDelete("CASCADE");
    table.foreign("serviceId").references("id").inTable("list.services").onDelete("CASCADE");
    table.foreign("createdBy").references("id").inTable("sec.login").onDelete("CASCADE");
  });

  await knex.schema.raw(`
    CREATE UNIQUE INDEX idx_agentRoles_profile_service
    ON agn."agentRoles"("profileId", "serviceId");
  `);
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.raw(
    `DROP INDEX IF EXISTS agn.idx_agentRoles_profile_service`
  );

  await knex.schema.withSchema("agn").dropTableIfExists("agentRoles");
}
