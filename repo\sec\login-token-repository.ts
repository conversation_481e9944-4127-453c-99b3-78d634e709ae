import { drizzleDb } from "../../config/database";
import { loginToken } from "../../db_schema/LoginToken";
import { eq } from "drizzle-orm";

export class LoginTokenRepository {

  async removeByUserId(userId: number) {
    try {
      return await drizzleDb.delete(loginToken).where(eq(loginToken.userId, userId));
    } catch (error) {
      console.error('Unable to remove login token from table logintable.', error);
      throw error;
    }
  }

}