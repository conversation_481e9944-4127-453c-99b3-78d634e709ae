import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { loginSchema } from "../../../utils/validations/auth.validation";
import { errorCatchResponse, errorResponse, response, responseData } from "../../../utils/response";
import { db } from "../../../config/database";
import bcrypt from "bcryptjs";
import { getSignedJwt, verifyToken } from "../../../utils/services/jwt";
import { upsertToken } from "../../../utils/helperFunctions/upsertToken";
import { AUTH } from "../../../utils/database/queries/auth";
import { JwtPayload } from "jsonwebtoken";
import { AuthService } from "../../../service/AuthService";

const baseUrl = process.env.BASE_URL?.replace(/\/+$/, "");

export const adminLogin = asyncHandler(async (req: Request, res: Response) => {
    try {
        let result;
        let resultUserName;
        const { email, password } = req.body;
        const { success, error } = loginSchema.safeParse({ email, password });
        if (!success) {
            return errorResponse(res, error?.issues[0].message);
        }
        result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);
        if (result.rows.length === 0) {
            resultUserName = await db.query(AUTH.SELECT_BY_USERNAME_FROM_PROFILE, [
                email,
            ]);
            if (resultUserName.rows.length === 0) {
                return errorResponse(res, "Invalid username or email.");
            } else {
                result = await db.query(AUTH.SELECT_BY_ID, [
                    resultUserName?.rows[0].profileId,
                ]);
            }
        }

        if (result.rows.length > 0) {
            const user = result.rows[0];

            const loginResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
                user.id,
            ]);
            if (loginResult.rows.length > 0) {
                const login = loginResult.rows[0];

                if (login.passwordHash) {
                    if (await bcrypt.compare(password, login.passwordHash)) {
                        if (login.isActivated) {
                            const userRole = await db.query(AUTH.SELECT_LOGIN_ROLE_BY_ID, [
                                login.id,
                            ]);

                            if (userRole.rows.length > 0) {
                                if (userRole.rows[0].name != "admin" && userRole.rows[0].name != "superAdmin") {
                                    errorResponse(res, "You are not authorized to access this resource.");
                                    return;
                                }
                                login.role = userRole.rows[0].name;
                            } else {
                                errorResponse(res, "You are not authorized to access this resource.");
                                return;
                            }

                            const token = getSignedJwt(login.id, email);

                            res.cookie("adminAuthToken", token, {
                                httpOnly: true,
                                secure: process.env.NODE_ENV === "production",
                                sameSite: "strict",
                                maxAge:
                                    (Number(process.env.COOKIE_EXPIRY) || 7) *
                                    24 *
                                    60 *
                                    60 *
                                    1000,
                            });
                            await upsertToken(login.id, token);
                            
                            const result = await db.query(AUTH.UPDATE_INTO_LOGIN_LAST_LOGIN, [login.id]);
                            if (result.rows.length > 0) {
                              console.log("lastLogin updated successfully");
                            } else {
                              console.log("Failed to update lastLogin");
                            }

                            delete login.passwordHash;
                            user.user_id = user.id;
                            delete user.id;
                            login.login_id = login.id;
                            delete login.id;

                            if (user.profileImage) {
                                user.profileImage = baseUrl + user.profileImage;
                            }

                            const data = { ...user, ...login };

                            return responseData(res, 200, "Login successful", data);
                        } else {
                            return errorResponse(
                                res,
                                "Your account is not activated. Please check your email for the activation link."
                            );
                        }
                    } else {
                        return errorResponse(res, "Invalid email or password");
                    }
                } else {
                    return errorResponse(res, "You have not set password yet.");
                }
            } else {
                return errorResponse(res, "Invalid credentials. Please try again.");
            }
        } else {
            return errorResponse(res, "Invalid credentials. Please try again.");
        }
    } catch (error) {
        console.error("Error logging in user:", error);
        return response(res, 500, "Failed to login user");
    }
});

export const verifyAdminUserToken = asyncHandler(
    async (req: Request, res: Response) => {
        try {
            let { id } = req.params;

            if (!id || isNaN(Number(id))) {
                return errorResponse(res, "User ID must be a valid number");
            }

            const userId = Number(id);
            if (!userId) {
                return errorResponse(res, "User ID parameter is required");
            }
            const token = req.cookies?.adminAuthToken;

            if (!token) {
                return errorResponse(res, "Invalid or expired token");
            }

            let decodedToken;
            try {
                decodedToken = verifyToken(token) as JwtPayload;
            } catch (err) {
                return errorResponse(res, "Invalid or expired token");
            }

            const loginId = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [userId]);

            const loginTokenResults = db.query(AUTH.SELECT_TOKEN_LOGIN, [token, loginId.rows[0].id])

            if((await loginTokenResults).rows.length == 0){
                return response(res, 401, "Unauthorized access. Please login first.");
            }

            const userRoleResult = await db.query(AUTH.SELECT_LOGIN_ROLE_BY_ID, [
                loginId.rows[0].id,
            ]);

            const userRoleData = userRoleResult.rows[0];
            // Fix: Logical condition should use AND (&&), not OR (||)
            if (userRoleData && userRoleData.name !== "superAdmin" && userRoleData.name !== "admin") {
                return response(res, 401, "Unauthorized access. Only admins are allowed.");
            }

            const isMatch = decodedToken.id === loginId.rows[0].id;

            if (!isMatch) {
                return errorResponse(res, "Invalid or expired token");
            }

            return response(res, 200, "Token verified successfully");
        } catch (error) {
            console.error("Error during token verification:", error);
            return errorCatchResponse(res, "Internal Server Error");
        }
    }
);

export const adminLogout = asyncHandler(async (req: Request, res: Response) => {
    try {
        const authService = new AuthService();

        await authService.removeTokenFromDatabase(Number(req.user.loginId))

        res.cookie("adminAuthToken", "", {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            sameSite: "strict",
            maxAge: 0,
        });
        
        return response(res, 200, "Logout successful");
    } catch (error) {
        console.error("Error during logout:", error);
        return errorCatchResponse(res, "Failed to log out.");
    }
});