import { z } from "zod";
import { zInt } from "./ServiceDTO";

/** LIST / table + cards (no change) */
export const ListServiceQuerySchema = z.object({
  agentId: zInt.optional(),
  search: z.string().trim().optional(),
  typeId: zInt.optional(),
  pricing: z.enum(["free", "paid", ""]).optional(),
  statusId: zInt.optional(),
  page: zInt.optional().default(1),
  limit: zInt.optional().default(10),
  isDeleted: z.boolean(),
  sort: z.enum(["newest"]).optional().default("newest"),
  locationId: zInt.optional(),
});

export type ListServiceQueryDTO = z.infer<typeof ListServiceQuerySchema>;
