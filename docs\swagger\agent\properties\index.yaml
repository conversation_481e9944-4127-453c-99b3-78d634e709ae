/agent/properties:
  get:
    tags:
      - Properties
    summary: Get filtered properties
    operationId: getFilteredProperties
    description: Fetch properties using filters like status, type, location, and listing type.
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: pageSize
        in: query
        schema:
          type: integer
          default: 10
      - name: status
        in: query
        schema:
          type: string
      - name: propertyTypeId
        in: query
        schema:
          type: integer
      - name: locationId
        in: query
        schema:
          type: integer
      - name: listingType
        in: query
        schema:
          type: integer
    responses:
      "200":
        description: Properties fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: object
                  properties:
                    properties:
                      type: array
                      items:
                        $ref: "#/components/schemas/Property"
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    statusCounts:
                      type: array
                      items:
                        $ref: "#/components/schemas/StatusCount"

  post:
    tags:
      - Properties
    summary: Create or update a property
    operationId: createOrUpdateProperty
    security:
      - cookieAuth: []
    requestBody:
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              id:
                type: string
                description: ID of the property (used for updates; leave empty to create new)
                default: ""
              # code:
              #   type: string
              #   description: Unique internal code for the property
              #   default: PROP-1235
              name:
                type: string
                description: Title or name of the property (used to generate slug)
                default: Luxury Downtown Apartment
              local:
                type: string
                description: Localized or alternate name for the property
                default: en
              propertyTypeId:
                type: integer
                description: ID of the property type (e.g., Apartment, Villa); value comes from the types table
                default: 55
              apartmentTypeId:
                type: integer
                description: ID of the apartment subtype (e.g., Penthouse, Studio); value comes from the types table
                default: 54
              totalRooms:
                type: integer
                description: Total number of rooms in the property
                default: 5
              locationId:
                type: integer
                description: ID of the property's location (e.g., area or city); value comes from the location table
                default: 3
              address:
                type: string
                description: Full address of the property
                default: 123 Palm Street, Downtown City
              currencyId:
                type: integer
                description: Currency ID used for pricing (e.g., USD, AED); value comes from the currency table
                default: 6
              price:
                type: number
                description: Price of the property
                default: 450000
              size:
                type: number
                description: Size of the property (in square feet/meters)
                default: 1350
              permitNo:
                type: string
                description: Government-issued permit number
                default: PERM-9876
              parking:
                type: boolean
                description: Whether the property has parking available
                default: true
              swimmingPools:
                type: boolean
                description: Whether the property includes swimming pools
                default: false
              gym:
                type: boolean
                description: Whether the property has a gym
                default: true
              startDate:
                type: string
                format: date
                description: Date when the property listing starts
                default: 2025-08-01
              expiryDate:
                type: string
                format: date
                description: Date when the listing expires
                default: 2025-12-31
              listingType:
                type: integer
                description: ID representing listing type (e.g., Sale, Rent); value comes from the types table
                default: 1
              completionStatus:
                type: integer
                description: Construction status (e.g., completed, under construction)
                default: 1
              ownershipTypeId:
                type: integer
                description: ID for ownership type (e.g., Freehold, Leasehold); value comes from the types table
                default: 2
              metaTitle:
                type: string
                description: SEO meta title for the property
                default: Modern Apartment in Downtown
              metaDescription:
                type: string
                description: SEO meta description for the property
                default: Spacious and modern 2-bedroom apartment with amenities in downtown area.
              bedrooms:
                type: integer
                description: Number of bedrooms
                default: 2
              bathrooms:
                type: integer
                description: Number of bathrooms
                default: 2
              furnished:
                type: boolean
                description: Whether the property is furnished
                default: true
              permitId:
                type: string
                description: Internal system permit ID (different from permit number)
                default: 987654321
              unitNo:
                type: string
                description: Unit or apartment number (if applicable)
                default: A-1204
              projectId:
                type: integer
                description: ID of the associated project (optional)
                default: 3001
              tagLine:
                type: string
                description: Marketing tagline or short slogan for the property
                default: Live Luxuriously in the Heart of the City
              features:
                type: string
                description: Comma-separated list of features (e.g., "Sea View, Balcony")
                default: near hospital, airport
              propertyPhotos:
                type: array
                items:
                  type: string
                  format: binary
                description: Upload one or more property photos
              govtIssuedQr:
                type: string
                format: binary
                description: Upload government-issued QR code for property verification
    responses:
      "201":
        description: Property created or updated
      "400":
        description: Bad Request

/agent/properties/{id}:
  get:
    tags:
      - Properties
    summary: Get property by ID
    operationId: getPropertyById
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Property found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Property"
      "404":
        description: Property not found

  delete:
    tags:
      - Properties
    summary: Delete a property
    operationId: deleteProperty
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "204":
        description: Deleted successfully
      "404":
        description: Property not found

/agent/properties/{id}/status:
  put:
    tags:
      - Properties
    summary: Update property status
    operationId: updatePropertyStatus
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              status:
                type: string
    responses:
      "200":
        description: Status updated

/agent/properties/{id}/flag:
  put:
    tags:
      - Properties
    summary: Toggle a flag (isFeatured or isVerified)
    operationId: togglePropertyFlag
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              column:
                type: string
                enum: [isFeatured, isVerified]
    responses:
      "200":
        description: Flag toggled

/agent/properties/{id}/photos:
  patch:
    tags:
      - Properties
    summary: Update property photos
    operationId: updatePropertyPhotos
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              photoIdsToDelete:
                type: string
              propertyPhotos:
                type: array
                items:
                  type: string
                  format: binary
    responses:
      "200":
        description: Photos updated

components:
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: authToken
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Property:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        price:
          type: string
        size:
          type: string
        location_id:
          type: integer
        property_type_id:
          type: integer
        listing_type:
          type: integer
        status_id:
          type: integer
        status_name:
          type: string
        is_featured:
          type: boolean
        is_verified:
          type: boolean
        expiry_date:
          type: string
          format: date-time
        slug:
          type: string
        meta_title:
          type: string
        images:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              url:
                type: string

    Pagination:
      type: object
      properties:
        total:
          type: integer
        totalPages:
          type: integer
        currentPage:
          type: integer
        perPage:
          type: integer

    StatusCount:
      type: object
      properties:
        status_id:
          type: integer
        status_name:
          type: string
        count:
          type: integer
