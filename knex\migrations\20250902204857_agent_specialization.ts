import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").createTable("agentspecialization", (table) => {
    table.increments("id").primary();
    table.bigInteger("profileId").notNullable();
    table.text("specialization").notNullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").dropTableIfExists("agentspecialization");
}

