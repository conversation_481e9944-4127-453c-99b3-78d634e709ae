import { db } from "../../config/database";
import { DbFilterParams } from "../../dto/payment/DbFilterParamsDTO";
import { PaymentQueries } from "../../utils/database/queries/PaymentQueries";
import { AUTH } from "../../utils/database/queries/auth";
import { TABLE } from "../../utils/database/table";

export class PaymentRepository {
  // --- simple resolvers ---
  async findStatusIdByName(name: string | null): Promise<number | null> {
    if (!name) return null;
    const { rows } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [name]
    );
    return rows[0]?.id ?? null;
  }

  async getActivatedAccountStatusId(): Promise<number> {
    const statusNames = ["Activated"];
    const { rows } = await db.query(AUTH.SELECT_ACCOUNT_STATUS(statusNames), statusNames);
    return rows[0]?.id;
  }

  // --- lists (payments) ---
  async fetchPayments(p: DbFilterParams) {
    const { rows } = await db.query(
      PaymentQueries.GET_FILTERED_PAYMENTS(p),
      PaymentQueries.GET_FILTERED_PAYMENTS_PARAMS(p)
    );
    return rows;
  }

  async fetchPaymentStatusCounts(p: DbFilterParams) {
    const { rows } = await db.query(
      PaymentQueries.GET_PAYMENT_STATUS_COUNTS(p),
      PaymentQueries.GET_PAYMENT_STATUS_COUNTS_PARAMS(p)
    );
    return rows;
  }

  // --- detail ---
  async fetchPaymentDetailById(id: number) {
    const { rows } = await db.query(PaymentQueries.GET_PAYMENT_DETAIL_BY_ID, [id]);
    return rows[0] || null;
  }

  // --- filter options ---
  async fetchPlans() {
    const { rows } = await db.query(PaymentQueries.GET_PLANS);
    return rows;
  }
  async fetchPaymentTypes() {
    const { rows } = await db.query(PaymentQueries.GET_PAYMENT_TYPES);
    return rows;
  }
  async fetchListingTypes() {
    const { rows } = await db.query(PaymentQueries.GET_LISTING_TYPES);
    return rows;
  }

  // --- invoices ---
  async fetchInvoices(p: DbFilterParams) {
    const { rows } = await db.query(
      PaymentQueries.GET_FILTERED_INVOICES(p),
      PaymentQueries.GET_FILTERED_PAYMENTS_PARAMS(p) // same param order
    );
    return rows;
  }

  async fetchInvoiceStatusCounts(p: DbFilterParams) {
    const { rows } = await db.query(
      PaymentQueries.GET_INVOICES_STATUS_COUNTS(p),
      PaymentQueries.GET_PAYMENT_STATUS_COUNTS_PARAMS(p)
    );
    return rows;
  }

  // --- subscriptions ---
  async fetchActiveSubscriptionByProfile(profileId: number, activeStatusId: number) {
    const { rows } = await db.query(PaymentQueries.GET_ACTIVE_SUBSCRIPTION_BY_PROFILE, [
      profileId,
      activeStatusId,
    ]);
    return rows[0] || null;
  }
}
