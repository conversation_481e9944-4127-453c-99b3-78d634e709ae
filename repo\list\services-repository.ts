import { sql } from "drizzle-orm";
import { PoolClient } from "pg";
import { drizzleDb } from "../../config/database";

export class ServicesRepository {

    async getServiceByName(industryName: string) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM list.services WHERE name = ${industryName}`
        );
        return result;
    }

    async addService(
        industryName: string,
        desc: string,
        parentId: string | null,
        typeId: number,
        statusId: number,
        createdBy: number,
        isCustom: boolean,
        group: string | null,
        dbClient: PoolClient
    ) {
        const result = await dbClient.query(
            `INSERT INTO list.services (
            name, 
            description, 
            "parentId", 
            "typeId", 
            "statusId", 
            "createdBy",
            "isCustom",
            "group"
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id`,
            [industryName, desc, parentId, typeId, statusId, createdBy, isCustom, group]
        );
        return result;
    }

    async getServiceById(serviceId: number) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM list."services" WHERE id = ${serviceId}`
        );
        return result;
    }

}