import { db } from "../../config/database";
import { CreateServiceDTO } from "../../dto/service/ServiceDTO";
import { ServiceQueries } from "../../utils/database/queries/ServiceQueries";
import { PoolClient } from "pg";
import { deleteFileFromS3 } from "../../utils/services/s3-bucket";
import { TYPE } from "../../utils/database/queries/type";
import { StatusService } from "../../service/status/StatusService";
import { ListServiceQueryDTO } from "../../dto/service/ListServiceQueryDTO";
import { UpdateServiceDTO } from "../../dto/service/UpdateServiceDTO";
import { PhotoPayloadDTO } from "../../dto/service/PhotoPayloadDTO";
import { REASONSQUERIES } from "../../utils/database/queries/ReasonQueries";

export class ServiceRepository {
  private statusService = new StatusService();

  async list(q: ListServiceQueryDTO) {
    const {
      page = 1,
      limit = 10,
      search,
      pricing,
      statusId,
      sort,
      agentId,
      isDeleted,
    } = q;
    const offset = (page - 1) * limit;

    const filters: string[] = [`s."createdBy" = $1`];
    const params: any[] = [agentId];
    let idx = params.length;

    if (search) {
      params.push(`%${search.trim()}%`);
      filters.push(
        `(LOWER(s."title") LIKE LOWER($${++idx}) OR LOWER(COALESCE(s."description",''))
           LIKE LOWER($${idx}))`
      );
    }
    if (pricing === "free") filters.push(`COALESCE(s."isFree", false) = true`);
    if (pricing === "paid") filters.push(`COALESCE(s."isFree", false) = false`);
    if (statusId) {
      params.push(statusId);
      filters.push(`s."statusId" = $${++idx}`);
    }

    filters.push(`COALESCE(s."isDeleted", false) = ${isDeleted}`);

    const where = filters.length ? `WHERE ${filters.join(" AND ")}` : "";
    const orderBy = `ORDER BY s."createdOn" DESC`; // simple + fast

    const listSql = ServiceQueries.LIST(where, orderBy, limit, offset);

    const [rows] = await Promise.all([db.query(listSql, params)]);

    // header cards
    const countsRes = await db.query(ServiceQueries.DASH_COUNTS, [agentId]);

    return {
      items: rows.rows,
      counts: countsRes.rows[0] || {
        available_listings: 0,
        active_listings: 0,
        total_views: 0,
        free_services: 0,
      },
      pagination: {
        page,
        limit,
        total: 0,
      },
    };
  }

  async listCounts(q: ListServiceQueryDTO) {
    const { search, pricing, statusId, agentId, isDeleted } = q;

    const filters: string[] = [`s."createdBy" = $1`];
    const params: any[] = [agentId];
    let idx = params.length;

    if (search) {
      params.push(`%${search.trim()}%`);
      filters.push(
        `(LOWER(s."title") LIKE LOWER($${++idx}) OR LOWER(COALESCE(s."description",''))
           LIKE LOWER($${idx}))`
      );
    }
    if (pricing === "free") filters.push(`COALESCE(s."isFree", false) = true`);
    if (pricing === "paid") filters.push(`COALESCE(s."isFree", false) = false`);
    if (statusId) {
      params.push(statusId);
      filters.push(`s."statusId" = $${++idx}`);
    }

    filters.push(`COALESCE(s."isDeleted", false) = ${isDeleted}`);

    const where = filters.length ? `WHERE ${filters.join(" AND ")}` : "";

    const countSql = ServiceQueries.LIST_COUNT(where);

    const [countRes] = await Promise.all([db.query(countSql, params)]);

    return countRes.rows[0];
  }

  async adminList(q: ListServiceQueryDTO) {
    const {
      page = 1,
      limit = 10,
      search,
      pricing,
      statusId,
      sort,
      isDeleted,
      typeId,
      locationId,
    } = q;
    const offset = (page - 1) * limit;

    const filters: string[] = [];
    const params: any[] = [];

    // --- Search filter (title OR description) ---
    if (search) {
      params.push(`%${search.trim()}%`);
      filters.push(
        `(LOWER(s."title") LIKE LOWER($${params.length}) OR LOWER(COALESCE(s."description", '')) LIKE LOWER($${params.length}))`
      );
    }

    // --- Pricing filter ---
    if (pricing === "free") {
      filters.push(`COALESCE(s."isFree", false) = true`);
    }
    if (pricing === "paid") {
      filters.push(`COALESCE(s."isFree", false) = false`);
    }

    if (typeId) {
      params.push(typeId);
      filters.push(`s."duration" = $${params.length}`);
    }

    // --- Status filter ---
    if (statusId) {
      params.push(statusId);
      filters.push(`s."statusId" = $${params.length}`);
    }

    // --- Soft delete filter ---
    filters.push(`COALESCE(s."isDeleted", false) = ${isDeleted}`);

    // --- SQL assembly ---
    const where = filters.length ? `WHERE ${filters.join(" AND ")}` : "";
    const orderBy = `ORDER BY s."createdOn" DESC`;

    const listSql = ServiceQueries.LIST(where, orderBy, limit, offset);

    // --- Run queries ---
    const [rows] = await Promise.all([db.query(listSql, params)]);

    // --- Admin dashboard counts (separate query, no params) ---
    const countsRes = await db.query(ServiceQueries.ADMIN_DASH_COUNTS);

    return {
      items: rows.rows,
      counts: countsRes.rows[0],
      pagination: {
        page,
        limit,
        total: 0,
      },
    };
  }

  async adminListCounts(q: ListServiceQueryDTO) {
    const { search, pricing, statusId, isDeleted } = q;

    const filters: string[] = [];
    const params: any[] = [];

    // --- Search filter (title OR description) ---
    if (search) {
      params.push(`%${search.trim()}%`);
      filters.push(
        `(LOWER(s."title") LIKE LOWER($${params.length}) OR LOWER(COALESCE(s."description", '')) LIKE LOWER($${params.length}))`
      );
    }

    // --- Pricing filter ---
    if (pricing === "free") {
      filters.push(`COALESCE(s."isFree", false) = true`);
    }
    if (pricing === "paid") {
      filters.push(`COALESCE(s."isFree", false) = false`);
    }

    // --- Status filter ---
    if (statusId) {
      params.push(statusId);
      filters.push(`s."statusId" = $${params.length}`);
    }

    // --- Soft delete filter ---
    filters.push(`COALESCE(s."isDeleted", false) = ${isDeleted}`);

    // --- SQL assembly ---
    const where = filters.length ? `WHERE ${filters.join(" AND ")}` : "";

    const countSql = ServiceQueries.LIST_COUNT(where);

    const [countRes] = await Promise.all([db.query(countSql, params)]);

    return countRes.rows[0];
  }

  async getById(id: number) {
    const res = await db.query(ServiceQueries.GET_BY_ID, [id]);
    return res.rows[0] || null;
  }

  async create(client: PoolClient, dto: CreateServiceDTO) {
    try {
      // create base service
      const res = await client.query(ServiceQueries.CREATE, [
        dto.profileId,
        dto.agencyId ?? null,
        dto.statusId,
        dto.createdBy,
        dto.title,
        dto.duration,
        dto.isRemote ?? false,
        dto.isFree ?? false,
        dto.specialOffer ?? null,
        dto.websiteUrl ?? null,
        dto.description ?? null,
        dto.price ?? null,
      ]);

      const serviceId = res.rows[0].id as number;

      const { rows: service } = await client.query(ServiceQueries.GET_BY_ID, [
        serviceId,
      ]);

      return service[0];
    } catch (err) {
      throw err;
    }
  }

  async createServiceTypes(
    client: PoolClient,
    serviceId: number,
    dto: CreateServiceDTO
  ) {
    try {
      // insert service types
      if (dto.serviceIds?.length) {
        await client.query(ServiceQueries.SERVICE_TYPES_BULK_INSERT, [
          serviceId,
          JSON.stringify(dto.serviceIds),
        ]);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  async createServiceLocations(
    client: PoolClient,
    serviceId: number,
    dto: CreateServiceDTO
  ) {
    try {
      // insert service locations
      if (dto.locationIds?.length) {
        await client.query(ServiceQueries.SERVICE_LOCATIONS_BULK_INSERT, [
          serviceId,
          JSON.stringify(dto.locationIds),
        ]);
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  async createServicePhotos(
    client: PoolClient,
    serviceId: number,
    files: PhotoPayloadDTO
  ) {
    try {
      // insert images
      if (files.photos?.length) {
        await client.query(ServiceQueries.PHOTOS_BULK_INSERT, [
          serviceId,
          files.profileId,
          files.statusId,
          files.createdBy,
          JSON.stringify(files.photos),
        ]);
      }
      return true;
    } catch (error) {
      throw error;
    }
  }

  async update(client: PoolClient, dto: UpdateServiceDTO) {
    try {
      await client.query(ServiceQueries.UPDATE, [
        dto.id,
        dto.profileId,
        dto.agencyId ?? null,
        dto.statusId,
        dto.modifiedBy ?? dto.createdBy,
        dto.title,
        dto.duration,
        dto.isRemote ?? false,
        dto.isFree ?? false,
        dto.specialOffer ?? null,
        dto.websiteUrl ?? null,
        dto.description ?? null,
        dto.price ?? null,
      ]);

      const { rows: service } = await db.query(ServiceQueries.GET_BY_ID, [
        dto.id,
      ]);
      return service[0];
    } catch (err) {
      throw err;
    }
  }

  async deleteExistingServiceTypes(client: PoolClient, serviceId: number) {
    try {
      // clear and re-insert service types
      await client.query(ServiceQueries.SERVICE_TYPES_DELETE_BY_SERVICE, [
        serviceId,
      ]);
      return true;
    } catch (error) {
      throw error;
    }
  }

  async deleteExistingServiceLocations(client: PoolClient, serviceId: number) {
    try {
      // clear and re-insert service locations
      await client.query(ServiceQueries.SERVICE_LOCATIONS_DELETE_BY_SERVICE, [
        serviceId,
      ]);
      return true;
    } catch (error) {
      throw error;
    }
  }

  async updateStatus(id: number, statusId: number) {
    await db.query(ServiceQueries.UPDATE_STATUS, [id, statusId]);
  }

  async delete(id: number) {
    await db.query(ServiceQueries.DELETE, [id]);
  }

  async updatePhotos(
    id: number,
    toAdd: string[],
    ctx: { profileId: number; statusId: number; createdBy: number }
  ) {
    if (toAdd?.length) {
      await db.query(ServiceQueries.PHOTOS_BULK_INSERT, [
        id,
        ctx.profileId,
        ctx.statusId,
        ctx.createdBy,
        JSON.stringify(toAdd),
      ]);
    }
    return this.getById(id);
  }

  async deletePhotoseByIds(removeIds: number[]) {
    if (removeIds?.length) {
      const { rows: imagesToUnlink } = await db.query(
        ServiceQueries.PHOTOS_SELECT_BY_ID,
        [removeIds]
      );

      await Promise.allSettled(
        imagesToUnlink.map((imageData) => deleteFileFromS3(imageData.url!))
      );

      await db.query(ServiceQueries.PHOTOS_DELETE_MANY, [removeIds]);
    }

    return true;
  }

  async userMissionsOrServices(id: number) {
    const { rows: industries } = await db.query(
      ServiceQueries.FIND_BY_PROFILE_WITH_SERVICE,
      [id]
    );

    return industries;
  }

  async getLocations() {
    const { rows: locations } = await db.query(TYPE.GET_ALL_LOCATIONS);
    return locations;
  }

  async getTypesByParentTypeName(typeName: string) {
    const { rows: durations } = await db.query(
      ServiceQueries.FIND_CHILD_TYPES_BY_PARENT,
      [typeName]
    );

    return durations;
  }

  async insertReason(data: {
    profileId: number;
    reason: string;
    createdBy: number;
    oldStatusId: number;
    newStatusId: number;
    isPrivate: boolean;
    module: string;
  }) {
    const res = await db.query(REASONSQUERIES.InsertReasonQuery, [
      data.profileId,
      data.reason,
      data.createdBy,
      data.oldStatusId,
      data.newStatusId,
      data.isPrivate,
      data.module,
    ]);
    return res.rows[0];
  }
}
