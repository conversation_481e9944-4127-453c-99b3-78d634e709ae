import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("blog_faqs", (table) => {
    table.increments("id").primary();
    table.bigInteger("blogId").notNullable();
    table.text("question").notNullable();
    table.text("answer").notNullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("blog_faqs");
}
