import { PoolClient, QueryResult } from "pg";
import { db } from "../../config/database";
import { IndividualCompleteProfileDto } from "../../dto/individual/individual-complete-profile";
import { CompanyProfileDto } from "../../dto/company-profile/company-profile";
import { AgentLicensesRepository } from "../../repo/agent-licenses-repository";
import { AUTH } from "../../utils/database/queries/auth";
import { REFERRALS } from "../../utils/database/queries/referrals";
import { TABLE } from "../../utils/database/table";
import { CollectionUtils } from "../../utils/helperFunctions/collectionUtils";
import { ImagesRepository } from "../../repo/prf/images-repository";
import { LicensesRepository } from "../../repo/agn/licenses-repository";
import { ReferralsRepository } from "../../repo/agn/referrals-repository";
import { LoginRepository } from "../../repo/sec/login-repository";
import { ProjectsService } from "../agn/projects-service";
import { PropertiesService } from "../agn/properties-service";
import { EventsService } from "../prf/events-service";
import { PrfServicesService } from "../prf/services-service";
import { AgentRolesRepository } from "../../repo/agn/agent-roles-repository";
import { ServicesRepository } from "../../repo/list/services-repository";
import { StatusRepository } from "../../repo/look/status-repository";
import { ProfileRepository } from "../../repo/prf/profile-repository";
import { AgenciesRepository } from "../../repo/agn/agencies-repostiory";
import { AgentDetailsRepository } from "../../repo/agn/agent-details-repository";

export class AgentService {

    private static prfService = new PrfServicesService();
    private static eventService = new EventsService();
    private static propertiesService = new PropertiesService();
    private static projectsService = new ProjectsService();

    private static servicesRepo = new ServicesRepository();
    private static statusRepo = new StatusRepository();
    private static agentRoleRepo = new AgentRolesRepository();
    private static profileRepo = new ProfileRepository();
    private static agentLicensesRepo = new AgentLicensesRepository();
    private static imagesRepo = new ImagesRepository();
    private static licensesRepo = new LicensesRepository();
    private static referralsRepo = new ReferralsRepository();
    private static loginRepo = new LoginRepository();
    private static agentDetailsRepo = new AgentDetailsRepository();
    private static agenciesRepo = new AgenciesRepository();


    static async processIndividualVerification(individualProfileDto: IndividualCompleteProfileDto, userId: string) {
        const client = await db.connect();
        await client.query("BEGIN");

        try {
            const { allImages } = getAllDocsFromInput(individualProfileDto);
            const profileId = Number(userId);
            const loginResult = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
                profileId,
            ]);

            const otherIndustry = await this.servicesRepo.getServiceByName('Other');

            const loginId = loginResult.rows[0].id;
            const industryOtherId = String(otherIndustry.rows[0].id);
            const draftStatusId = await this.statusRepo.getStatusIdByStatusName('Draft');

            const documents = individualProfileDto.documents;
            const industries = individualProfileDto.industries;

            const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);

            const existingAgent = agentProfile.rows[0];
            if (!existingAgent) {
                await client.query("ROLLBACK");
                return { success: false, message: "Agent profile does not exist" };
            }

            const updateAgentDetailsValues: Record<string, unknown> = {};

            const trimAndUpdateItem = (key: any, value: any) => {
                if (value) updateAgentDetailsValues[key] = value.trim();
            };

            trimAndUpdateItem("nationality", individualProfileDto.nationality);
            trimAndUpdateItem("gender", individualProfileDto.gender);
            trimAndUpdateItem("emiratesIdExpiry", documents.emiratesIdExpiry)
            trimAndUpdateItem("visaExpiry", documents.visaExpiry)

            if (industries) {
                const primaryIndustryIds = individualProfileDto.industries
                    .filter((ind: { industryId: string; }) => ind.industryId !== industryOtherId)
                    .map((ind: { industryId: any; }) => ind.industryId);

                if (Array.isArray(primaryIndustryIds)) {
                    updateAgentDetailsValues.industryMission = primaryIndustryIds.join(', ');
                } else {
                    updateAgentDetailsValues.industryMission = primaryIndustryIds;
                }
            }

            const industryMissionOther = industries.find((ind: any) => ind.industryId === industryOtherId);
            if (industryMissionOther) {
                const roleNamesForOtherIndustries = industries
                    .filter((industry: { industryId: string; }) => industry.industryId === industryOtherId)
                    .map((industry: { roles: any[]; }) => industry.roles.map((role: { roleName: any; }) => role.roleName))
                    .flat()
                    .join(", ");

                const otherIndustryNames = industries
                    .filter((industry: { industryId: string; }) => industry.industryId === industryOtherId)
                    .map((industry: { industryName: any; }) => industry.industryName)
                    .join(", ") || null;

                updateAgentDetailsValues.industrySubCategory = otherIndustryNames;
                updateAgentDetailsValues.industryMissionOther = roleNamesForOtherIndustries;
                updateAgentDetailsValues.agentRole = roleNamesForOtherIndustries;

            } else {
                const roleIdsForPrimaryIndustries = industries
                    .filter((industry: { industryId: string; }) => industry.industryId !== industryOtherId)
                    .map((industry: { roles: any[]; }) => industry.roles.map((role: { roleId: any; }) => role.roleId))
                    .flat()
                    .join(", ")

                updateAgentDetailsValues.agentRole = roleIdsForPrimaryIndustries;
            }

            if (individualProfileDto.documents.visa && individualProfileDto.documents.visa.length !== 0)
                updateAgentDetailsValues.visa = CollectionUtils.convertToArray(documents.visa);

            if (documents?.passport && documents.passport.length != 0)
                updateAgentDetailsValues.passport = CollectionUtils.convertToArray(documents.passport);

            if (documents?.emiratesId && documents.emiratesId.length != 0)
                updateAgentDetailsValues.emiratesId = CollectionUtils.convertToArray(documents.emiratesId);

            if (individualProfileDto.termsAgree)
                updateAgentDetailsValues.termsAgree = individualProfileDto.termsAgree;

            if (individualProfileDto.accuracyConfirm)
                updateAgentDetailsValues.accuracyConfirm = individualProfileDto.accuracyConfirm;

            if (individualProfileDto.communicationConsent)
                updateAgentDetailsValues.communicationConsent = individualProfileDto.communicationConsent;

            const keys = Object.keys(updateAgentDetailsValues);
            if (keys.length === 0) {
                await client.query("ROLLBACK");
                return { success: false, message: "No valid fields to update" };
            }

            // ADDING ROLES & INDUSTRIES TO LIST.SERVICES IF NOT EXIST
            let agentRoleIds = [];
            let allAgentRoles = [];
            for (const industry of industries) {
                const industryName = industry.industryName;

                const industryId: string = await AgentService.insertIndustryIfNotExist(industryName, loginId, draftStatusId, null, client);

                for (const role of industry.roles) {
                    const roleName = role.roleName;
                    const existingService = await this.servicesRepo.getServiceByName(roleName)

                    let roleId: number;

                    if (existingService.rows.length > 0) {
                        roleId = existingService.rows[0].id as number;
                    } else {
                        const insertResult = await this.servicesRepo.addService(
                            role.roleName,
                            role.roleName,
                            industryId,
                            2,
                            draftStatusId,
                            loginId,
                            true,
                            null,
                            client
                        )
                        roleId = insertResult.rows[0].id as number;
                    }
                    agentRoleIds.push(String(roleId));
                    role.roleId = String(roleId);
                    allAgentRoles.push(role);
                }
            }

            processAgentRolesData(agentRoleIds, profileId, loginId);

            const statusNames = ["Pending"];
            const pendingStatus = await client.query(
                AUTH.SELECT_ACCOUNT_STATUS(statusNames),
                statusNames
            );

            const statusId = pendingStatus.rows[0].id;

            this.profileRepo.updateProfile(
                individualProfileDto.firstName,
                individualProfileDto.middleName ? individualProfileDto.middleName : null,
                individualProfileDto.lastName,
                individualProfileDto.phoneNumber,
                documents.emiratesId ? CollectionUtils.convertToArray(documents.emiratesId) : [],
                statusId,
                individualProfileDto.profilePhoto,
                Number(individualProfileDto.location),
                profileId,
                client
            );

            const values = keys.map((k) => updateAgentDetailsValues[k]);
            const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

            // Try update first
            const result = await client.query(
                `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${keys.length + 1
                } RETURNING *`,
                [...values, profileId]
            );

            if (!result.rows[0]) {
                // No row updated, insert new
                let columnNames = keys.map((k) => `"${k}"`).join(", ");
                let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
                columnNames += `, "profile_id"`;
                placeholders += `, $${keys.length + 1}`;
                values.push(profileId);
                const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
                await client.query(insertQuery, values);
            }

            await client.query(AUTH.UPDATE_PROFILE_STATUS_COMPLETED, [
                true,
                profileId,
            ]);

            const updatedData = await client.query(AUTH.SELECT_BY_ID, [profileId]);

            const updatedAgentData = updatedData.rows[0];

            // Remove domain from URLs
            const mediaNames = allImages
                .map((url: string) => url?.replace(/^https?:\/\/[^/]+\//, ""))
                .filter(Boolean);

            if (mediaNames.length > 0) {
                const placeholders = mediaNames.map((_: any, i: number) => `$${i + 2}`).join(", ");

                const query = `
                  UPDATE prf.images
                  SET "isFinal" = true
                  WHERE "profileId" = $1
                  AND "url" IN (${placeholders});
                `;

                const values = [profileId, ...mediaNames];

                await client.query(query, values);
            }

            if (allAgentRoles?.length > 0) {
                for (const item of allAgentRoles) {
                    let {
                        roleId,
                        roleName,
                        hasLicense,
                        licenseDetails
                    } = item;

                    console.log(roleId)

                    const licenseDocs = licenseDetails && licenseDetails?.licenseDocs && licenseDetails.licenseDocs.length > 0 ?
                        CollectionUtils.convertToArray(licenseDetails.licenseDocs) : null

                    await this.agentLicensesRepo.addAgentLicense(
                        profileId,
                        Number(roleId),
                        roleName,
                        hasLicense,
                        licenseDetails?.licenseNumber || null,
                        licenseDetails?.licenseExpiryDate || null,
                        licenseDocs,
                        client
                    )

                    if (licenseDocs && licenseDocs.length > 0) {
                        interface LicenseFilePlaceholders {
                            placeholders: string;
                        }

                        const licenseFilePlaceholders: LicenseFilePlaceholders = {
                            placeholders: licenseDocs
                                .map((_: string, index: number) => `$${index + 2}`)
                                .join(", "),
                        };

                        const placeholders: string = licenseFilePlaceholders.placeholders;
                        const values = [profileId, ...licenseDocs];

                        const updateQuery = `
                      UPDATE prf.images
                      SET "isFinal" = true
                      WHERE "profileId" = $1
                      AND "url" IN (${placeholders});
                    `;

                        await client.query(updateQuery, values);
                    }
                }
            }


            await client.query("COMMIT");
            return {
                success: true,
                message: "Profile updated successfully",
                data: updatedAgentData,
            };
        } catch (error) {
            await client.query("ROLLBACK");
            console.error("Error updating agent profile:", error);
            return { success: false, message: "An error occurred while updating agent profile" };
        }

        function processAgentRolesData(this: any, agentRoleIds: string[], profileId: number, loginId: any) {
            // ADDING ALL AGENT ROLE IDs TO AGN.agentRoles
            if (agentRoleIds.length > 0) {
                for (const roleId of agentRoleIds) {
                    const subCategoryId = Number(roleId);
                    if (!isNaN(subCategoryId)) {
                        if (subCategoryId) {
                            AgentService.agentRoleRepo.addAgentRole(profileId, subCategoryId, loginId, client);
                        }
                    }
                }
            }
        }

        function getAllDocsFromInput(individualProfileDto: IndividualCompleteProfileDto) {
            const documents = individualProfileDto.documents;

            const licenseDocs: never[] = [];
            const emiratesId = [documents.emiratesId];
            const visa = documents.visa ? [documents.visa] : [];
            const passport = documents.passport ? [documents.passport] : [];
            const profilePhotoArray = individualProfileDto.profilePhoto ? [individualProfileDto.profilePhoto] : [];

            const industriesDocs = individualProfileDto.industries
                .map((industry: { roles: any; }) => industry.roles)
                .flat()
                .filter((role: { hasLicense: any; }) => role.hasLicense)
                .map((role: { licenseDetails: { licenseDocs: any; }; }) => role.licenseDetails.licenseDocs);

            const allImages = [
                ...licenseDocs,
                ...emiratesId,
                ...visa,
                ...passport,
                ...profilePhotoArray,
                ...industriesDocs,
            ].flat();

            return {
                licenseDocs,
                visa,
                passport,
                emiratesId,
                profilePhoto: profilePhotoArray,
                industriesDocs,
                allImages,
            };
        }
    }

    static async processCompanyProfileVerification(companyProfileDto: CompanyProfileDto, userId: string) {
        const client = await db.connect();
        await client.query("BEGIN");

        try {
            const { allImages } = getAllImages();

            const profileId = Number(userId);
            const loginResult = await this.loginRepo.getLoginByProfileId(profileId, client);
            const loginId = loginResult.rows[0].id;
            const agentProfile = await this.profileRepo.getProfileById(profileId, client)
            const existingAgent = agentProfile.rows[0];

            if (!existingAgent) {
                await client.query("ROLLBACK");
                return { success: false, message: "Company profile does not exist" };
            }

            const otherIndustry = await this.servicesRepo.getServiceByName('Other');
            const draftStatusId = await this.statusRepo.getStatusIdByStatusName('Draft');
            const industryOtherId = String(otherIndustry.rows[0].id);

            const companyIndustries = companyProfileDto.industries;

            const updateFields: Record<string, any> = extractDataToSaveInAgentDetailsTable(companyIndustries, industryOtherId);

            const keys = Object.keys(updateFields);
            if (keys.length === 0) {
                await client.query("ROLLBACK");
                return { success: false, message: "No valid fields to update" };
            }

            const values = keys.map((k) => updateFields[k]);
            const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

            // Try update first
            const result = await client.query(
                `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${keys.length + 1
                } RETURNING *`,
                [...values, profileId]
            );

            if (!result.rows[0]) {
                // No row updated, insert new
                let columnNames = keys.map((k) => `"${k}"`).join(", ");
                let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
                columnNames += `, "profile_id"`;
                placeholders += `, $${keys.length + 1}`;
                values.push(profileId);
                const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
                await client.query(insertQuery, values);
            }

            const statusNames = ["Pending"];
            const pendingStatus = await this.statusRepo.getStatusesByStatusNames(statusNames, client);

            const statusId = pendingStatus.rows[0].id;

            const profileValues = [
                companyProfileDto.companyPhone,
                companyProfileDto.emiratesId ? CollectionUtils.convertToArray(companyProfileDto.emiratesId) : null,
                statusId,
                companyProfileDto.profilePhoto,
                profileId,
            ];

            this.profileRepo.updateCompanyProfile(profileValues, client);

            const agencyData = {
                name: companyProfileDto.companyName,
                companyPhone: companyProfileDto.companyPhone,
                inviteAgents: companyProfileDto.inviteAgents ? companyProfileDto.inviteAgents.join(",") : null,
                passportDoc: companyProfileDto.passport ? CollectionUtils.convertToArray(companyProfileDto.passport) : null,
                visaDoc: companyProfileDto.visa ? CollectionUtils.convertToArray(companyProfileDto.visa) : null,
                supportingDocsDoc: companyProfileDto.supportingDocs ? CollectionUtils.convertToArray(companyProfileDto.supportingDocs) : null,
                profileId,
                statusId: 2,
                createdBy: loginId,
            };

            // Clean out undefined fields
            const entries = Object.entries(agencyData).filter(
                ([_, v]) => v !== undefined
            );

            const columns = entries.map(([key]) => `"${key}"`);
            const companyValues = entries.map(([_, value]) => value);
            const placeholders = entries.map((_, i) => `$${i + 1}`);
            const insertQuery = `INSERT INTO ${TABLE.AGENCIES} (${columns.join(", ")}) VALUES (${placeholders.join(", ")}) RETURNING *`;

            const agencyResult = await client.query(insertQuery, companyValues);
            const savedAgencyData = agencyResult.rows[0];

            // ADDING ROLES & INDUSTRIES TO LIST.SERVICES IF NOT EXIST
            const agencyRoleIds = await insertRolesAndIndustriesIfNotExist(companyIndustries, loginId, draftStatusId);

            processAgentRolesData(agencyRoleIds, profileId, loginId);

            this.profileRepo.updateProfileStatus(true, profileId, client);

            if (companyProfileDto.referralId) {
                const referralCheck = await this.referralsRepo.getReferralById(companyProfileDto.referralId);
                if (referralCheck.rows.length === 0) {
                    await client.query("ROLLBACK");
                    return { success: false, message: "Invalid referral code." };
                }
                await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
                    profileId,
                    companyProfileDto.referralId,
                ]);
            }

            // Remove domain from URLs
            const mediaNames = allImages
                .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
                .filter(Boolean);

            if (mediaNames.length > 0) {
                const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");
                const values = [profileId, ...mediaNames];
                this.imagesRepo.markMediaAsFinal(placeholders, values, client)
            }

            const licenseFilesToBeUpdated = []

            for (const operationArea of companyProfileDto.operationArea) {
                this.licensesRepo.addLicenseInfo(
                    profileId,
                    savedAgencyData.id,
                    operationArea.operationArea,
                    operationArea.licenseInfo.licenseNumber,
                    operationArea.licenseInfo.licenseExpiryDate,
                    operationArea.licenseInfo.licenseFile,
                    Number(loginId),
                    client,
                )
                licenseFilesToBeUpdated.push(operationArea.licenseInfo.licenseFile);
            }
            licenseFilesToBeUpdated.flat();

            if (licenseFilesToBeUpdated && licenseFilesToBeUpdated.length > 0) {
                interface LicenseFilePlaceholders {
                    placeholders: string;
                }

                const licenseFilePlaceholders: LicenseFilePlaceholders = {
                    placeholders: licenseFilesToBeUpdated
                        .map((_: string[], index: number) => `$${index + 2}`)
                        .join(", "),
                };

                const placeholders: string = licenseFilePlaceholders.placeholders;
                const values = [profileId, ...licenseFilesToBeUpdated];
                this.imagesRepo.markMediaAsFinal(placeholders, values, client);
            }

            await client.query("COMMIT"); // Commit transaction

            const updatedData = await this.profileRepo.getProfileById(profileId, client);
            const updatedProfileData = updatedData.rows[0];

            let data = {
                name: savedAgencyData.name,
                email: updatedProfileData.email,
                phone: savedAgencyData.companyPhone,
                accountType: updatedProfileData.accountType
            }

            return {
                success: true,
                message: "Company registration completed successfully",
                data: data,
            };
        } catch (error) {
            return { success: false, message: "An error occurred while updating company profile" };
        }

        function extractDataToSaveInAgentDetailsTable(companyIndustries: { industryId: string; industryName: string; roles: { roleId: string; roleName: string; }[]; }[], industryOtherId: string) {
            const updateFields: Record<string, any> = []
            const operationAreasIds = companyProfileDto.operationArea.map(area => area.operationArea).join(",");
            updateFields.operationArea = operationAreasIds;
            updateFields.nationality = companyProfileDto.nationality;
            updateFields.personalIdDoc = companyProfileDto.emiratesId ? CollectionUtils.convertToArray(companyProfileDto.emiratesId) : null;
            updateFields.emiratesId = companyProfileDto.emiratesId ? CollectionUtils.convertToArray(companyProfileDto.emiratesId) : null;

            const allIndustryMissionIds = companyIndustries.map(ind => ind.industryId).join(",");
            updateFields.industryMission = allIndustryMissionIds;

            const industryMissionOther = companyIndustries
                .filter((ind: any) => ind.industryId === industryOtherId);


            if (industryMissionOther) {
                const roleNamesForOtherIndustries = industryMissionOther
                    .map((industry: { roles: any[]; }) => industry.roles.map((role: { roleName: any; }) => role.roleName))
                    .flat()
                    .join(", ");

                const otherIndustryNames = industryMissionOther
                    .map((industry: { industryName: any; }) => industry.industryName)
                    .join(", ") || null;

                updateFields.industrySubCategory = otherIndustryNames;
                updateFields.industryMissionOther = roleNamesForOtherIndustries;
            }

            updateFields.termsAgree = companyProfileDto.termsAgree;
            updateFields.accuracyConfirm = companyProfileDto.accuracyConfirm;
            return updateFields;
        }

        async function insertRolesAndIndustriesIfNotExist(companyIndustries: { industryId: string; industryName: string; roles: { roleId: string; roleName: string; }[]; }[], loginId: QueryResult<any>, draftStatusId: number) {
            let agencyRoleIds: string[] = [];
            for (const industry of companyIndustries) {
                const industryName = industry.industryName;
                const industryId = await AgentService.insertIndustryIfNotExist(industryName, loginId, draftStatusId, "agency", client);
                for (const role of industry.roles) {
                    const existingService = await AgentService.servicesRepo.getServiceByName(role.roleName);
                    let roleId: string;

                    if (existingService.rows.length > 0) {
                        roleId = existingService.rows[0].id as string;
                    } else {
                        const insertResult = await AgentService.servicesRepo.addService(
                            role.roleName,
                            role.roleName,
                            industryId,
                            2,
                            draftStatusId,
                            Number(loginId),
                            true,
                            "agency",
                            client
                        );
                        roleId = insertResult.rows[0].id as string;
                    }
                    agencyRoleIds.push(roleId);
                }
            }
            return agencyRoleIds;
        }

        async function processAgentRolesData(this: any, agentRoleIds: string[], profileId: number, loginId: any) {
            // ADDING ALL AGENT ROLE IDs TO AGN.agentRoles
            if (agentRoleIds.length > 0) {
                for (const roleId of agentRoleIds) {
                    const subCategoryId = Number(roleId);
                    if (!isNaN(subCategoryId)) {
                        if (subCategoryId) {
                            AgentService.agentRoleRepo.addAgentRole(profileId, subCategoryId, loginId, client);
                        }
                    }
                }
            }
        }

        function getAllImages() {
            const profilePhoto = CollectionUtils.convertToArray(companyProfileDto.profilePhoto);
            const visaDoc = CollectionUtils.convertToArray(companyProfileDto.visa);
            const passportDoc = CollectionUtils.convertToArray(companyProfileDto.passport);
            const emiratesId = CollectionUtils.convertToArray(companyProfileDto.emiratesId);
            const personalIdDoc = CollectionUtils.convertToArray(companyProfileDto.emiratesId);
            const supportingDocsDoc = CollectionUtils.convertToArray(companyProfileDto.supportingDocs);

            const allImages = [
                ...supportingDocsDoc,
                ...visaDoc,
                ...passportDoc,
                ...emiratesId,
                ...profilePhoto,
                ...personalIdDoc
            ].flat();

            return {
                supportingDocsDoc,
                visaDoc,
                passportDoc,
                emiratesId,
                profilePhoto,
                personalIdDoc,
                allImages,
            };
        }
    }

    private static async insertIndustryIfNotExist(industryName: string, loginId: any, statusId: number, group: string | null, client: PoolClient): Promise<string> {
        let industryId = "";
        try {
        if (industryName !== "") {
            const existing = await AgentService.servicesRepo.getServiceByName(industryName);

            if (existing.rows.length === 0) {
                const result = await AgentService.servicesRepo.addService(
                    industryName,
                    industryName,
                    null,
                    2,
                    statusId,
                    loginId,
                    true,
                    group,
                    client
                )
                console.log("Result for inserting services: ", result.rows);
                industryId = result.rows[0].id as string;
            } else {
                industryId = existing.rows[0].id as string;
            }
        }
        return industryId;
        } catch (error) {
            console.error("Error in insertIndustryIfNotExist:", error);        
            return industryId;
        }   
    }

    static async getCompanyApplicationByProfileId(profileId: number) {
        const client = await db.connect();
        const profileData = await this.profileRepo.getProfileById(profileId, client);
        ensuresResultsAreNotEmpty(profileData);
        const agencyData = await this.agenciesRepo.getAgenciesDataByProfileId(profileId);
        ensuresResultsAreNotEmpty(agencyData);
        const companyDetails = await this.agentDetailsRepo.getAgentDetailsByProfileId(profileId);
        ensuresResultsAreNotEmpty(companyDetails);
        const agentRolesData = await this.agentRoleRepo.getAgentRolesByProfileId(profileId);
        ensuresResultsAreNotEmpty(agentRolesData);
        const operationAreaAndLicenseInfo = await this.licensesRepo.getLicensesByProfileId(profileId);
        ensuresResultsAreNotEmpty(operationAreaAndLicenseInfo);

        const agencyDetails = companyDetails.rows[0];

        const agency = agencyData.rows[0];

        const companyIndustries: any[] = await AgentService.buildCompanyIndustries(agentRolesData.rows)

        const operationAreas = (operationAreaAndLicenseInfo as { rows: any[] }).rows;
        const operationArea = operationAreas.map((license) => ({
            operationArea: license.operationArea,
            licenseInfo: {
                licenseNumber: license.licenseNumber,
                licenseExpiryDate: license.licenseExpiryDate,
                licenseFile: license.licenseFile
            }
        }));

        const companyProfileApplication: CompanyProfileDto = {
            companyName: agency.name as string,
            companyPhone: agency.companyPhone as string,
            operationArea: operationArea,
            profilePhoto: profileData.rows[0].profileImage,
            industries: companyIndustries,
            nationality: agencyDetails.nationality as string,
            emiratesId: agencyDetails.emiratesId as string[],
            passport: agency.passportDoc as string[],
            visa: agency.visaDoc as string[],
            inviteAgents: agency.inviteAgents as string[],
            supportingDocs: agency.supportingDocsDoc as string[] | undefined,
            termsAgree: agencyDetails.termsAgree as boolean,
            accuracyConfirm: agencyDetails.accuracyConfirm as boolean,
            inviteTeamMembers: null
        }
        return companyProfileApplication;
    }

    private static async buildCompanyIndustries(agentRolesData: any[]): Promise<any[]> {
        const companyIndustries: any[] = [];

        for (const agentRoleRow of agentRolesData) {
            const { serviceId } = agentRoleRow;

            // Get service details
            const service = await AgentService.servicesRepo.getServiceById(Number(serviceId));
            const { id, name, parentId } = service.rows[0];

            // Get parent industry (primary industry)
            const primaryIndustry = await AgentService.servicesRepo.getServiceById(Number(parentId));
            const primaryIndustryData = primaryIndustry.rows[0];

            // Build industry object
            const industries = {
                industryId: primaryIndustryData.id,
                industryName: primaryIndustryData.name,
                roles: {
                    roleId: id,
                    roleName: name,
                },
            };
            companyIndustries.push(industries);
        }
        return companyIndustries;
    }


    static async getCountsByProfileId(profileId: number) {
        const servicesCountData = await this.prfService.getServicesCountByProfileId(profileId);
        const eventsCountData = await this.eventService.getEventsCountByProfileId(profileId);
        const propertiesCountData = await this.propertiesService.getPropertiesCountByProfileId(profileId);
        const projectsCountData = await this.projectsService.getProjectsCountByProfileId(profileId);

        return {
            Services: servicesCountData,
            Events: eventsCountData,
            Properties: propertiesCountData,
            Projects: projectsCountData
        };
    }

    static async getIndividualApplicationByProfileId(profileId: number) {
        const client = await db.connect();
        const profileData = await this.profileRepo.getProfileById(profileId, client);
        ensuresResultsAreNotEmpty(profileData);
        const agentProfile = profileData.rows[0];

        const agentDetails = await this.agentDetailsRepo.getAgentDetailsByProfileId(profileId);
        ensuresResultsAreNotEmpty(agentDetails);
        const agent = agentDetails.rows[0];

        const agentRolesData = await this.agentRoleRepo.getAgentRolesByProfileId(profileId);
        ensuresResultsAreNotEmpty(agentRolesData);

        const agentIndustries: any[] = await AgentService.buildAgentIndustries(agentRolesData.rows, profileId)

        const individualProfileApplication: IndividualCompleteProfileDto = {
            firstName: agentProfile.firstName,
            middleName: agentProfile.middleName,
            lastName: agentProfile.lastName,
            nationality: agent.nationality as string,
            gender: agent.gender as string,
            phoneNumber: agentProfile.phone,
            profilePhoto: agentProfile.profileImage,
            industries: agentIndustries,
            location: agentProfile.locationId,
            documents: {
                emiratesId: agent.emiratesId as string[] | undefined,
                emiratesIdExpiry: agent.emiratesIdExpiry as string | undefined,
                passport: agent.passportDoc as string[] | undefined,
                visa: agent.visaDoc as string[] | undefined,
                visaExpiry: agent.visaExpiry as string | undefined
            },
            termsAgree: agent.termsAgree as boolean,
            accuracyConfirm: agent.accuracyConfirm as boolean,
            communicationConsent: agent.communicationConsent as boolean,
        }
        return individualProfileApplication;
    }


    private static async buildAgentIndustries(agentRolesData: any[], profileId: number): Promise<any[]> {
        const agentIndustries: any[] = [];

        for (const agentRoleRow of agentRolesData) {
            const { serviceId } = agentRoleRow;

            // Get service details
            const service = await AgentService.servicesRepo.getServiceById(Number(serviceId));
            const { id, name, parentId } = service.rows[0];

            // Get parent industry
            const primaryIndustry = await AgentService.servicesRepo.getServiceById(Number(parentId));
            const primaryIndustryData = primaryIndustry.rows[0];

            // Get agent license for this role
            const agentLicense = await AgentService.agentLicensesRepo.getLicensesByAgentIdAndRoleId(profileId, Number(id));
            ensuresResultsAreNotEmpty(agentLicense);
            const roleLicenseInfo = agentLicense.rows[0];

            // Build industry object
            const industries = {
                industryId: primaryIndustryData.id,
                industryName: primaryIndustryData.name,
                roles: [
                    {
                        roleId: id,
                        roleName: name,
                        hasLicense: roleLicenseInfo.hasLicense,
                        licenseDetails: {
                            licenseNumber: roleLicenseInfo.licenseNumber,
                            licenseExpiryDate: roleLicenseInfo.licenseexpiryDate,
                            licenseDocs: roleLicenseInfo.licenseFile,
                        },
                    },
                ],
            };
            agentIndustries.push(industries);
        }

        return agentIndustries;
    }


}

function ensuresResultsAreNotEmpty(data: QueryResult<any>) {
    if (!data || data.rows?.length === 0) {
        throw new Error("Error occured while getting individual application");
    }
}


