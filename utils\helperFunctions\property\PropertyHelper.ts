export class PropertyHelper {
  /** Parse integer safely, with fallback */
  static toInt(v: any, fallback: number): number {
    const n = parseInt(String(v), 10);
    return Number.isFinite(n) && n > 0 ? n : fallback;
  }

  /** Normalize CSV or array → clean string[] */
  static splitCsv(v: unknown): string[] {
    if (Array.isArray(v)) {
      return v
        .map(String)
        .map((s) => s.trim())
        .filter(Boolean);
    }
    if (typeof v === "string") {
      return v
        .split(",")
        .map((s) => s.trim())
        .filter(Boolean);
    }
    return [];
  }

  /** Whitelist a value against allowed enum-like strings */
  static pick<T extends string>(v: any, allowed: readonly T[], fallback: T): T {
    return (allowed as readonly string[]).includes(String(v))
      ? (v as T)
      : fallback;
  }

  /** Accept [1,2], "[1,2]" or "1,2" → number[] */
  static parseIdList(input: unknown): number[] {
    if (Array.isArray(input)) {
      return input.map((x) => Number(x)).filter(Number.isFinite);
    }
    if (typeof input === "string") {
      const trimmed = input.trim();
      if (!trimmed) return [];
      try {
        const asJson = JSON.parse(trimmed);
        if (Array.isArray(asJson)) {
          return asJson.map((x) => Number(x)).filter(Number.isFinite);
        }
      } catch {
        // fall back to CSV
      }
      return trimmed
        .split(",")
        .map((s) => Number(s.trim()))
        .filter(Number.isFinite);
    }
    return [];
  }
}
