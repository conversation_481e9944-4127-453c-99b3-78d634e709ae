export type AdminBillingTransaction = {
  id: number;
  profileId: number;
  agentName: string;
  agentEmail: string | null;
  amount: number; // base units
  amountFormatted: string; // "AED 299.99"
  type: string; // "Subscription" | "Payment" | ...
  status: string; // "Completed" | ...
  description: string | null;
  date: string; // ISO
  paymentMethod: string | null; // "Credit Card ***1234" / "Bank Transfer"
  reference: string; // "TXN_001234" (your display ref)
  stripe: {
    customerId?: string | null;
    subscriptionId?: string | null;
    paymentIntentId?: string | null;
    chargeId?: string | null;
    cardBrand?: string | null;
    cardLast4?: string | null;
    cardExpMonth?: number | null;
    cardExpYear?: number | null;
  };
};
