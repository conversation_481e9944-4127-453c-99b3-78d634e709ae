// --- imports -------------------------------------------------------------
import { PoolClient } from "pg";
import Stripe from "stripe";
import { db } from "../../config/database";
import { AUTH } from "../database/queries/auth";
import stripeKeys from "../../config/stripeConfigs";
import { sendSubscriptionConfirmationEmail } from "../services/nodemailer/sendSubscriptionConfirmationEmail";

import { WEBHOOK } from "../database/queries/webhook";

// — optional helper -------------------------------------------------------
const priceIdToPackageTypeId = async (
  priceId: string,
  pg: PoolClient
): Promise<number | null> => {
  const { rows } = await pg.query(
    WEBHOOK.GET_PACKAGE_TYPE_ID_BY_STRIPE_PLAN_ID,
    [priceId]
  );
  return rows[0]?.id ?? null;
};

// --- main webhook --------------------------------------------------------
export const subscriptionCreation = async (subscriptionData: any) => {
  try {
    /* ------------------------------------------------------------------ */
    /* 1️⃣  Look-up the app user from Stripe customer                     */
    /* ------------------------------------------------------------------ */
    const user = await stripeCustomerDetails(subscriptionData.customer);
    if (!user) return;

    const pgClient = await db.connect();
    try {
      await pgClient.query("BEGIN");

      const profileId = user.id;
      const packageTypeId = await priceIdToPackageTypeId(
        subscriptionData.plan.id,
        pgClient
      );

      /* STEP 1: grab “Cancelled” status id */
      const statusNames = ["Cancelled"];
      const statusRes = await pgClient.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );
      if (statusRes.rows.length === 0) throw new Error("Status not found");
      const cancelledStatusId = statusRes.rows[0].id;

      const paymentStatusNames = [
        subscriptionData.status == "paid" || subscriptionData.status == "active" ? "Paid" : "Pending",
      ];

      const statusPaymentRes = await pgClient.query(
        AUTH.SELECT_ACCOUNT_STATUS(paymentStatusNames),
        paymentStatusNames
      );

      if (statusPaymentRes.rows.length === 0)
        throw new Error("Status not found");
      const PaymentStatusId = statusPaymentRes.rows[0].id;

      /* STEP 2: load package meta from look.sp_packagetype */
      const pkgParams = [
        0,
        packageTypeId,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "id",
        null,
        null,
        null,
        null,
      ];

      const pkgResult = await db.query(
        "SELECT * FROM look.sp_packagetype($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,$13, $14, $15)",
        pkgParams
      );

      const pkgData = pkgResult.rows[0]?.sp_packagetype;

      if (!pkgData || pkgData.type === "error" || !pkgData.data)
        throw new Error("Package type not found");
      if (pkgData.data.statusId !== 1)
        throw new Error("Package type is not active");

      /* STEP 3: upsert subscription */
      const now = new Date();
      const endDate = new Date(now);
      endDate.setMonth(now.getMonth() + 1);

      const { rows: existingSubs } = await pgClient.query(
        WEBHOOK.GET_ACTIVE_SUBSCRIPTION,
        [profileId, packageTypeId]
      );

      if (existingSubs.length) {
        // — extend / refresh same package
        await pgClient.query(WEBHOOK.UPDATE_SUBSCRIPTION, [
          now,
          endDate,
          now,
          subscriptionData.id,
          existingSubs[0].id,
        ]);
      } else {
        // — cancel *other* active subs
        const { rows: oldSubs } = await pgClient.query(
          WEBHOOK.GET_OTHER_ACTIVE_SUBSCRIPTION,
          [profileId, packageTypeId]
        );
        for (const sub of oldSubs) {
          await pgClient.query(WEBHOOK.CANCEL_SUBSCRIPTION, [
            cancelledStatusId,
            sub.id,
          ]);
        }

        // — insert new sub via look.sp_subscription
        const price = pkgData.data.price || 0;
        const currency = pkgData.data.currency || "AED";
        const subParams = [
          2, //  1  p_fnid  (INSERT/UPDATE)
          null, //  2  p_id    (null ⇒ INSERT)
          subscriptionData.id, //  3  p_subscriptionid  (Stripe ID)
          pkgData.data.name, //  4  p_package          (plan name)  ❖
          null, //  5  p_typeid           (unused)
          null, //  6  p_details          (unused)
          1, //  7  p_statusid         (1 = Active)
          profileId, //  8  p_profileid
          packageTypeId, //  9  p_packagetypeid
          now, // 10  p_startdate
          endDate, // 11  p_enddate
          null, // 12  p_renewaldate
          price, // 13  p_price
          currency, // 14  p_currency
          PaymentStatusId, // 15  p_paymentstatusid
          null, // 16  p_sortby  (unused when fnid=2)
        ];
        const subRes = await pgClient.query(
          WEBHOOK.CREATE_SUBSCRIPTION,
          subParams
        );

        const subData = subRes.rows[0]?.sp_subscription;
        if (!subData || subData.type === "error")
          throw new Error(subData?.message || "Failed to subscribe");
      }

      // STEP 3.5: Update customer ID in profile table if not already set

      const query = `
        UPDATE prf.profile
        SET
          "customerId" = $1
        WHERE id = $2
        RETURNING *;
      `;

      const profileValues = [subscriptionData.customer, profileId];

      await pgClient.query(query, profileValues);

      /* STEP 4: commit Postgres and send confirmation email */
      await pgClient.query("COMMIT");

      const fullName = [user.firstName, user.middleName, user.lastName]
        .filter(Boolean)
        .join(" ");
      try {
        const subscription = await pgClient.query(
          WEBHOOK.GET_SUBSCRIPTION_BY_SUBSCRIPTION_ID,
          [subscriptionData.id]
        );
        const ref = await pgClient.query(WEBHOOK.GET_REFERRAL_BY_USER_ID, [
          profileId,
        ]);
        const selectDiscount = await pgClient.query(
          WEBHOOK.GET_SALES_PERSON_BY_ID,
          [ref.rows[0].salesPersonId]
        );
        const amount = pkgData.data.price;
        const commissionRate = selectDiscount.rows[0].commission_rate;
        const commissionAmount = Math.round(
          (Number(amount) * commissionRate) / 100
        ); 
       
          // Query only returns commission rows where isSubscribed is false
          const existingCommission = await pgClient.query(
            WEBHOOK.GET_COMMISSION_BY_USER_AND_SALES_PERSON,
            [profileId, ref.rows[0].salesPersonId, false]
          );

          // Use the first unsubscribed commission row directly
          const unsubscribedCommission = existingCommission.rows[0];

        if (unsubscribedCommission && commissionAmount > 0) {
          // Update the unsubscribed commission record
          if (!unsubscribedCommission.isSubscribed) {
            await pgClient.query(WEBHOOK.UPDATE_COMMISSION, [
              commissionAmount,
              3,
              subscription.rows[0].id,
              ref.rows[0].id,
              unsubscribedCommission.id,
            ]);
          } else {
            console.log("Already Subscribed paid");
          }
        } else if (commissionAmount > 0 && !existingCommission.rows.length) {
          // Insert a new commission record only if no unsubscribed commission exists
          await pgClient.query(WEBHOOK.INSERT_COMMISSION, [
            profileId,
            ref.rows[0].salesPersonId,
            commissionAmount,
            3,
            subscription.rows[0].id,
            ref.rows[0].id,
          ]);
        } else {
          console.log(
            "ALready Added or commissionAmount is not greater than 0"
          );
        }
      } catch (error) {
        console.error("Error creating/updating commission record:", error);
      }

      await sendSubscriptionConfirmationEmail(
        fullName,
        user.email,
        pkgData.data.name,
        pkgData.data.price || 0,
        pkgData.data.currency || "AED",
        now.toLocaleDateString(),
        endDate.toLocaleDateString()
      );
    } catch (error) {
      console.log(error);
      await pgClient.query("ROLLBACK");
    } finally {
      pgClient.release();
    }

    /* ------------------------------------------------------------------ */
    /* 4️⃣  Done — both Mongo & Postgres are consistent                    */
    /* ------------------------------------------------------------------ */
  } catch (err) {
    console.error("[subscriptionCreation] ", err);
    // optional: alerting / Sentry / etc.
  }
};

export const stripeCustomerDetails = async (customerId: string) => {
  const stripe = new Stripe(stripeKeys.stripe.secretKey);
  const customerData = await stripe.customers.retrieve(customerId);
  if (!customerData.deleted) {
    const result = await db.query(AUTH.SELECT_BY_EMAIL, [customerData?.email]);
    return result.rows[0];
  }
  return;
};
