import { <PERSON><PERSON> } from "knex";
import bcrypt from "bcryptjs";
import { TABLE } from "../../utils/database/table";
import { db } from "../../config/database";
import { AUTH } from "../../utils/database/queries/auth";

export async function seed(knex: Knex): Promise<void> {
    const email = "<EMAIL>";
    const phoneNumber = "+**********";
    const userName = "superadmin";
    const accountType = "email";
    const hashedPassword = await bcrypt.hash("Secure123@", 10);

    try {
        await db.query("BEGIN");

        // Insert profile
        const result = await db.query(
            `INSERT INTO "${TABLE.PROFILE_TABLE}" (
        "firstName", "lastName", email, phone, "cardHolderName", "cardType", "cardNumber", "createdOn"
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, DEFAULT) RETURNING *;`,
            ["Super", "Admin", email, phoneNumber, "test", "test", "12345"]
        );

        if (result.rows.length === 0) throw new Error("Failed to create profile");

        const user = result.rows[0];

        // Insert login
        const loginData = await db.query(
            `INSERT INTO "${TABLE.LOGIN_TABLE}" (
        "profileId", username, "accountType", "passwordHash"
      ) VALUES ($1, $2, $3, $4) RETURNING *;`,
            [user.id, userName, accountType, hashedPassword]
        );

        if (loginData.rows.length === 0) throw new Error("Failed to create login");

        const login = loginData.rows[0];

        // Insert login-role
        await db.query(
            `INSERT INTO "${TABLE.LOGIN_ROLE}" ("loginId", "roleId", "createdBy") VALUES ($1, $2, $3);`,
            [login.id, 1, user.id]
        );

        // Update login (assuming it takes profileId — or change to login.id if needed)
        await db.query(AUTH.UPDATE_INTO_LOGIN, [user.id]);

        await db.query("COMMIT");
        console.log("✅ SuperAdmin user seeded successfully.");
    } catch (error) {
        await db.query("ROLLBACK");
        console.error("❌ Error seeding SuperAdmin user:", error);
    }
}
