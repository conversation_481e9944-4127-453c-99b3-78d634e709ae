import { sql } from "drizzle-orm";
import { PoolClient } from "pg";
import { drizzleDb } from "../../config/database";
import { AUTH } from "../../utils/database/queries/auth";

export class StatusRepository {

    async getStatusIdByStatusName(status: string) {
        const result = await drizzleDb.execute(
            sql`SELECT id FROM look.status WHERE name = ${status}`
        );
        return result.rows[0].id as number;
    }

    async getStatusesByStatusNames(statusNames: string[], client: PoolClient) {
        return await client.query(
                AUTH.SELECT_ACCOUNT_STATUS(statusNames),
                statusNames
            );
    }
}