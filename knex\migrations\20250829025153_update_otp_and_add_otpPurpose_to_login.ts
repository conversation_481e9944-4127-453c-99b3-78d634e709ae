import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.withSchema("sec").alterTable("login", (table) => {
        table.string("otp", 255).nullable().alter();
        table.enum("otpPurpose", ["ACCOUNT_VERIFICATION", "FORGOT_PASSWORD"])
            .nullable()
            .after("otp");
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.withSchema("sec").alterTable("login", (table) => {
        table.integer("otp").nullable().alter();
        table.dropColumn("otpPurpose");
    });
}