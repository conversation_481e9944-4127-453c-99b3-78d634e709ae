import { PrfServicesRepository } from "../../repo/prf/services-repository";

export class PrfServicesService {

    private prfServicesRepo = new PrfServicesRepository();

    async getServicesCountByProfileId(id: number) {
        const count = await this.prfServicesRepo.getServicesCountByProfileId(id);
        const servicesCountData = {
            ServicesCount: count,
            ServicesAllowedLimit: 10
        }
        return servicesCountData;
    }

}