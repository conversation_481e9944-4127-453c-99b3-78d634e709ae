// repo/billings/BillingRepository.ts
import { db } from "../../config/database";
import { DbListParams } from "../../dto/billing/DbListParamsDTO";
import { BillingQueries } from "../../utils/database/queries/BillingQueries";
import { TABLE } from "../../utils/database/table";

export class BillingRepository {
  async getSummaryRow() {
    const { rows } = await db.query(BillingQueries.SUMMARY);
    return rows[0] || {};
  }

  async getStatusIdByName(name?: string | null): Promise<number | null> {
    if (!name) return null;
    const { rows } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [name]
    );
    return rows[0]?.id ?? null;
  }

  async listTransactions(params: DbListParams) {
    return db.query(
      BillingQueries.LIST(params),
      BillingQueries.LIST_PARAMS(params)
    ); // returns { rows }
  }
}
