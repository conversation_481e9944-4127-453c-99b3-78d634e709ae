import { Request, Response } from "express";
import { ReviewService } from "../../service/ReviewsService";
import async<PERSON>and<PERSON> from "../../middleware/trycatch";
import { response, responseData } from "../../utils/response";
import validate from "../../utils/validations";
import {
  createReviewSchema,
  flagReviewSchema,
} from "../../utils/validations/reviews.validation";
interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    [key: string]: any;
  };
}

export class ReviewController {
  private reviewService = new ReviewService();

  createReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (req.body.revieweeId) req.body.revieweeId = Number(req.body.revieweeId);
      if (req.body.rating) req.body.rating = Number(req.body.rating);
      
      const validation = await validate(createReviewSchema, req.body, res);
      if (!validation.success) {
        const errorMsg = typeof (validation as any).message === 'string' ? (validation as any).message : 'Validation failed';
        return response(res, 400, errorMsg);
      }

      const { revieweeId, reviewText, rating } = validation.data;
      const reviewerId = req.user?.id;

      if (!reviewerId) {
        return response(res, 401, "Authentication required to create a review");
      }

      const result = await this.reviewService.createReview(
        reviewerId,
        revieweeId,
        reviewText,
        rating
      );

      return responseData(
        res,
        201,
        "Review submitted successfully. It will be visible after admin approval.",
        result
      );
    } catch (error: any) {
      if (error.message === "You have already reviewed this profile") {
        return response(res, 409, error.message);
      }
      if (error.message === "Reviewee not found") {
        return response(res, 404, error.message);
      }
      if (error.message === "You can only review agents or agencies") {
        return response(res, 403, error.message);
      }
      // Handle all 400 errors in one case
      if (
        error.message === "You cannot review yourself" ||
        error.message === "Reviewer login not found" ||
        error.message.includes("Missing required fields") ||
        error.message.includes("Rating must be between")
      ) {
        return response(res, 400, error.message);
      }
      console.error("Error creating review:", error);
      return response(res, 500, "Failed to create review");
    }
  });

  getProfileReviews = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    const result = await this.reviewService.getProfileReviews(
      Number(profileId),
      page,
      limit
    );

    return responseData(res, 200, "Reviews retrieved successfully", result);
  });

  getProfileRatingStats = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;
    const result = await this.reviewService.getProfileRatingStats(Number(profileId));
    return responseData(res, 200, "Rating statistics retrieved successfully", result);
  });

  getUserReviews = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const userId = req.user?.id;
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    if (!userId) {
      throw new Error("Authentication required");
    }

    const result = await this.reviewService.getUserReviews(userId, page, limit);
    return responseData(res, 200, "User reviews retrieved successfully", result);
  });

  getReviewById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.reviewService.getReviewById(Number(req.params.id));
      return responseData(res, 200, "Review fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review:", error);
      return response(res, 500, "Failed to fetch review");
    }
  });

  flagReview = asyncHandler(async (req: Request, res: Response) => {
    try {
      const validation = await validate(flagReviewSchema, req.body, res);
      if (!validation.success) return;

      const { flagged } = validation.data;
      await this.reviewService.flagReview(Number(req.params.id), flagged);
      return response(res, 200, `Review ${flagged ? 'flagged' : 'unflagged'} successfully`);
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error flagging review:", error);
      return response(res, 500, "Failed to flag review");
    }
  });

  getReviewHistory = asyncHandler(async (req: Request, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const result = await this.reviewService.getReviewHistory(reviewId);
      return responseData(res, 200, "Review history fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review history:", error);
      return response(res, 500, "Failed to fetch review history");
    }
  });
}
