import express from "express";
 
import { storageData } from "../../../utils/services/multer";
import { getAllPayments, getAllReferals, getRecentReferrals,  getSalespersonDashboardStats,  getTopSalespersonsByCommission, updatePaymentPaid, updateReferal } from "../../../controller/admin/referral-management";
const router = express.Router();

const upload = storageData("salespersons");
 
router.get("/", getAllReferals);  
router.get("/recent", getRecentReferrals);
router.get("/top", getTopSalespersonsByCommission);
router.get("/dashboard", getSalespersonDashboardStats);
router.put("/:id", updateReferal);

router.put("/payments/:id", updatePaymentPaid);
router.get("/payments", getAllPayments);

export default router;
