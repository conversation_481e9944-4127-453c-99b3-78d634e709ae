import { db } from "../../config/database";
import { AUTH } from "../../utils/database/queries/auth";

export class StatusRepository {
  async getStatusDetailsByStatusName(statusNames: string[]) {
    const { rows } = await db.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );
    return rows ?? null;
  }

  async getStatusDetailsById(statusId: number) {
    const { rows } = await db.query(AUTH.SELECT_STATUS_BY_ID, [statusId]);
    return rows[0] ?? null;
  }
}
