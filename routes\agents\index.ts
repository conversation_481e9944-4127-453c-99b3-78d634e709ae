import { Request, Response } from "express";
import express from "express";
import * as agentsController from "../../controller/agents/index";
import { authMiddleware } from "../../middleware/authMiddleware";
import { storageData, uploadErrorHandler } from "../../utils/services/multer";
import agentsAuthRoutes from "./auth/auth.Route";
import SubscriptionRoutes from "./subscription.Route";
import PropertiesRoutes from "./properties/propertyRoutes";
import ProfileRoutes from "./profile.routes";
import ServicesRoutes from "./services/serviceRoutes";
import { getAgentAccountsById } from "../../controller/admin/agent";
import paymentsRoutes from "./payments/paymentRoutes";
import { requestValidatorMiddleware } from "../../middleware/requestValidatorMiddleware";
import { CompanyProfileSchema } from "../../dto/company-profile/company-profile";
import { IndividualCompleteProfileSchema } from "../../dto/individual/individual-complete-profile";

const router = express.Router();

const upload = storageData("agentDetails");

router.post(
  "/",
  authMiddleware,
  upload.fields([
    { name: "profilePhoto", maxCount: 20 },
    { name: "licenseDoc", maxCount: 20 },
    { name: "freelancePermitDoc", maxCount: 20 },
    { name: "tradeLicenseDoc", maxCount: 20 },
    { name: "employmentDoc", maxCount: 20 },
    { name: "certifications", maxCount: 20 },
    { name: "emiratesId", maxCount: 20 }, // ✅ Add this
    { name: "visa", maxCount: 20 }, // ✅ Add this
    { name: "passport", maxCount: 20 }, // ✅ Add this
  ]),
  uploadErrorHandler,
  agentsController.createAgentProfile
);

router.post(
  "/profile-creation",
  authMiddleware,
  upload.none(),
  agentsController.createBasicAgentProfile
);

router.put(
  "/account-action",
  authMiddleware,
  upload.none(),
  agentsController.activateDeactivateAccount
);

router.get(
  "/",
  authMiddleware,
  upload.none(),
  agentsController.getAgentProfile
);

router.put(
  "/application-temp-data",
  authMiddleware,
  upload.none(),
  agentsController.updateApplicationTempData
);

router.put(
  "/complete-profile",
  authMiddleware,
  requestValidatorMiddleware(IndividualCompleteProfileSchema),
  upload.none(),
  agentsController.completeAgentProfileV2
);

router.put(
  "/complete-company-profile",
  authMiddleware,
  requestValidatorMiddleware(CompanyProfileSchema),
  upload.none(),
  agentsController.completeCompanyAgentProfileV2
);

router.put(
  "/re-submit/application/agent",
  authMiddleware,
  upload.none(),
  agentsController.updateAgentApplicationProfile
);

router.put(
  "/re-submit/application/company",
  authMiddleware,
  upload.none(),
  agentsController.updateCompanyAgentProfile
);

router.get(
  "/get-individual-application",
  authMiddleware,
  upload.none(),
  agentsController.getIndividualApplicationByProfileId
);

router.get(
  "/get-application",
  authMiddleware,
  upload.none(),
  agentsController.getApplicationProfileByProfileId
);

router.get(
  "/get-company-application",
  authMiddleware,
  upload.none(),
  agentsController.getCompanyApplicationByProfileId
);

router.get("/user-profile/:id", getAgentAccountsById);

router.put("/profile", authMiddleware, agentsController.updateProfile);
router.post(
  "/invite-team",
  authMiddleware,
  upload.none(),
  agentsController.inviteTeamMembers
);

router.post("/login", agentsController.agentLogin);

router.post(
  "/send-invitation-team-member",
  authMiddleware,
  upload.none(),
  agentsController.sendInvitationTeamMember
);

router.get("/invite/accept", agentsController.acceptInvite);

router.get("/invited-agent-email", agentsController.getInvitedAgentEmail);

router.get(
  "/team-members",
  authMiddleware,
  upload.none(),
  agentsController.getTeamMembers
);

router.delete(
  "/agency/team/:agency_id/:agent_id",
  authMiddleware,
  agentsController.suspendOrUnlinkTeamMember
);

router.patch(
  "/team-member/:id/reset-password",
  authMiddleware,
  agentsController.resetUserPassword
);

router.get(
  "/get-counts/:id",
  authMiddleware,
  agentsController.getCountsByProfileId
);

router.use("/auth", agentsAuthRoutes);
router.use("/subscription", authMiddleware, SubscriptionRoutes);

router.use("/properties", authMiddleware, PropertiesRoutes);

router.use("/profile", authMiddleware, ProfileRoutes);

router.use("/payments", authMiddleware, paymentsRoutes);

router.use("/services", authMiddleware, ServicesRoutes);

export default router;
