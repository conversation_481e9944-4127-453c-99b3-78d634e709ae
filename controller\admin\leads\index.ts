import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { AUTH } from "../../../utils/database/queries/auth";
import { TABLE } from "../../../utils/database/table";
import { LEADS } from "../../../utils/database/queries/leads";

import {
  generatePassword,
  sendWelcomeEmail,
} from "../../../utils/services/nodemailer/leadConverting";
import { generateUniqueUsername } from "../../../utils/helperFunctions/userNameValidator";
import { generateOTP } from "../../../utils/services/nodemailer/register";
import { REFERRALS } from "../../../utils/database/queries/referrals";
import { nanoid } from "nanoid";
import bcrypt from "bcryptjs";
import { parseCsv, RawLeadRow } from "../../../utils/parseCsvLeads";
interface LeadData {
  leads: any;
  leadsCounts: any[]; // or a specific type like LeadCount[] if known
}
import fs from "fs";
import { AGENT_ACCOUNT_TYPE } from "../../../utils/enums/account.enum";
import { sendEmailToLeadCreated } from "../../../utils/services/nodemailer/leadCreation";

// Helper
const isValidLeadType = (type: string) =>
  ["agent", "agency"].includes(type?.toLowerCase());

// -------------------- GET ALL LEADS --------------------
// GET /leads?search=acme&status=New&page=2&pageSize=20
// ─────────────────────────────────────────────────────
export const getAllLeads = asyncHandler(async (req: Request, res: Response) => {
  /* ───────── read query-string params ───────── */
  const {
    filterColumn,
    filterValue,
    status,
    page,
    pageSize,
    search, // <── NEW
  } = req.query;

  /* ───────── resolve primitives ───────── */
  const resolvedFilterColumn = filterColumn ? String(filterColumn) : null;
  const resolvedFilterValue = filterValue ? String(filterValue) : null;
  const resolvedSearch = search ? String(search).trim() : null;

  let resolvedStatusId: number | null = null;

  /* ───────── map status name → id (optional) ───────── */
  if (status) {
    const statusName = String(status).trim();
    const { rows } = await db.query(
      `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
      [statusName]
    );
    if (rows.length === 0) {
      return error(res, 400, `Status '${statusName}' not found.`);
    }
    resolvedStatusId = rows[0].id;
  }

  /* ───────── whitelist filter column ───────── */
  const allowedColumns = [
    "fullName",
    "email",
    "phone",
    "licenseNumber",
    "company",
    "leadType",
    "source",
  ];
  if (resolvedFilterColumn && !allowedColumns.includes(resolvedFilterColumn)) {
    return error(res, 400, `Invalid filter column: '${resolvedFilterColumn}'`);
  }

  /* ───────── pagination defaults & validation ───────── */
  const resolvedPage = page ? parseInt(String(page), 10) : 1;
  const resolvedPageSize = pageSize ? parseInt(String(pageSize), 10) : 10;

  if (Number.isNaN(resolvedPage) || resolvedPage < 1) {
    return error(res, 400, "`page` must be a positive integer.");
  }
  if (
    Number.isNaN(resolvedPageSize) ||
    resolvedPageSize < 1 ||
    resolvedPageSize > 100
  ) {
    return error(res, 400, "`pageSize` must be between 1 and 100.");
  }

  /* ───────── build parameter array (exactly 22) ───────── */
  const params = [
    1, //  1  p_fnid  – list mode
    null, //  2  p_lead_id
    null, //  3  p_full_name
    null, //  4  p_email
    null, //  5  p_phone
    null, //  6  p_license_number
    null, //  7  p_company
    null, //  8  p_lead_type
    resolvedStatusId, //  9  p_status_id
    null, // 10  p_source
    null, // 11  p_is_converted
    null, // 12  p_note_id
    null, // 13  p_entity_type
    null, // 14  p_entity_id
    null, // 15  p_note
    null, // 16  p_sortby
    resolvedFilterColumn, // 17  p_filter_column
    resolvedFilterValue, // 18  p_filter_value
    resolvedPage, // 19  p_page_no
    resolvedPageSize, // 20  p_page_size
    resolvedSearch, // 21  p_search_text   (NEW)
    null, // 22  p_deleted_by
  ];

  const client = await db.connect();
  try {
    /* ───────── call stored procedure ───────── */
    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes; // JSONB → JS object
    if (result.type === "error") {
      return error(res, 400, result.message);
    }

    /* ───────── counts (unchanged) ───────── */
    const data: LeadData = { leads: result.data, leadsCounts: [] };

    if (resolvedStatusId) {
      const { rows: c } = await db.query(LEADS.GET_LEADS_COUNTS_WITH_STATUS, [
        resolvedStatusId,
      ]);
      data.leadsCounts = c;
    } else {
      const { rows: c } = await db.query(LEADS.GET_LEADS_COUNTS);
      data.leadsCounts = c;
    }

    /* ───────── prepare pagination for the frontend ───────── */
    const pagination = result.pagination ?? {
      page: resolvedPage,
      pageSize: resolvedPageSize,
      returned: Array.isArray(result.data) ? result.data.length : 0,
      total: Array.isArray(result.data) ? result.data.length : 0,
    };

    /* ───────── send response ───────── */
    return responseData(res, 200, result.message, {
      ...data,
      pagination,
    });
  } catch (err) {
    console.error("getAllLeads failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- GET LEAD BY ID --------------------
export const getLeadById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const params = [
    0, // fnid (0 = get single lead)
    parseInt(id, 10), // lead_id
    null, // fullName
    null, // email
    null, // phone
    null, // licenseNumber
    null, // company
    null, // leadType
    null, // statusId
    null, // source
    null, // isConverted
    null, // noteId
    null, // entityType
    null, // entityId
    null, // note
    null, // filterColumn
    null, // filterValue
    null, // p_page_no
    null, // p_page_size
    null, // p_search_text
    null, // p_deleted_by
  ];

  const { rows } = await db.query(
    `SELECT * FROM look.sp_leads_notes(${params
      .map((_, i) => `$${i + 1}`)
      .join(", ")})`,
    params
  );

  const result = rows[0].sp_leads_notes;
  if (result.type === "error") return error(res, 400, result.message);
  if (!result.data) return error(res, 404, "Lead not found");

  return responseData(res, 200, result.message, result.data);
});

// -------------------- CREATE LEAD --------------------
export const createLead = asyncHandler(async (req: Request, res: Response) => {
  const { fullName, email, phone, licenseNumber, company, leadType, source } =
    req.body;

  if (!fullName || !email || !leadType) {
    return error(
      res,
      400,
      "Missing required fields: fullName, email, leadType"
    );
  }

  if (!isValidLeadType(leadType)) {
    return error(res, 400, "Invalid leadType. Must be 'agent' or 'agency'");
  }

  const client = await db.connect();
  try {
    await client.query("BEGIN");

    /* ───────── 1. Duplicate-email guard ───────── */
    const { rows: dup } = await client.query<{ id: number }>(
      "SELECT id FROM look.leads WHERE LOWER(email) = LOWER($1) LIMIT 1",
      [email]
    );

    if (dup.length > 0) {
      await client.query("ROLLBACK");
      return error(res, 400, "A lead with this email already exists.");
    }

    const profileResult = await db.query(AUTH.SELECT_BY_EMAIL, [email]);

    if (profileResult.rows.length > 0) {
      await client.query("ROLLBACK");
      return error(res, 400, "A user with this email already exists.");
    }

    const statusNames = ["New"];
    const status = await client.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );

    const statusId = status.rows[0].id;

    const params = [
      2, // p_fnid
      null, // p_lead_id
      fullName, // p_full_name
      email, // p_email
      phone, // p_phone
      licenseNumber, // p_license_number
      company, // p_company
      leadType.toLowerCase(), // p_lead_type
      statusId || null, // p_status_id
      source, // p_source
      false, // p_is_converted
      null, // p_note_id
      null, // p_entity_type
      null, // p_entity_id
      null, // p_note
      null, // p_sortby
      null, // p_filter_column
      null, // p_filter_value
      null, // p_page_no (defaults to 1)
      null, // p_page_size (defaults to 10)
      null, // p_search_text (defaults to NULL)
      null, // p_deleted_by
    ];

    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;
    if (result.type === "error") {
      await client.query("ROLLBACK");
      return error(res, 400, result.message);
    }

    await client.query("COMMIT");
    return responseData(res, 201, result.message, result.data);
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Create lead failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

export const createLeadViaSendingEmail = asyncHandler(async (req: Request, res: Response) => {
  const { fullName, email, phone, licenseNumber, company, leadType, source } =
    req.body;

  if (!fullName || !email || !leadType) {
    return error(
      res,
      400,
      "Missing required fields: fullName, email, leadType"
    );
  }

  if (!isValidLeadType(leadType)) {
    return error(res, 400, "Invalid leadType. Must be 'agent' or 'agency'");
  }

  const client = await db.connect();
  try {
    await client.query("BEGIN");

    /* ───────── 1. Duplicate-email guard ───────── */
    const { rows: dup } = await client.query<{ id: number }>(
      "SELECT id FROM look.leads WHERE LOWER(email) = LOWER($1) LIMIT 1",
      [email]
    );

    if (dup.length > 0) {
      await client.query("ROLLBACK");
      return error(res, 400, "A lead with this email already exists.");
    }

    const profileResult = await db.query(AUTH.SELECT_BY_EMAIL, [email]);

    if (profileResult.rows.length > 0) {
      await client.query("ROLLBACK");
      return error(res, 400, "A user with this email already exists.");
    }

    const statusNames = ["New"];
    const status = await client.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );

    const statusId = status.rows[0].id;

    const params = [
      2, // p_fnid
      null, // p_lead_id
      fullName, // p_full_name
      email, // p_email
      phone, // p_phone
      licenseNumber, // p_license_number
      company, // p_company
      leadType.toLowerCase(), // p_lead_type
      statusId || null, // p_status_id
      source, // p_source
      false, // p_is_converted
      null, // p_note_id
      null, // p_entity_type
      null, // p_entity_id
      null, // p_note
      null, // p_sortby
      null, // p_filter_column
      null, // p_filter_value
      null, // p_page_no (defaults to 1)
      null, // p_page_size (defaults to 10)
      null, // p_search_text (defaults to NULL)
      null, // p_deleted_by
    ];

    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;
    if (result.type === "error") {
      await client.query("ROLLBACK");
      return error(res, 400, result.message);
    }

    await client.query("COMMIT");
    await sendEmailToLeadCreated(fullName, email);
    return responseData(res, 201, result.message, result.data);
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Create lead failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- UPDATE LEAD --------------------
export const updateLead = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const {
    fullName,
    email,
    phone,
    licenseNumber,
    company,
    leadType,
    status,
    source,
    isConverted,
  } = req.body;

  if (!fullName || !email || !leadType) {
    return error(
      res,
      400,
      "Missing required fields: fullName, email, leadType"
    );
  }

  if (!isValidLeadType(leadType)) {
    return error(res, 400, "Invalid leadType. Must be 'agent' or 'agency'");
  }

  const client = await db.connect();

  try {
    await client.query("BEGIN");


   const existingLeadParams = [
  0,
  parseInt(id, 10),
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
  null,
];
    const existingLeadRes = await client.query(
      `SELECT * FROM look.sp_leads_notes(${existingLeadParams
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      existingLeadParams
    );

    /* ─── 2. Ensure e-mail is unique among *other* leads ─── */
    const { rows: dup } = await client.query<{ id: number }>(
      "SELECT id FROM look.leads WHERE LOWER(email) = LOWER($1) AND id <> $2 LIMIT 1 FOR UPDATE",
      [email, parseInt(id, 10)]
    );

    if (dup.length) {
      await client.query("ROLLBACK");
      return error(res, 400, "Another lead with this email already exists.");
    }

    const leadResult = existingLeadRes.rows[0].sp_leads_notes;
    if (!leadResult?.data) {
      await client.query("ROLLBACK");
      return error(res, 404, "Lead not found");
    }

    let resolvedStatusId: number;

    // 🧠 Step 2: Get statusId if provided, otherwise use existing one
    if (status) {
      const statusQuery = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS([status]),
        [status]
      );

      if (statusQuery.rows.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, `Status '${status}' not found`);
      }

      resolvedStatusId = statusQuery.rows[0].id;
    } else {
      resolvedStatusId = leadResult.data.statusId;
    }

    const updateParams = [
      2, // p_fnid = update
      parseInt(id, 10), // p_lead_id
      fullName, // p_full_name
      email, // p_email
      phone, // p_phone
      licenseNumber, // p_license_number
      company, // p_company
      leadType.toLowerCase(), // p_lead_type
      resolvedStatusId, // p_status_id
      source, // p_source
      isConverted === true, // p_is_converted
      null, // p_note_id
      null, // p_entity_type
      null, // p_entity_id
      null, // p_note
      null, // p_sortby
      null, // p_filter_column
      null, // p_filter_value
      null, // p_page_no (default 1)
      null, // p_page_size (default 10)
      null, // p_search_text (default null)
      null, // p_deleted_by
    ];

    const updateRes = await client.query(
      `SELECT * FROM look.sp_leads_notes(${updateParams
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      updateParams
    );

    const updateResult = updateRes.rows[0].sp_leads_notes;
    if (updateResult.type === "error") {
      await client.query("ROLLBACK");
      return error(res, 400, updateResult.message);
    }

    await client.query("COMMIT");
    return responseData(res, 200, updateResult.message, updateResult.data);
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Update lead failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- DELETE LEAD --------------------
export const deleteLead = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const client = await db.connect();

  const params = [
    3, // fnid = 3 (soft delete lead)
    parseInt(id, 10), // lead_id
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null, // lead fields
    null,
    null,
    null,
    null, // notes
    null,
    null, // filters
    null, // p_page_no
    null, // p_page_size
    null, // p_search_text
    req.user?.id || null, // p_deleted_by - admin user ID
  ];

  try {
    await client.query("BEGIN");

    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;
    if (result.type === "error") {
      await client.query("ROLLBACK");
      return error(res, 400, result.message);
    }

    await client.query("COMMIT");
    return response(res, 200, result.message);
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Delete lead failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- BULK DELETE LEADS --------------------
export const bulkDeleteLeads = asyncHandler(async (req: Request, res: Response) => {
  const { leadIds } = req.body;
 
  const client = await db.connect();

  const params = [
    4, // fnid = 4 (bulk soft delete leads)
    leadIds, // lead_ids array
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null,
    null, // lead fields
    null,
    null,
    null,
    null, // notes
    null,
    null, // filters
    null, // p_page_no
    null, // p_page_size
    null, // p_search_text
    req.user?.id || null, // p_deleted_by - admin user ID
  ];

  try {
    await client.query("BEGIN");

    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;
    if (result.type === "error") {
      await client.query("ROLLBACK");
      return error(res, 400, result.message);
    }

    await client.query("COMMIT");
    
    // Return the deleted leads data
    return responseData(res, 200, result.message, {
      deletedLeads: result.data,
      deletedCount: Array.isArray(result.data) ? result.data.length : 0
    });
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Bulk delete leads failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- CREATE NOTE FOR A LEAD --------------------
export const createNote = asyncHandler(async (req: Request, res: Response) => {
  /* ---------- validate input ---------- */
  const leadId = Number(req.params.id); // make sure it is a number
  const { note } = req.body;

  if (!note || note.trim().length === 0) {
    return error(res, 400, "Note cannot be empty.");
  }

  /* ---------- build the exact 22-arg list ---------- */
  const params = [
    10, // p_fnid
    null, // p_lead_id
    null, // p_full_name
    null, // p_email
    null, // p_phone
    null, // p_license_number
    null, // p_company
    null, // p_lead_type
    null, // p_status_id
    null, // p_source
    null, // p_is_converted
    null, // p_note_id
    "lead", // p_entity_type
    leadId, // p_entity_id
    note, // p_note
    null, // p_sortby
    null, // p_filter_column
    null, // p_filter_value
    1, // p_page_no
    10, // p_page_size
    null, // p_search_text
    null, // p_deleted_by
  ];

  /* ---------- make the call ---------- */
  const client = await db.connect();
  try {
    // $1,$2,…,$22  – generated from params.length
    const { rows } = await client.query(
      `SELECT * FROM look.sp_leads_notes(${params
        .map((_, i) => `$${i + 1}`)
        .join(", ")})`,
      params
    );

    const result = rows[0].sp_leads_notes;

    if (result.type === "error") {
      return error(res, 400, result.message);
    }

    return responseData(res, 201, "Note added successfully.", result.data);
  } catch (err) {
    console.error("Create note failed:", err);
    return error(res, 500, "Internal server error");
  } finally {
    client.release();
  }
});

// -------------------- MARK LEAD AS LOST --------------------
export const markLeadAsLost = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { reason, status = "Lost" } = req.body;

    if (!id) return error(res, 400, "Lead ID is required.");

    const client = await db.connect();

    try {
      await client.query("BEGIN");

      // Step 1: Get statusId for "Lost"
      const statusRes = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS([status]),
        [status]
      );

      if (statusRes.rows.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Lost status not found.");
      }

      const lostStatusId = statusRes.rows[0].id;

      // Step 2: Directly update lead's status
      await client.query(
        `UPDATE ${TABLE.LEADS} SET "statusId" = $1 WHERE id = $2 AND "is_deleted" = FALSE`,
        [lostStatusId, id]
      );

      // Step 3: Create note (if reason provided)
      if (reason?.trim()) {
        await client.query(
          `INSERT INTO ${TABLE.NOTES} ("entityType", "entityId", "note", "statusId")
         VALUES ($1, $2, $3, $4)`,
          ["lead", id, reason.trim(), lostStatusId]
        );
      }

      await client.query("COMMIT");
      return response(res, 200, "Lead marked as lost successfully.");
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("markLeadAsLost failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

export const converLeadsAndSendEmails = asyncHandler(
  async (req: Request, res: Response) => {
    const { userIds } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return error(res, 400, "User IDs are required.");
    }

    const client = await db.connect();
    const failedUsers: string[] = [];

    try {
      const placeholders = userIds.map((_, i) => `$${i + 1}`).join(", ");
      const query = `SELECT id, "fullName", email, "leadType" FROM ${TABLE.LEADS} WHERE id IN (${placeholders}) AND "isConverted" = false AND "is_deleted" = FALSE`;
      const result = await client.query(query, userIds);

      for (const lead of result.rows) {
        try {
          await client.query("BEGIN");

          const existingUser = await client.query(AUTH.SELECT_BY_EMAIL, [
            lead.email,
          ]);
          if (existingUser.rows.length > 0) {
            await client.query("ROLLBACK");
            continue;
          }

          const userName = await generateUniqueUsername(lead.email);
          const accountTypeValue: AGENT_ACCOUNT_TYPE =
            lead.leadType === "agent"
              ? AGENT_ACCOUNT_TYPE.INDIVIDUAL
              : AGENT_ACCOUNT_TYPE.COMPANY_OR_AGENCY;

          const { query: profileQuery, values: profileValues } =
            AUTH.INSERT_INTO_PROFILE({
              email: lead.email,
              accountType: accountTypeValue,
              isProfileCompleted: false,
            });
          const profileResult = await client.query(profileQuery, profileValues);
          if (profileResult.rows.length === 0) {
            await client.query("ROLLBACK");
            failedUsers.push(lead.email);
            continue;
          }

          const password = generatePassword();
          const salt = await bcrypt.genSalt(10);
          const hashed = await bcrypt.hash(password, salt);

          const otp = generateOTP();
          const expireTime = new Date(Date.now() + 10 * 60 * 1000);

          const createLoginQuery = `
            INSERT INTO "${TABLE.LOGIN_TABLE}"
            ("profileId", username, "accountType", "passwordHash", "otp", "expireOn", "isActivated")
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *;`;
          const loginResult = await client.query(createLoginQuery, [
            profileResult.rows[0].id,
            userName,
            "email",
            hashed,
            otp,
            expireTime,
            true,
          ]);
          if (loginResult.rows.length === 0) {
            await client.query("ROLLBACK");
            failedUsers.push(lead.email);
            continue;
          }

          const roleName =
            accountTypeValue === AGENT_ACCOUNT_TYPE.INDIVIDUAL
              ? "agent"
              : "agency";
          const roleData = await client.query(AUTH.SELECT_ROLE_BY_NAME, [
            roleName,
          ]);
          if (roleData.rows.length === 0) {
            await client.query("ROLLBACK");
            failedUsers.push(lead.email);
            continue;
          }

          await client.query(AUTH.INSERT_INTO_LOGIN_ROLE, [
            loginResult.rows[0].id,
            roleData.rows[0].id,
            loginResult.rows[0].id,
          ]);

          const referralId = `faa-${nanoid(8)}`;
          await client.query(REFERRALS.INSERT_INTO_REFERRALS, [
            profileResult.rows[0].id,
            lead.email,
            referralId,
          ]);

          // Step 1: Get statusId for "Lost"
          const statusRes = await client.query(
            AUTH.SELECT_ACCOUNT_STATUS(["Converted"]),
            ["Converted"]
          );

          if (statusRes.rows.length === 0) {
            await client.query("ROLLBACK");
            return error(res, 400, "Converted status not found.");
          }

          const convertedStatusId = statusRes.rows[0].id;

          await client.query(
            `UPDATE ${TABLE.LEADS} SET "isConverted" = $1, "statusId" = $2 WHERE id = $3 AND "is_deleted" = FALSE`,
            [true, convertedStatusId, lead.id]
          );

          await sendWelcomeEmail(lead.fullName, lead.email, password);

          await client.query("COMMIT");
        } catch (userError) {
          await client.query("ROLLBACK");
          failedUsers.push(lead.email);
          console.error(`User processing failed for ${lead.email}:`, userError);
        }
      }
      if (failedUsers.length > 0) {
        return responseData(
          res,
          200,
          "Leads have failed to convert.",
          failedUsers
        );
      } else {
        return responseData(res, 200, "Leads converted successfully.");
      }
    } catch (err) {
      console.error("Unexpected error during batch user creation:", err);
      return error(res, 500, "Unexpected error occurred.");
    } finally {
      client.release();
    }
  }
);

/* ───────── bulk create controller ───────── */
export const bulkCreateLeads = asyncHandler(
  async (req: Request, res: Response) => {
    /* 1 ── read CSV payload ------------------------------------------------ */
    const csvPayload: Buffer | string | undefined =
      req.file?.buffer ??
      (req.file?.path && fs.readFileSync(req.file.path)) ??
      req.body.csvText;

    if (!csvPayload) return error(res, 400, "No CSV data provided");

    /* 2 ── parse ----------------------------------------------------------- */
    let rawRows: RawLeadRow[];
    try {
      rawRows = parseCsv(csvPayload);
    } catch (e) {
      return error(res, 400, "Malformed CSV: " + (e as Error).message);
    }
    if (!rawRows.length) return error(res, 400, "CSV contained no leads");

    /* 3 ── in-memory cleanup & defaults ----------------------------------- */
    const invalidRows: number[] = [];
    rawRows.forEach((row, idx) => {
      Object.keys(row).forEach((k) => {
        const v = row[k as keyof RawLeadRow];
        if (typeof v === "string") row[k as keyof RawLeadRow] = v.trim() as any;
      });

      if (!row.Email || !row.Type) invalidRows.push(idx + 2);

      row.Status = row.Status || "New";
      row.Type = row.Type?.toLowerCase() === "agency" ? "agency" : "agent";
    });
    if (invalidRows.length)
      return error(
        res,
        400,
        `Row(s) ${invalidRows.join(
          ", "
        )} missing required 'Email' or 'Type' field`
      );

    /* 4 ── fetch: status-id map *and* existing e-mails --------------------- */
    const client = await db.connect();
    try {
      /* 4a  status-id map */
      const distinctStatuses = [...new Set(rawRows.map((r) => r.Status!))];
      const { rows: statusRows } = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(distinctStatuses),
        distinctStatuses
      );
      const statusDict: Record<string, number> = Object.fromEntries(
        statusRows.map((r) => [r.name.toLowerCase(), r.id])
      );

      /* 4b  current e-mails in DB (case-insensitive) */
      const distinctEmails = [
        ...new Set(rawRows.map((r) => r.Email.toLowerCase())),
      ];
      const { rows: emailRows } = await client.query<{ email: string }>(
        "SELECT LOWER(email) AS email FROM look.leads WHERE LOWER(email) = ANY($1::text[]) AND \"is_deleted\" = FALSE",
        [distinctEmails]
      );
      const existingEmailSet = new Set(emailRows.map((r) => r.email));

      /* 5 ── iterate rows -------------------------------------------------- */
      const successes: { row: number; id: number }[] = [];
      const failures: { row: number; reason: string }[] = [];

      for (let i = 0; i < rawRows.length; i++) {
        const r = rawRows[i];
        const csvRow = i + 2; // header is line 1

        /* 5a  validate against DB duplicates just fetched */
        if (existingEmailSet.has(r.Email.toLowerCase())) {
          failures.push({ row: csvRow, reason: "Email already exists" });
          continue;
        }

        /* 5b  per-row validation */
        if (!isValidLeadType(r.Type!)) {
          failures.push({ row: csvRow, reason: "Invalid Type (agent|agency)" });
          continue;
        }
        const statusId = statusDict[r.Status!.toLowerCase()];
        if (!statusId) {
          failures.push({
            row: csvRow,
            reason: `Unknown Status '${r.Status}'`,
          });
          continue;
        }

        /* 5c  call the stored procedure (22 params) */
        const params = [
          2, // fnid
          null,
          r.Name ?? null, //  3 fullName
          r.Email, //  4 email
          r.Phone ?? null, //  5 phone
          r.LicenseNumber, //  6 licenseNumber
          r.Company ?? null, //  7 company
          r.Type, //  8 leadType
          statusId, //  9 statusId
          r.Source, // 10 source
          false,
          null,
          null,
          null,
          null,
          null,
          null,
          null, // p_page_no
          null, // p_page_size
          null, // p_search_text
          null, // p_deleted_by
        ];

        try {
          const { rows } = await client.query(
            `SELECT * FROM look.sp_leads_notes(${params
              .map((_, idx) => `$${idx + 1}`)
              .join(", ")})`,
            params
          );
          const result = rows[0]?.sp_leads_notes;
          if (!result || result.type === "error")
            throw new Error(result?.message || "Unknown error");

          successes.push({ row: csvRow, id: result.data.id });
          /* keep the set up-to-date to prevent duplicates later in the same batch */
          existingEmailSet.add(r.Email.toLowerCase());
        } catch (e: any) {
          /* unique-index race-condition fallback */
          if (e.code === "23505") {
            failures.push({ row: csvRow, reason: "Email already exists" });
          } else {
            failures.push({ row: csvRow, reason: e.message });
          }
        }
      }

      /* 6 ── respond ------------------------------------------------------ */
      return responseData(res, 207, "Bulk upload summary", {
        created: successes.length,
        failed: failures.length,
        successes,
        failures,
      });
    } finally {
      client.release();
    }
  }
);

export const getNotesOfLead = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params; 
    if (!id) return error(res, 400, "Lead ID is required.");

    const client = await db.connect();

    try {
      await client.query("BEGIN");

      // Step 1: Get statusId for "Lost" 
    
     const notes =   await client.query(
          `SELECT * FROM ${TABLE.NOTES}  WHERE  "entityId" = $1 `,
          [  id,  ]
        );
     

      await client.query("COMMIT");
      if( !notes.rows.length) {
        return error(res, 404, "No notes found for this lead."); 
       }else{

         return responseData(res, 200, "Notes  Fetched." , notes.rows);
       }
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Notes  Fetched failed:", err);
      return error(res, 500, "Internal server error");
    } finally {
      client.release();
    }
  }
);

