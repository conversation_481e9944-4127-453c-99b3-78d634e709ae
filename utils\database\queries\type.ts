import { TABLE } from "../table";

export const TYPE = {
  CREATE_TYPE_MISSION: `INSERT INTO ${TABLE.TYPE} (name, description) VALUES ($1, $2) RETURNING *`,
  GET_BY_NAME_BY_PARENT: `SELECT * FROM look.type WHERE name = $1;`,
  GET_ALL_LOCATIONS: `SELECT * FROM list.location ORDER BY name ASC;`,
  GET_ALL_LANGUAGES: `SELECT * FROM list.languages ORDER BY name ASC;`,
  GET_ALL_SERVICES: `SELECT * FROM list.services`,
  GET_SHARED_PARENT_SERVICES: `SELECT * FROM list.services WHERE "parentId" IS NULL AND "statusId" = 1;`, 
  GET_ALL_SERVICE_BY_PARENT_AND_GROUP: `
    SELECT id, name, "parentId", "group"
    FROM list.services
    WHERE "parentId" = $1 AND "group" = $2;`,
  GET_PARENT_SERVICES_BY_IDs: `SELECT id, name FROM list.services WHERE id = ANY($1::int[]);`,
  GET_LOCATION_BY_ID: `SELECT * FROM list.location WHERE id = $1;`,
};
