import nodemailer from "nodemailer";
import transporter from ".";

export const sendPropertyStatusUpdateEmail = async (
  propertyName: string,
  propertyId: number,
  ownerName: string,
  ownerEmail: string,
  newStatus: string,
  reason?: string
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: ownerEmail,
    subject: "Property Blocked - FindAnyAgent",
    html: await propertyStatusUpdateEmailTemplate(propertyName, propertyId, ownerName, newStatus, reason),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Property status update email sent to: ${ownerEmail} for property: ${propertyName}`);
  } catch (error) {
    console.error("Error sending property status update email:", error);
    // Don't throw error to avoid disrupting the status update flow
  }
};

export const propertyStatusUpdateEmailTemplate = async (
  propertyName: string,
  propertyId: number,
  ownerName: string,
  newStatus: string,
  reason?: string
): Promise<string> => {
  const isBlocked = newStatus.toLowerCase() === 'blocked';
  const statusColor = '#dc3545'; // Red color for blocking action
  const statusMessage = 'Your property listing has been blocked by our admin team.';

  return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Property Status Update - FindAnyAgent</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <!-- === Full-width wrapper (centers the card) === -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <!-- === Centered card, max 600px === -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <!-- Header -->
                <h1 style="margin:0 0 20px 0;font-size:22px;color:${statusColor};">
                  Property Blocked Notification
                </h1>

                <!-- Content -->
                <div style="font-size:16px;">
                  <p>Dear ${ownerName},</p>

                  <p>${statusMessage}</p>

                  <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid ${statusColor};">
                    <p style="margin: 0 0 8px 0;"><strong>Property Name:</strong> ${propertyName}</p>
                    ${reason ? `<p style="margin: 0;"><strong>Reason:</strong> ${reason}</p>` : ''}
                  </div>

                  <p><strong>What does this mean?</strong></p>
                  <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Your property listing is no longer visible to potential buyers/renters</li>
                    <li>You cannot modify or update this property listing</li>
                    <li>No new inquiries can be made for this property</li>
                  </ul>

                  <p><strong>What can you do?</strong></p>
                  <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>Review our property listing guidelines and terms of service</li>
                    <li>Contact our support team if you believe this was done in error</li>
                    <li>Address any policy violations mentioned in the reason above</li>
                  </ul>

                  <p>If you have any questions or concerns about this status change, please don't hesitate to contact our support team.</p>

                  <div style="background-color: #e9ecef; padding: 12px; border-radius: 4px; margin: 20px 0;">
                    <p style="margin: 0; font-size: 14px;">
                      <strong>Need Help?</strong><br>
                      Email: <EMAIL><br>
                    </p>
                  </div>
                </div>

                <!-- Footer -->
                <div style="margin-top:30px;padding-top:20px;border-top:1px solid #e9ecef;
                            font-size:14px;color:#6c757d;">
                  <p style="margin:0 0 8px 0;">
                    Best regards,<br>
                    <strong>FindAnyAgent Admin Team</strong>
                  </p>

                  <p style="margin:0;font-size:12px;color:#adb5bd;">
                    This is an automated notification. Please do not reply to this email.
                  </p>
                </div>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>`;
};