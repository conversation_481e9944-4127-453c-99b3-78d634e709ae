import { ProfileRepository } from "../repo/ProfileRepository";
import { AgentDetailsRepository } from "../repo/agent-details-repository";
import { AgentLanguagesSpecializationRepository } from "../repo/agent-languages-specialization-repository";
import { StatusRepository } from "../repo/look/status-repository";
import { ProfileStatus } from "../utils/enums/profile-status.enum";
import bcrypt from "bcryptjs";
 
export class ProfileService {
  private repo = new ProfileRepository();
  private agentDetailsRepo = new AgentDetailsRepository();
  private agentLanguagesSpecRepo = new AgentLanguagesSpecializationRepository();
  private statusRepo = new StatusRepository();

  async getProfileById(id: number) {
    const profile = await this.repo.getProfileById(id);
    if (!profile) throw new Error("Profile not found");
    return profile;
  }


  async updateProfileBasic(id: number, profileImage: string, firstName: string, lastName: string, middleName: string, nationality: string, gender: string) {
    try {
      const profileData = await this.repo.updateProfileBasic(id, profileImage, firstName, lastName, middleName);
      const agentDetailsData = await this.agentDetailsRepo.updateAgentDetailsGender(id, nationality, gender);
      
      return {
        success: true,
        message: "Profile basic information updated successfully",
        data: {
          profile: profileData,
          agentDetails: agentDetailsData
        }
      };
    } catch (error) {
      throw new Error(`Failed to update profile basic information: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updateProfileContact(id: number, locationId: number, address: string) {
    try {
      const updatedData = await this.repo.updateProfileContact(id, locationId, address);
      return {
        success: true,
        message: "Profile contact information updated successfully",
        data: updatedData
      };
    } catch (error) {
      throw new Error(`Failed to update profile contact information: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
 
  // async updateProfilePassword(id: number, oldPassword: string, newPassword: string,  ) { 
  //   const previousHashedPassword = await this.repo.getPreviousPassword(id);
  //   if (previousHashedPassword) {
  //     const isMatch = await bcrypt.compare(oldPassword, previousHashedPassword);
  //     if (!isMatch) throw new Error("Old password is incorrect");

  //     const salt = await bcrypt.genSalt(10);
  //     const hashedPassword = await bcrypt.hash(newPassword, salt);

  //     try {
  //       const updatedData = await this.repo.updateProfilePassword(id, hashedPassword);
  //       return {
  //         success: true,
  //         message: "Password updated successfully",
  //         data: { id: updatedData.id, profileId: updatedData.profileId }
  //       };
  //     } catch (error) {
  //       throw new Error(`Failed to update password: ${error instanceof Error ? error.message : 'Unknown error'}`);
  //     }
  //   }else{
  //     throw new Error("Previous password not found");
  //   }
  // }

  async updateProfileStatus(id: number, status: string) {
    // Validate that the status is a valid enum value
    if (!Object.values(ProfileStatus).includes(status as ProfileStatus)) {
      throw new Error("Invalid status");
    }

    try {
      // Get the status ID from the database
      const statusId = await this.statusRepo.getStatusIdByStatusName(status);
      
      if (!statusId) {
        throw new Error("Status not found in database");
      }

      const updatedData = await this.repo.updateProfileStatus(id, statusId);
      return {
        success: true,
        message: "Profile status updated successfully",
        data: updatedData
      };
    } catch (error) {
      throw new Error(`Failed to update profile status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

    async updateProfessionalInfo(id: number, details: string, experience: number, languages: number[], specialization: string[]) {
      try {
        const profileData = await this.repo.updateProfessionalInfo(id, details, experience);
        await this.agentLanguagesSpecRepo.updateAgentLanguagesAndSpecializations(id, languages, specialization);
        
        return {
          success: true,
          message: "Professional information updated successfully",
          data: profileData
        };
      } catch (error) {
        throw new Error(`Failed to update professional information: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  
  }
