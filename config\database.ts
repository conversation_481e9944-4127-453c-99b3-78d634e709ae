import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import { config } from "dotenv"; 
config();

const PORT = parseInt(process.env.DATABASE_PORT || "5432", 10);

const DATABASE_URL = `postgres://${process.env.DATABASE_USER}:${process.env.DATABASE_PASSWORD}@${process.env.DATABASE_HOST}:${PORT}/${process.env.DATABASE_NAME}`;

const db = new Pool({
  connectionString: DATABASE_URL,
});

db.on("connect", async (client) => {
  try {
    await client.query("SET search_path TO prf, sec, look,agn, public;");
    console.log("Database connected successfully! Search path set.");
  } catch (err) {
    console.error("Error setting search path:", err);
  }
});


db.on("error", (err) => {
  console.error("Database connection error:", err.stack);
});

const drizzleDb = drizzle(db);

export { db, drizzleDb };

