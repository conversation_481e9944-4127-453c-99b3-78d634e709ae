import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
    await knex.schema.alterTable("prf.events", (table) => {
        table
            .integer("profileId")
            .nullable()
            .references("id")
            .inTable("prf.profile");
    });

    await knex.schema.alterTable("agn.properties", (table) => {
        table
            .integer("profileId")
            .nullable()
            .references("id")
            .inTable("prf.profile");
    });

    await knex.schema.alterTable("agn.projects", (table) => {
        table
            .integer("profileId")
            .nullable()
            .references("id")
            .inTable("prf.profile");
    });
}

export async function down(knex: Knex): Promise<void> {
    await knex.schema.alterTable("prf.events", (table) => {
        table.dropColumn("profileId");
    });

    await knex.schema.alterTable("agn.properties", (table) => {
        table.dropColumn("profileId");
    });

    await knex.schema.alterTable("agn.projects", (table) => {
        table.dropColumn("profileId");
    });
}