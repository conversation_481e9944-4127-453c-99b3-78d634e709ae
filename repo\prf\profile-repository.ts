import { PoolClient } from "pg";
import { AUTH } from "../../utils/database/queries/auth";

export class ProfileRepository {

    async updateProfile(
        firstName: string,
        middleName: string | null,
        lastName: string,
        phone: string,
        emiratesId: string[] | null | [],
        statusId: number,
        profileImage: string,
        locationId: number,
        profileId: number,
        dbClient: PoolClient
    ) {
        const query = `
    UPDATE prf.profile
    SET "firstName" = $1,
        "middleName" = $2,
        "lastName" = $3,
        "phone" = $4,
        "emiratesId" = $5,
        "statusId" = $6,
        "profileImage" = $7,
        "locationId" = $8
    WHERE id = $9
    RETURNING *;
  `;
        const values = [
            firstName,
            middleName,
            lastName,
            phone,
            emiratesId,
            statusId,
            profileImage,
            locationId,
            profileId,
        ];

        return dbClient.query(query, values);
    }

    async updateCompanyProfile(values: any[], client: PoolClient) {
        const profileQuery = `
          UPDATE prf.profile
          SET
              "phone" = $1,
              "emiratesId" = $2,
              "statusId" = $3,
              "profileImage" = $4
          WHERE id = $5
          RETURNING *;
          `;

        await client.query(profileQuery, values);
    }

    async updateProfileStatus(status: boolean, profileId: number, client: PoolClient) {
        await client.query(
            `UPDATE "profile" SET "isProfileCompleted" = $1 WHERE id = $2`,
            [status, profileId]
        );
    }

    async getProfileById(profileId: number, client: PoolClient) {
        return await client.query(AUTH.SELECT_BY_ID, [profileId]);
    }

}