import { StatusRepository } from "../../repo/look/StatusRepository";

export class StatusService {
  private statusRepository = new StatusRepository();

  async getStatusDetails(status: string[]): Promise<any> {
    return this.statusRepository.getStatusDetailsByStatusName(status);
  }

  async getByIdStatusDetails(status: number): Promise<any> {
    return this.statusRepository.getStatusDetailsById(status);
  }
}
