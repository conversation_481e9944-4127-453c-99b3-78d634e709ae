import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { response, responseData, error as errorResponse } from "../../../utils/response";
import { TICKETS } from "../../../utils/database/queries/tickets";
import { sendTicketNotificationEmail, sendTicketCreationNotification } from "../../../utils/services/nodemailer/sendTicketNotificationEmail";


// GET /api/v1/admin/tickets?status=&priority=&category=&search=
export const getAllTickets = asyncHandler(async (req: Request, res: Response) => {
  const { status, priority, category, search } = req.query;
  const { rows } = await db.query(TICKETS.FILTER_TICKETS, [
    status || null,
    priority || null,
    category || null,
    search || null,
  ]);
  return responseData(res, 200, "Tickets fetched successfully", rows);
});



// GET /api/v1/admin/tickets/:id
export const getTicketById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { rows } = await db.query(TICKETS.GET_TICKET_BY_ID, [id]);
  if (!rows.length) return errorResponse(res, 404, "Ticket not found");
  // Get responses
  const { rows: responses } = await db.query(TICKETS.GET_RESPONSES_BY_TICKET_ID, [id]);
  return responseData(res, 200, "Ticket fetched successfully", { ...rows[0], responses });
});



// POST /api/v1/admin/tickets
export const createTicket = asyncHandler(async (req: Request, res: Response) => {
  const { agent_id, agent_name, agent_email, title, priority, category, description, assigned_to } = req.body;
  
  // Validate required fields (agent_id is optional for unregistered users)
  if (!agent_name || !agent_email || !title || !priority || !category || !description) {
    return errorResponse(res, 400, "Missing required fields: agent_name, agent_email, title, priority, category, description");
  }

  // Validate priority and category
  const validPriorities = ['low', 'medium', 'high', 'urgent'];
  const validCategories = ['General', 'Technical', 'Payment', 'Account', 'Feature Request', 'Bug Report', 'Other'];
  
  if (!validPriorities.includes(priority)) {
    return errorResponse(res, 400, `Invalid priority value. Must be one of: ${validPriorities.join(', ')}`);
  }

  if (!validCategories.includes(category)) {
    return errorResponse(res, 400, `Invalid category value. Must be one of: ${validCategories.join(', ')}`);
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(agent_email)) {
    return errorResponse(res, 400, "Invalid email format");
  }

  // If agent_id is provided, verify the agent exists
  if (agent_id) {
    const agent = await db.query(
      `SELECT p.id FROM prf.profile p
       LEFT JOIN sec.login l ON p.id = l."profileId"
       LEFT JOIN sec.loginrole lr ON l.id = lr."loginId"
       LEFT JOIN sec.roles r ON lr."roleId" = r.id
       WHERE p.id = $1 AND r.name = ANY($2)`,
      [agent_id, ['agent', 'agency', 'agencyAdmin']]
    );
    if (!agent.rows.length) {
      return errorResponse(res, 400, "Invalid agent_id provided");
    }
  }

  // created_by is the admin user making the request
  const created_by = req.user?.id || null;
  
  // Get admin details for email notification
  let adminEmail = null;
  let adminName = null;
  
  if (created_by) {
    try {
      const adminResult = await db.query(
        `SELECT CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName") AS name, p.email 
         FROM prf.profile p WHERE p.id = $1`,
        [created_by]
      );
      
      if (adminResult.rows.length > 0) {
        adminName = adminResult.rows[0].name;
        adminEmail = adminResult.rows[0].email;
      }
    } catch (error) {
      console.error('Failed to get admin details:', error);
    }
  }
  
  const { rows } = await db.query(TICKETS.CREATE_TICKET, [
    agent_id || null, // Can be null for unregistered users
    agent_name,
    agent_email,
    title,
    description,
    priority,
    category,
    'open',
    created_by,
    assigned_to || null
  ]);

  // Send notification emails
  try {
    await sendTicketCreationNotification(
      agent_name,
      agent_email,
      rows[0].id.toString(),
      title,
      priority,
      category,
      description,
      !!agent_id, // true if agent_id is provided (registered user), false otherwise
      adminEmail,
      adminName
    );
  } catch (emailError) {
    console.error('Failed to send notification email:', emailError);
    // Don't fail the ticket creation if email fails
  }

  return responseData(res, 201, "Ticket created successfully", rows[0]);
});



// PUT /api/v1/admin/tickets/:id
export const updateTicket = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status, priority, agent_id, agent_name, agent_email, description, assigned_to } = req.body;
  // Validate ticket exists
  const { rows: existing } = await db.query(TICKETS.GET_TICKET_BY_ID, [id]);
  if (!existing.length) return errorResponse(res, 404, "Ticket not found");
  // Validate agent if changed
  if (agent_id) {
    const agent = await db.query(
      `SELECT p.id FROM prf.profile p
       LEFT JOIN sec.login l ON p.id = l."profileId"
       LEFT JOIN sec.loginrole lr ON l.id = lr."loginId"
       LEFT JOIN sec.roles r ON lr."roleId" = r.id
       WHERE p.id = $1 AND r.name = ANY($2)`,
      [agent_id, ['agent', 'agency', 'agencyAdmin']]
    );
    if (!agent.rows.length) return errorResponse(res, 400, "Invalid agent_id");
  }
  const { rows } = await db.query(TICKETS.UPDATE_TICKET, [
    status || existing[0].status,
    priority || existing[0].priority,
    agent_id || existing[0].agent_id,
    agent_name || existing[0].agent_name,
    agent_email || existing[0].agent_email,
    description || existing[0].description,
    assigned_to || existing[0].assigned_to,
    id
  ]);

  // Get admin details for email notification
  let adminEmail = null;
  let adminName = null;
  
  if (req.user?.id) {
    try {
      const adminResult = await db.query(
        `SELECT CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName") AS name, p.email 
         FROM prf.profile p WHERE p.id = $1`,
        [req.user.id]
      );
      
      if (adminResult.rows.length > 0) {
        adminName = adminResult.rows[0].name;
        adminEmail = adminResult.rows[0].email;
      }
    } catch (error) {
      console.error('Failed to get admin details:', error);
    }
  }

  // Send notification to agent
  try {
    await sendTicketNotificationEmail(
      "Support Ticket Updated",
      `Your support ticket (ID: ${id}) has been updated. Status: ${status || existing[0].status}, Priority: ${priority || existing[0].priority}`,
      agent_email || existing[0].agent_email
    );
  } catch (emailError) {
    console.error('Failed to send agent notification email:', emailError);
  }

  // Send confirmation to admin
  if (adminEmail && adminName) {
    try {
      await sendTicketNotificationEmail(
        "Support Ticket Updated - Confirmation",
        `You have successfully updated support ticket (ID: ${id}) for ${agent_name || existing[0].agent_name}. Status: ${status || existing[0].status}, Priority: ${priority || existing[0].priority}`,
        adminEmail
      );
    } catch (emailError) {
      console.error('Failed to send admin notification email:', emailError);
    }
  }
  return responseData(res, 200, "Ticket updated", rows[0]);
});



// DELETE /api/v1/admin/tickets/:id (soft delete)
export const deleteTicket = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  // Validate ticket exists
  const { rows: existing } = await db.query(TICKETS.GET_TICKET_BY_ID, [id]);
  if (!existing.length) return errorResponse(res, 404, "Ticket not found");
  const { rows } = await db.query(TICKETS.SOFT_DELETE_TICKET, [id]);
  return response(res, 200, "Ticket closed successfully");
});



// GET /api/v1/admin/tickets/:id/responses
export const getResponsesByTicketId = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { rows } = await db.query(TICKETS.GET_RESPONSES_BY_TICKET_ID, [id]);
  return responseData(res, 200, "Responses fetched", rows);
});



// POST /api/v1/admin/tickets/:id/responses
export const createResponse = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { agent_id, response: resp } = req.body;
  if (!resp) return errorResponse(res, 400, "Response text required");

  // Validate ticket exists
  const { rows: ticketRows } = await db.query(TICKETS.GET_TICKET_BY_ID, [id]);
  if (!ticketRows.length) return errorResponse(res, 404, "Ticket not found");

  // Only the ticket's agent can comment as agent
  if (agent_id) {
    if (ticketRows[0].agent_id !== Number(agent_id)) return errorResponse(res, 403, "Only the ticket's agent can comment");
  }

  // For admin responses, use the logged-in user's ID
  const admin_id = req.user?.id || null;

  // Validate admin if admin_id is present
  if (admin_id) {
    const admin = await db.query(
      `SELECT p.id FROM prf.profile p
       LEFT JOIN sec.login l ON p.id = l."profileId"
       LEFT JOIN sec.loginrole lr ON l.id = lr."loginId"
       LEFT JOIN sec.roles r ON lr."roleId" = r.id
       WHERE p.id = $1 AND (r.name = $2 OR r.name = $3)`,
      [admin_id, 'admin', 'superAdmin']
    );
    if (!admin.rows.length) return errorResponse(res, 403, "Invalid admin credentials");
  }

  const { rows } = await db.query(TICKETS.CREATE_RESPONSE, [id, agent_id || null, admin_id || null, resp]);

  // Get the created response with admin/agent names
  const { rows: responseWithNames } = await db.query(TICKETS.GET_RESPONSE_BY_ID, [rows[0].id]);

  // Get admin details for email notification
  let adminEmail = null;
  let adminName = null;
  
  if (admin_id) {
    try {
      const adminResult = await db.query(
        `SELECT CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName") AS name, p.email 
         FROM prf.profile p WHERE p.id = $1`,
        [admin_id]
      );
      
      if (adminResult.rows.length > 0) {
        adminName = adminResult.rows[0].name;
        adminEmail = adminResult.rows[0].email;
      }
    } catch (error) {
      console.error('Failed to get admin details:', error);
    }
  }

  // Send notifications
  if (agent_id) {
    try {
      await sendTicketNotificationEmail(
        "New Response to Your Ticket",
        `A new response has been added to your ticket (ID: ${id}).`,
        ticketRows[0].agent_email
      );
    } catch (emailError) {
      console.error('Failed to send agent notification email:', emailError);
    }
  }
  
  if (admin_id && adminEmail) {
    try {
      await sendTicketNotificationEmail(
        "Response Added to Ticket - Confirmation",
        `You have successfully added a response to ticket (ID: ${id}) for ${ticketRows[0].agent_name}.`,
        adminEmail
      );
    } catch (emailError) {
      console.error('Failed to send admin notification email:', emailError);
    }
  }
  return responseData(res, 201, "Response added", responseWithNames[0]);
});



// GET /api/v1/admin/tickets/summary
export const getTicketSummary = asyncHandler(async (req: Request, res: Response) => {
  const { rows } = await db.query(`SELECT status, COUNT(*) as count FROM prf.tickets GROUP BY status`);
  const summary: Record<string, number> = { open: 0, "in-progress": 0, resolved: 0, closed: 0 };
  rows.forEach((row: any) => {
    if (row.status in summary) {
      summary[row.status as keyof typeof summary] = Number(row.count);
    }
  });
  return responseData(res, 200, "Ticket summary fetched", summary);
});



// GET /api/v1/admin/tickets/meta
export const getTicketMeta = asyncHandler(async (req: Request, res: Response) => {
  const status = ["open", "in-progress", "resolved", "closed"];
  const priority = ["low", "medium", "high", "urgent"];
  const category = ["General", "Technical", "Payment", "Account", "Feature Request", "Bug Report"];
  return responseData(res, 200, "Ticket meta fetched", { status, priority, category });
}); 