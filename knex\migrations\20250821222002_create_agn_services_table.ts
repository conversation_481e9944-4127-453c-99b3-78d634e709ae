import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("agn.services", (table) => {
    // From screenshot
    table.increments("id").primary(); // Primary Key
    table.integer("profileId").notNullable();
    table.integer("agencyId").nullable();
    table.integer("currencyId").nullable();
    table.boolean("isRemote").notNullable().defaultTo(false);
    table.boolean("isFree").notNullable().defaultTo(false);
    table.decimal("price").nullable();

    table.string("title").nullable();
    table.integer("experience").nullable();
    table.integer("duration").nullable();
    table.boolean("isDeleted").notNullable().defaultTo(false);
    table.string("specialOffer").nullable();
    table.string("websiteUrl").nullable();
    table.text("description").nullable();

    table.text("address").nullable();
    table.text("json").nullable();
    table.integer("statusId").notNullable();
    table.integer("createdBy").notNullable();
    table
      .timestamp("createdOn", { useTz: true })
      .notNullable()
      .defaultTo(knex.fn.now());
    table.integer("modifiedBy").nullable();
    table.timestamp("modifiedOn", { useTz: true }).nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists("agn.services");
}
