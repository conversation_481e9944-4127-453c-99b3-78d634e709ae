import "reflect-metadata";
import express, { Express, Request, Response } from "express";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swaggerConfig";
import dotenv from "dotenv";
import cors from "cors";
import { morganConfig } from "./config/morgan";
import corsOptions from "./config/cors";
import Routes from "./routes";
import cookieParser from 'cookie-parser';
import { sendEmailToSalespersonsCron, startDocumentExpiryCron } from "./cron/documentExpiryChecker";

const app: Express = express();

dotenv.config();
morganConfig(app);
app.use(cookieParser());
app.use(cors(corsOptions));
app.use(express.json());
app.use(express.static("public"));

app.get("/", (req, res) => {
  res.send("🚀 Findanyagent backend APIs are Running!");
});

const PORT = process.env.PORT || 3000;

// Swagger setup
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

app.use("/api/v1", Routes);

app.use((req: Request, res: Response) => {
  res
    .status(404)
    .json({ status: 404, success: false, message: "Route not found" });
});

app.listen(PORT, () => {
  console.log(`[server]: Server is running at http://localhost:${PORT}`);
  console.log(`Swagger docs available at http://localhost:${PORT}/api-docs`);
  startDocumentExpiryCron();
  sendEmailToSalespersonsCron();
});
