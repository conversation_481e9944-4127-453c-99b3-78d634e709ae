import { Request, Response } from "express"; 
import { db } from "../../config/database";
 
import {
  errorCatchResponse,
  errorResponse,
  response,
  responseData,
} from "../../utils/response";
 
import asyncHandler from "../../middleware/trycatch"; 
import { TYPE } from "../../utils/database/queries/type";
 

export const createTypeAndMission = asyncHandler(
  async (req: Request, res: Response) => {
    const { mission, parentId, description } = req.body;

    try {
      if (!mission) {
        return errorResponse(res, "Mission is required");
      }

      const typeExists = await db.query(TYPE.CREATE_TYPE_MISSION, [
        mission,
        description || null,
      
      ]);

      if (typeExists.rowCount === 0) {
        return errorResponse(res, "Mission not Created");
      }

      return response(res, 200, "Type and Mission created successfully");
    } catch (error) {
      console.error("Error creating type and mission:", error);
      return errorCatchResponse(
        res,
        "An error occurred while creating the type and mission"
      );
    }
  }
);
