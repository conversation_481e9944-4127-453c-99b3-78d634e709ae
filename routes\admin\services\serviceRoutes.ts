import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { ServiceController } from "../../../controller/admin/services/ServiceController";

const router = Router();
const upload = storageData("documents");

const serviceController = new ServiceController();

// Multer fields for create/update
const fields = [{ name: "servicePhotos", maxCount: 10 }];

// LIST services (table + header counts via query params)
router.get("/", serviceController.getAllServices);

// GET one service
router.get("/:id", serviceController.getServiceById);

// UPDATE status (expects { statusId } in body)
router.put("/:id/status", upload.none(), serviceController.updateStatus);

// DELETE service
router.delete("/:id", serviceController.deleteService);

router.get(
  "/get/status-locations-and-types",
  serviceController.getFilterOptionsAndTypes
);

// create a note for a services
router.post("/:id/notes", upload.none(), serviceController.createNote);

// GET notes of a services
router.get("/note/:id", serviceController.getNotes);

export default router;
