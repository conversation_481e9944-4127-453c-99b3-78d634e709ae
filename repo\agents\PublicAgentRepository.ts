import { db } from "../../config/database";
import { PropertyQueries } from "../../utils/database/queries/PropertyQueries";

export class PublicAgentRepository {
  async getAllAgents() {
    const { rows } = await db.query(PropertyQueries.GET_ALL_AGENTS);
    return rows;
  }

  async getAgentIdByProfile(profileId: number) {
    const result = await db.query(PropertyQueries.GET_AGENCY_ID_BY_PROFILE_ID, [
      profileId,
    ]);

    return result;
  }

  async getPropertiesByAgentId(agentId: number) {
    const { rows } = await db.query(
      PropertyQueries.GET_PROPERTIES_BY_AGENT_ID,
      [agentId]
    );
    return rows;
  }
}
