#!/bin/bash
set -e  # Exit immediately if a command exits with a non-zero status
set -x  # Print commands and their arguments as they are executed

echo "=== Starting restart script ==="

# Activate virtualenv
source /home/<USER>/venv/bin/activate

# Go to app dir
cd /home/<USER>/app

# Run migrations
echo "Running migrations..."
python manage.py migrate --noinput

# Restart service
echo "Restarting Gunicorn..."
sudo systemctl restart gunicorn

echo "=== Restart completed successfully ==="
