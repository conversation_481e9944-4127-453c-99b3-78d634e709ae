import { array, z } from "zod";
import { CompanyIndustrySchema } from "./company-industries";
import { CompanyOperationAreaDtoSchema } from "./company-operation-area";

export const CompanyProfileSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  companyPhone: z.string().min(1, "Company phone is required"),
  profilePhoto: z.string().optional(),

  operationArea: z.array(CompanyOperationAreaDtoSchema),
  industries: z.array(CompanyIndustrySchema),

  nationality: z.string().min(1, "Nationality is required"),

  emiratesId: z.string().array().optional(),
  passport: z.string().array().optional(),
  visa: z.string().array().optional(),

  inviteTeamMembers: z.boolean().optional().nullable,
  inviteAgents: z.string().array().min(1, "Invite agents is required"),

  supportingDocs: z.array(z.string()).optional(),
  referralId: z.string().nullable().optional(),

  termsAgree: z.boolean(),
  accuracyConfirm: z.boolean(),
});

export type CompanyProfileDto = z.infer<typeof CompanyProfileSchema>;
