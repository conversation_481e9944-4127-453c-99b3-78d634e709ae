import { TABLE } from "../table";

 
export const monthlyReferralReportQuery = `
  SELECT 
    r.id AS referral_id,
    TO_CHAR(r."created_at", 'YYYY-MM-DD') AS referred_date,
    sp.full_name AS salesperson_name,
    sp.email AS salesperson_email,
    u."firstName" AS user_firstname,
    u."lastName" AS user_lastname,
    u.email AS user_email,
    u.phone AS user_phone,
    COALESCE(s."package", 'Free') AS subscription_plan_name,
    COALESCE(s.price, 0) AS subscription_price,
    COALESCE(c."commissionAmount", 0) AS commissionAmount,
    NULL AS commission_status_id,
    CASE 
      WHEN c."paymentMethod" IS NOT NULL OR s.id IS NULL THEN 'Paid'
      ELSE ''
    END AS commission_status_name,  
    c."paymentAt" AS commission_paid_at
  FROM "referralRecords" r
  LEFT JOIN "commission" c ON c."referralId" = r.id
  LEFT JOIN look.status st ON st.id = c."status"
  LEFT JOIN look.salespersons sp ON sp.id = r."salesPersonId"
  LEFT JOIN "${TABLE.PROFILE_TABLE}" u ON u.id = r."userId"
  LEFT JOIN list.subscription s ON s.id = c."subscriptionId"
`;
