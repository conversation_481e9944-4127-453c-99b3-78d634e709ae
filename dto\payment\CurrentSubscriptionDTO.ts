export type CurrentSubscriptionDTO = {
  plan: {
    name: string | null;
    price: number | null; // in base unit (e.g., AED 199)
    interval: "month" | "year" | null;
    currency: string | null;
    status: string | null; // DB status name
  };
  nextBilling: {
    dateISO: string | null; // from Stripe current_period_end / upcoming invoice
    amount: number | null; // upcoming total (in base unit)
    currency: string | null;
    autoRenew: boolean | null; // cancel_at_period_end === false
  };
  paymentMethod: {
    brand: string | null;
    last4: string | null;
    expMonth: number | null;
    expYear: number | null;
    funding: string | null;
  } | null;
};
