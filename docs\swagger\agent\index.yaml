/agent/complete-profile:
  put:
    summary: Update agent profile
    description: Updates the agent profile with personal details, industries, documents, and consent information.
    tags: [Agent]
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              firstName:
                type: string
                example: <PERSON>za
              middleName:
                type: string
                example: <PERSON>
              lastName:
                type: string
                example: Sheikh
              nationality:
                type: string
                example: "223" # Example country code
              gender:
                type: string
                example: Male
              phoneNumber:
                type: string
                example: +971501234567
              profilePhoto:
                type: string
                format: uri
                example: https://example.com/photos/ali.jpg
              industries:
                type: array
                items:
                  type: object
                  properties:
                    industryId:
                      type: string
                      example: "25"
                    industryName:
                      type: string
                      example: Beauty
                    roles:
                      type: array
                      items:
                        type: object
                        properties:
                          roleId:
                            type: string
                            example: other
                          roleName:
                            type: string
                            example: Beautician
                          hasLicense:
                            type: boolean
                            example: true
                          licenseDetails:
                            type: object
                            properties:
                              licenseNumber:
                                type: string
                                example: RE-1234
                              licenseExpiryDate:
                                type: string
                                format: date
                                example: 2026-12-31
                              licenseDocs:
                                type: array
                                items:
                                  type: string
                                  format: uri
                                example:
                                  - https://example.com/license.pdf
                                  - https://example.com/license2.pdf
              documents:
                type: object
                properties:
                  emiratesId:
                    type: array
                    items:
                      type: string
                    example:
                      - 784-1990-1234567-1
                  emiratesIdExpiry:
                    type: string
                    format: date
                    example: 2027-01-01
                  passport:
                    type: array
                    items:
                      type: string
                      format: uri
                    example:
                      - https://example.com/docs/passport.pdf
                  visa:
                    type: array
                    items:
                      type: string
                      format: uri
                    example:
                      - https://example.com/docs/visa.pdf
                      - https://example.com/docs/visa2.pdf
                  visaExpiry:
                    type: string
                    format: date
                    example: 2026-06-15
              termsAgree:
                type: boolean
                example: true
              accuracyConfirm:
                type: boolean
                example: true
              communicationConsent:
                type: boolean
                example: true
              company:
                type: string
                example: Ali Real Estate
              location:
                type: string
                example: "3"
    responses:
      200:
        description: Agent profile updated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Agent profile updated successfully


/agent/complete-company-profile:
  put:
    summary: Register a new agency
    description: Registers a new agency with company details, operation areas, industries, documents, and consent information.
    tags: [Agency]
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              companyName:
                type: string
                example: Ali Technologies LLC
              companyPhone:
                type: string
                example: +971-*********
              operationArea:
                type: array
                items:
                  type: object
                  properties:
                    operationArea:
                      type: string
                      example: "193"
                    licenseInfo:
                      type: object
                      properties:
                        licenseNumber:
                          type: string
                          example: "12345"
                        licenseExpiryDate:
                          type: string
                          format: date
                          example: "2026-12-31"
                        licenseFile:
                          type: array
                          items:
                            type: string
                            format: uri
                          example:
                            - https://mycdn.com/uploads/license-abu-dhabi.pdf
                            - https://mycdn.com/uploads/license-abu-dhabi-2.pdf
              profilePhoto:
                type: string
                format: uri
                example: https://mycdn.com/uploads/company-logo.png
              industries:
                type: array
                items:
                  type: object
                  properties:
                    industryId:
                      type: string
                      example: "1"
                    industryName:
                      type: string
                      example: Real Estate
                    roles:
                      type: array
                      items:
                        type: object
                        properties:
                          roleId:
                            type: string
                            example: other
                          roleName:
                            type: string
                            example: Real Estate Broker
              nationality:
                type: string
                example: "1"
              emiratesId:
                type: array
                items:
                  type: string
                  format: uri
                example:
                  - https://mycdn.com/uploads/sadasfasfas.pdf
              passport:
                type: array
                items:
                  type: string
                  format: uri
                example:
                  - https://mycdn.com/uploads/passport.pdf
              visa:
                type: array
                items:
                  type: string
                  format: uri
                example:
                  - https://mycdn.com/uploads/visa.pdf
              inviteTeamMembers:
                type: boolean
                example: true
              inviteAgents:
                type: array
                items:
                  type: string
                  format: email
                example:
                  - <EMAIL>
                  - <EMAIL>
              supportingDocs:
                type: array
                items:
                  type: string
                  format: uri
                example:
                  - https://mycdn.com/uploads/doc1.pdf
                  - https://mycdn.com/uploads/doc2.pdf
              referralId:
                type: string
                nullable: true
                example: null
              termsAgree:
                type: boolean
                example: true
              accuracyConfirm:
                type: boolean
                example: true
    responses:
      200:
        description: Agency registered successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Agent profile updated successfully

/agent/counts/{id}:
  get:
    tags: [Agent]
    summary: Get counts by profile ID
    description: Fetches counts and allowed limits of Services, Events, Properties, and Projects for the given profile ID
    parameters:
      - name: id
        in: path
        required: true
        description: Profile ID
        schema:
          type: integer
          example: 4
    responses:
      "200":
        description: Counts fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: number
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Counts fetched successfully
                data:
                  type: object
                  properties:
                    Services:
                      type: object
                      properties:
                        ServicesCount:
                          type: integer
                          example: 2
                        ServicesAllowedLimit:
                          type: integer
                          example: 10
                    Events:
                      type: object
                      properties:
                        EventsCount:
                          type: integer
                          example: 3
                        EventsAllowedLimit:
                          type: integer
                          example: 10
                    Properties:
                      type: object
                      properties:
                        PropertiesCount:
                          type: integer
                          example: 2
                        PropertiesAllowedLimit:
                          type: integer
                          example: 10
                    Projects:
                      type: object
                      properties:
                        ProjectsCount:
                          type: integer
                          example: 3
                        ProjectsAllowedLimit:
                          type: integer
                          example: 10
      "500":
        description: Internal Server Error
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: number
                  example: 500
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Error occurred while getting counts

/agent/get-individual-application:
  get:
    tags: [Agent]
    summary: Get Individual Application
    description: Retrieves detailed profile information of the logged-in agent.
    responses:
      "200":
        description: Agent details fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Data retrieved successfully
                data:
                  type: object
                  properties:
                    firstName:
                      type: string
                      example: Raja
                    middleName:
                      type: string
                      nullable: true
                      example: null
                    lastName:
                      type: string
                      example: Khan
                    nationality:
                      type: string
                      example: "132"
                    gender:
                      type: string
                      example: Male
                    phoneNumber:
                      type: string
                      example: +923012345678
                    profilePhoto:
                      type: string
                      format: uri
                      example: https://fmafiles-main.s3.me-central-1.amazonaws.com/images/agent/verifications/profilePhoto-1757528207317.jpeg
                    industries:
                      type: array
                      items:
                        type: object
                        properties:
                          industryId:
                            type: integer
                            example: 1
                          industryName:
                            type: string
                            example: Real Estate
                          roles:
                            type: array
                            items:
                              type: object
                              properties:
                                roleId:
                                  type: integer
                                  example: 27
                                roleName:
                                  type: string
                                  example: Real Estate Sales Agent
                                hasLicense:
                                  type: boolean
                                  example: true
                                licenseDetails:
                                  type: object
                                  properties:
                                    licenseNumber:
                                      type: string
                                      nullable: true
                                      example: "00002"
                                    licenseExpiryDate:
                                      type: string
                                      format: date
                                      nullable: true
                                      example: "2025-09-30"
                                    licenseDocs:
                                      type: array
                                      nullable: true
                                      items:
                                        type: string
                                        format: uri
                                        example: https://fmafiles-main.s3.me-central-1.amazonaws.com/images/agent/verifications/licenseDoc-1757528349151.jpg
                    location:
                      type: integer
                      example: 6
                    documents:
                      type: object
                      properties:
                        emiratesId:
                          type: array
                          items:
                            type: string
                            format: uri
                            example: https://fmafiles-main.s3.me-

/agent/get-company-application:
  get:
    tags: [Agent]
    summary: Get company application by profile ID
    description: Retrieves the company application details
    responses:
      "200":
        description: Data retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: number
                  example: 200
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Data retrieved successfully
                data:
                  type: object
                  properties:
                    companyName:
                      type: string
                      example: Avanza Technologies LLC
                    companyPhone:
                      type: string
                      example: +971-*********
                    operationArea:
                      type: array
                      items:
                        type: object
                        properties:
                          operationArea:
                            type: string
                            example: "193"
                          licenseInfo:
                            type: object
                            properties:
                              licenseNumber:
                                type: string
                                example: "12345"
                              licenseExpiryDate:
                                type: string
                                format: date
                                example: "2026-12-31"
                              licenseFile:
                                type: array
                                items:
                                  type: string
                                  format: uri
                                example:
                                  - https://mycdn.com/uploads/license-abu-dhabi.pdf
                                  - https://mycdn.com/uploads/license-abu-dhabi-2.pdf
                    profilePhoto:
                      type: string
                      format: uri
                      example: https://mycdn.com/uploads/company-logo.png
                    industries:
                      type: array
                      items:
                        type: object
                        properties:
                          industryId:
                            type: integer
                            example: 1
                          industryName:
                            type: string
                            example: Real Estate
                          roles:
                            type: object
                            properties:
                              roleId:
                                type: integer
                                example: 26
                              roleName:
                                type: string
                                example: Real Estate Broker
                    nationality:
                      type: string
                      example: "1"
                    emiratesId:
                      type: array
                      items:
                        type: string
                        format: uri
                      example:
                        - https://mycdn.com/uploads/sadasfasfas.pdf
                    passport:
                      type: array
                      items:
                        type: string
                        format: uri
                      example:
                        - https://mycdn.com/uploads/passport.pdf
                    visa:
                      type: array
                      items:
                        type: string
                        format: uri
                      example:
                        - https://mycdn.com/uploads/visa.pdf
                    inviteAgents:
                      type: array
                      items:
                        type: string
                        format: email
                      example:
                        - <EMAIL>
                        - <EMAIL>
                    supportingDocs:
                      type: array
                      items:
                        type: string
                        format: uri
                      example:
                        - https://mycdn.com/uploads/doc1.pdf
                        - https://mycdn.com/uploads/doc2.pdf
                    termsAgree:
                      type: boolean
                      example: true
                    accuracyConfirm:
                      type: boolean
                      example: true
      "500":
        description: Internal Server Error
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: number
                  example: 500
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Error occurred while retrieving company application
