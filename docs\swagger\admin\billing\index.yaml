/admin/billing/transactions:
  get:
    tags:
      - Admin Billing
    summary: Get paginated list of admin billing transactions
    operationId: getAdminBillingTransactions
    description: >
      Fetches a paginated list of billing transactions for the admin panel.
      Includes subscription details, agent/agency info, amount, card details (if available) and Stripe references.
    security:
      - bearerAuth: []
      - cookieAuth: []
    parameters:
      - name: search
        in: query
        schema: { type: string }
      - name: type
        in: query
        schema:
          type: string
          enum:
            [
              "Subscription",
              "Payment",
              "Refund",
              "Dispute",
              "Commission",
              "",
            ]
      # - name: status
      #   in: query
      #   schema:
      #     type: string
      #     enum: ["Completed", "Pending", "Failed", "Disputed", ""]
      # - name: dateFrom
      #   in: query
      #   schema: { type: string, format: date }
      # - name: dateTo
      #   in: query
      #   schema: { type: string, format: date }
      - name: page
        in: query
        schema: { type: integer, default: 1 }
      - name: pageSize
        in: query
        schema: { type: integer, default: 10 }
      - name: sortBy
        in: query
        schema:
          type: string
          enum: ["date", "amount", "status"]
          default: "date"
      - name: sortDir
        in: query
        schema:
          type: string
          enum: ["asc", "desc"]
          default: "desc"
    responses:
      "200":
        description: Transactions fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                transactions:
                  type: array
                  items:
                    $ref: "#/components/schemas/AdminBillingTransaction"
                pagination:
                  type: object
                  properties:
                    total: { type: integer }
                    totalPages: { type: integer }
                    currentPage: { type: integer }
                    perPage: { type: integer }

/admin/billing/summary:
  get:
    tags:
      - Admin Billing
    summary: Get admin billing summary
    operationId: getAdminBillingSummary
    description: >
      Fetches aggregated billing metrics including total revenue,
      pending refunds, disputed amounts, and transaction count for admin.
    security:
      - bearerAuth: []
      - cookieAuth: []
    responses:
      "200":
        description: Summary fetched successfully
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AdminBillingSummary"

components:
  schemas:
    AdminBillingSummary:
      type: object
      properties:
        totalRevenue: { type: number, example: 1899.98 }
        pendingRefunds: { type: number, example: 149.99 }
        disputedAmount: { type: number, example: 89.99 }
        totalTransactions: { type: integer, example: 6 }

    AdminBillingTransaction:
      type: object
      properties:
        id: { type: integer }
        profileId: { type: integer }
        agentName: { type: string }
        agentEmail: { type: string, nullable: true }
        amount: { type: number }
        amountFormatted: { type: string, example: "AED 299.99" }
        type: { type: string }
        status: { type: string }
        description: { type: string, nullable: true }
        date: { type: string, format: date-time }
        paymentMethod: { type: string, nullable: true }
        reference: { type: string, example: "TXN_001234" }
        stripe:
          type: object
          properties:
            customerId: { type: string, nullable: true }
            subscriptionId: { type: string, nullable: true }
            paymentIntentId: { type: string, nullable: true }
            chargeId: { type: string, nullable: true }
            cardBrand: { type: string, nullable: true }
            cardLast4: { type: string, nullable: true }
            cardExpMonth: { type: integer, nullable: true }
            cardExpYear: { type: integer, nullable: true }
