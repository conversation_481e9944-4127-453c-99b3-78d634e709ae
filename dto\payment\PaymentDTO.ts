import { PaymentType } from "./PaymentTypeDTO";
import { ProfileDTO } from "./ProfileDTO";

export interface PaymentDTO {
  id: number;
  paymentId: string;
  date: string;
  type: PaymentType;
  description: string;
  amount: string;
  amountValue: number;
  currency: string;
  statusId: number;
  statusName: string;
  invoiceNo: string | null;
  packageTypeId?: number | null;
  packageTypeName?: string | null;
  interval?: string | null;
  invoicePdfUrl?: string | null;
  hostedInvoiceUrl?: string | null;
  subscriptionId?: string | null;
  profile?: ProfileDTO | null;
}
