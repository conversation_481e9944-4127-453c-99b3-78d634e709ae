import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, responseData, response } from "../../../utils/response";
import <PERSON><PERSON> from "stripe";
import stripeKeys from "../../../config/stripeConfigs";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

// Validation helpers
const isValidUserType = (userType: string) =>
  ["agent", "agency"].includes(userType);
const isValidFeatureType = (featureType: string) =>
  ["TEXT", "NUMERIC", "BOOLEAN", "ENUM"].includes(featureType);

// --- Package Types ---
export const getAllPackageTypes = asyncHandler(
  async (req: Request, res: Response) => {
    const params = [
      1,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      "name",
      null,
      null,
      null,
      null,
    ];
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagetype($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)",
      params
    );
    const result = rows[0];
    const spResult = result.sp_packagetype;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getPackageTypeById = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagetype($1, $2)",
      [0, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagetype;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Package not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const createPackageType = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      name,
      description,
      statusId,
      price,
      currency,
      userType,
      colorTheme,
      interval,
      discountId,
    } = req.body;

    /* 1️⃣ validate userType */
    if (!isValidUserType(userType)) {
      return error(res, 400, "Invalid userType. Must be 'agent' or 'agency'");
    }

    const normalizedInterval = interval?.toLowerCase() || "month";
    const client = await db.connect();
    /* 2️⃣ determine if this is a free plan */
    const isFree = !price || Number(price) <= 0;

    /* 4️⃣ Prevent duplicate (name + userType + interval) */
    const dupe = await client.query(
      `SELECT 1
         FROM look.packagetype
         WHERE LOWER(name) = LOWER($1)
           AND "userType" = $2
           AND "interval" = $3
         LIMIT 1`,
      [name, userType, normalizedInterval]
    );

    if (dupe.rows.length > 0) {
      return error(
        res,
        400,
        `PackageType '${name}' already exists for userType '${userType}' and interval '${normalizedInterval}'.`
      );
    }

    /* 5️⃣ Ensure only one free plan per userType + interval */
    if (isFree) {
      const freeExists = await client.query(
        `SELECT 1
           FROM look.packagetype
           WHERE price = 0
             AND "userType" = $1
             AND "interval" = $2
           LIMIT 1`,
        [userType, normalizedInterval]
      );

      if (freeExists.rows.length > 0) {
        return error(
          res,
          400,
          `A free plan already exists for userType '${userType}' and interval '${normalizedInterval}'.`
        );
      }
    }

    let stripeProductId = null;
    let stripePlanId = null;
    let payPalPlanId = null;

    /* 3️⃣ create Stripe plan/product (if not free) */
    if (!isFree) {
      const stripeProduct = await stripe.products.create({
        name: name ?? "Basic Plan",
        description: description ? description : "Basic subscription plan",
      });
      stripeProductId = stripeProduct.id;

      const stripePlan = await stripe.plans.create({
        product: stripeProductId,
        amount: Math.round(Number(price) * 100),
        currency: (currency || "AED").toLowerCase(),
        interval: normalizedInterval,
      });
      stripePlanId = stripePlan.id;
    }

    try {
      await client.query("BEGIN");

      /* 6️⃣ Call stored procedure */
      const { rows } = await client.query(
        `SELECT * FROM look.sp_packagetype(
          $1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14, $15
        )`,
        [
          2, // p_fnid (insert)
          null, // p_id
          name,
          description,
          statusId ?? 1,
          req.user.id,
          price ?? 0,
          currency ?? "AED",
          userType,
          colorTheme,
          "id", // sortBy
          stripePlanId,
          payPalPlanId,
          normalizedInterval,
          discountId,
        ]
      );

      const sp = rows[0].sp_packagetype;
      if (sp.type === "error") throw new Error(sp.message);

      await client.query("COMMIT");
      return responseData(res, 201, sp.message, sp.data);
    } catch (err: any) {
      await client.query("ROLLBACK");

      /* rollback Stripe artifacts */
      if (stripePlanId) {
        await stripe.plans
          .update(stripePlanId, { active: false })
          .catch(() => {});
      }
      if (stripeProductId) {
        await stripe.products
          .update(stripeProductId, { active: false })
          .catch(() => {});
      }

      const msg =
        err.code === "23505" ? err.message : "Could not create package type.";
      return error(res, 400, msg);
    } finally {
      client.release();
    }
  }
);

export const updatePackageType = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
      name,
      description,
      statusId,
      price,
      currency,
      userType,
      colorTheme,
      interval,
      discountId,
    } = req.body;

    if (userType && !isValidUserType(userType)) {
      return error(res, 400, "Invalid userType. Must be 'agent' or 'agency'");
    }

    const client = await db.connect();
    let newStripeProductId: string | null = null;
    let newStripePlanId: string | null = null;

    try {
      await client.query("BEGIN");

      const cur = await client.query(
        `SELECT * FROM look.packagetype WHERE id = $1 FOR UPDATE`,
        [id]
      );
      if (cur.rowCount === 0) {
        await client.query("ROLLBACK");
        return error(res, 404, "Package not found");
      }

      const current = cur.rows[0];

      const finalName = name ?? current.name;
      const finalDesc = description ?? current.description;
      const finalStatusId = statusId ?? current.statusid;
      const finalPrice = price ?? current.price;
      const finalCurrency = (currency ?? current.currency).toUpperCase();
      const finalUserType = userType ?? current.usertype;
      const finalTheme = colorTheme ?? current.colortheme;
      const finalDiscountId = discountId ?? current.discountid;
      const finalInterval = (
        interval ??
        current.interval ??
        "month"
      ).toLowerCase();
      const isFree = !finalPrice || Number(finalPrice) <= 0;

      const dupe = await client.query(
        `SELECT 1 FROM look.packagetype
         WHERE LOWER(name) = LOWER($1)
           AND "userType" = $2
           AND "interval" = $3
           AND id <> $4
         LIMIT 1`,
        [finalName, finalUserType, finalInterval, id]
      );
      if (dupe.rows.length > 0) {
        return error(
          res,
          400,
          `PackageType '${finalName}' already exists for userType '${finalUserType}' and interval '${finalInterval}'.`
        );
      }

      if (isFree) {
        const freeExists = await client.query(
          `SELECT 1 FROM look.packagetype
           WHERE price = 0
             AND "userType" = $1
             AND "interval" = $2
             AND id <> $3
           LIMIT 1`,
          [finalUserType, finalInterval, id]
        );
        if (freeExists.rows.length > 0) {
          return error(
            res,
            400,
            `A free plan already exists for userType '${finalUserType}' and interval '${finalInterval}'.`
          );
        }
      }

      let stripePlanIdToUse: string | null = current.stripeplanid;
      const needNewPlan =
        finalPrice !== current.price ||
        finalName !== current.name ||
        finalInterval !== current.interval;

      if (needNewPlan && !isFree) {
        if (current.stripeplanid) {
          await stripe.plans
            .update(current.stripeplanid, { active: false })
            .catch(() => {});
        }

        const product = await stripe.products.create({
          name: finalName,
          description: finalDesc,
        });
        newStripeProductId = product.id;

        const plan = await stripe.plans.create({
          product: newStripeProductId,
          amount: Math.round(finalPrice * 100),
          currency: finalCurrency.toLowerCase(),
          interval: finalInterval,
        });
        newStripePlanId = plan.id;
        stripePlanIdToUse = newStripePlanId;
      }

      /* ✅ Call stored procedure using positional args (14 total) */
      const { rows } = await client.query(
        `SELECT * FROM look.sp_packagetype(
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15
        )`,
        [
          2, // p_fnid
          id, // p_id
          finalName,
          finalDesc,
          finalStatusId,
          req.user.id,
          finalPrice,
          finalCurrency,
          finalUserType,
          finalTheme,
          "id", // p_sortby
          stripePlanIdToUse,
          null, // PayPal not implemented yet
          finalInterval,
          finalDiscountId,
        ]
      );

      const sp = rows[0].sp_packagetype;
      if (sp.type === "error") throw new Error(sp.message);

      if (newStripePlanId && current.stripeplanid) {
        await client.query(
          `UPDATE look.packagetype
           SET "archivedStripePlans" = COALESCE("archivedStripePlans", '[]')::jsonb || $2::jsonb
           WHERE id = $1`,
          [id, JSON.stringify([current.stripeplanid])]
        );
      }

      await client.query("COMMIT");

      if (newStripePlanId && current.stripeplanid) {
        await stripe.plans
          .update(current.stripeplanid, { active: false })
          .catch(() => {});
      }

      return responseData(res, 200, sp.message, sp.data);
    } catch (err: any) {
      await client.query("ROLLBACK");

      if (newStripePlanId)
        await stripe.plans
          .update(newStripePlanId, { active: false })
          .catch(() => {});
      if (newStripeProductId)
        await stripe.products
          .update(newStripeProductId, { active: false })
          .catch(() => {});

      const msg =
        err.code === "23505"
          ? "Duplicate name for that userType and interval."
          : err.message || "Could not update package type.";
      return error(res, 400, msg);
    } finally {
      client.release();
    }
  }
);

export const deletePackageType = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagetype($1, $2)",
      [3, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagetype;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return response(res, 200, spResult.message);
  }
);

// --- Features Meta ---
export const getAllFeatures = asyncHandler(
  async (req: Request, res: Response) => {
    const params = [1, null, null, null, null, null, null, 1];
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturesmeta($1, $2, $3, $4, $5, $6, $7, $8)",
      params
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturesmeta;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getFeatureById = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturesmeta($1, $2)",
      [0, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturesmeta;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Feature not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const createFeature = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      featureName,
      featureType,
      featureConstant,
      displayOrder,
      description,
    } = req.body;
    if (!isValidFeatureType(featureType)) {
      return error(
        res,
        400,
        "Invalid featureType. Must be one of: TEXT, NUMERIC, BOOLEAN, ENUM"
      );
    }
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturesmeta($1, $2, $3, $4, $5, $6, $7)",
      [
        2,
        null,
        featureName,
        featureType,
        featureConstant,
        displayOrder || 1,
        description,
      ]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturesmeta;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 201, spResult.message, spResult.data);
  }
);

export const updateFeature = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const {
      featureName,
      featureType,
      featureConstant,
      displayOrder,
      description,
    } = req.body;
    if (!isValidFeatureType(featureType)) {
      return error(
        res,
        400,
        "Invalid featureType. Must be one of: TEXT, NUMERIC, BOOLEAN, ENUM"
      );
    }
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturesmeta($1, $2, $3, $4, $5, $6, $7)",
      [
        2,
        id,
        featureName,
        featureType,
        featureConstant,
        displayOrder,
        description,
      ]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturesmeta;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Feature not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const deleteFeature = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturesmeta($1, $2)",
      [3, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturesmeta;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return response(res, 200, spResult.message);
  }
);

// --- Feature Values ---
export const getAllFeatureValues = asyncHandler(
  async (req: Request, res: Response) => {
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1)",
      [1]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getFeatureValueById = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1, $2)",
      [0, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Feature value not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getFeatureValuesByPackageTypeId = asyncHandler(
  async (req: Request, res: Response) => {
    const { packageTypeId } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1, $2, $3)",
      [1, null, packageTypeId]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const createFeatureValue = asyncHandler(
  async (req: Request, res: Response) => {
    const { packageTypeId, featureId, featureValue } = req.body;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1, $2, $3, $4, $5)",
      [2, null, packageTypeId, featureId, featureValue]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 201, spResult.message, spResult.data);
  }
);

export const updateFeatureValue = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { packageTypeId, featureId, featureValue } = req.body;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1, $2, $3, $4, $5)",
      [2, id, packageTypeId, featureId, featureValue]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Feature value not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const deleteFeatureValue = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagefeaturevalues($1, $2)",
      [3, id]
    );
    const result = rows[0];
    const spResult = result.sp_packagefeaturevalues;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return response(res, 200, spResult.message);
  }
);

// --- Helper Endpoints ---
export const updatePackageStatus = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params;
    const { statusId } = req.body;

    if (![1, 2].includes(statusId)) {
      return error(
        res,
        400,
        "Invalid statusId. Must be 1 (Active) or 2 (Inactive)"
      );
    }

    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagetype($1, $2, $3, $4, $5)",
      [4, id, null, null, statusId]
    );
    const result = rows[0];
    const spResult = result.sp_packagetype;
    if (spResult.type === "error") return error(res, 400, spResult.message);
    if (!spResult.data) return error(res, 404, "Package not found");
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getDistinctFeatureNames = asyncHandler(
  async (req: Request, res: Response) => {
    const { rows } = await db.query(`
    SELECT DISTINCT "featureName"
    FROM look.packagefeaturesmeta
    ORDER BY "featureName"
  `);
    return responseData(res, 200, "Feature names fetched", rows);
  }
);

// Additional helper endpoints that use the stored procedures
export const getPackagesWithFeatures = asyncHandler(
  async (req: Request, res: Response) => {
    const { rows } = await db.query(`
      SELECT 
        p.id as "packageId",
        p.name,
        p."userType",
        p.price,
        p.currency,
        p."statusId",
        p."colorTheme",
        p."stripePlanId",
        p."paypalPlanId",
        p.interval,
        p."discountId",
        d.name AS "discountName",
        d.type AS "discountType",
        d.value AS "discountValue",
        d."created_at" AS "discountCreatedAt",
        json_agg(
          json_build_object(
            'featureName', f."featureName",
            'featureValue', v."featureValue",
            'featureType', f."featureType",
            'featureConstant', f."featureConstant",
            'displayOrder', f."displayOrder"
          )
        ) as features
      FROM look.packagetype p
      LEFT JOIN look.packagefeaturevalues v ON p.id = v."packageTypeId"
      LEFT JOIN look.packagefeaturesmeta f ON v."featureId" = f."featureId"
      LEFT JOIN web.discounts d ON p."discountId" = d.id
      GROUP BY p.id, d.id
      ORDER BY p.id
    `);

    return responseData(res, 200, "Packages with features fetched", rows);
  }
);

export const getPackagesByUserType = asyncHandler(
  async (req: Request, res: Response) => {
    const { userType } = req.params;

    if (!isValidUserType(userType)) {
      return error(res, 400, "Invalid userType. Must be 'agent' or 'agency'");
    }

    const { rows } = await db.query(
      `
        SELECT p.*, 
          COUNT(*) OVER (PARTITION BY p.name) as name_count
        FROM look.packagetype p
        WHERE p."userType" = $1
        ORDER BY p.id
      `,
      [userType]
    );
    return responseData(res, 200, "Packages fetched", rows);
  }
);

export const getFeatureValuesByPackageAndUserType = asyncHandler(
  async (req: Request, res: Response) => {
    const { packageTypeId, userType } = req.params;

    if (!isValidUserType(userType)) {
      return error(res, 400, "Invalid userType. Must be 'agent' or 'agency'");
    }

    const { rows } = await db.query(
      `
        SELECT v.*, f."featureName", f."featureType", f."featureConstant"
        FROM look.packagefeaturevalues v
        JOIN look.packagefeaturesmeta f ON v."featureId" = f."featureId"
        JOIN look.packagetype p ON v."packageTypeId" = p.id
        WHERE v."packageTypeId" = $1 AND p."userType" = $2
        ORDER BY f."displayOrder"
      `,
      [packageTypeId, userType]
    );
    return responseData(res, 200, "Feature values fetched", rows);
  }
);
