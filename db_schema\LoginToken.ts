import { integer, serial, text, timestamp, pgSchema } from "drizzle-orm/pg-core";

export const secSchema = pgSchema("sec");

export const loginToken = secSchema.table(
  "logintoken",
  {
    id: serial("id").notNull().primaryKey(),
    userId: integer("userId").notNull(),
    token: text("localName").notNull(),
    createdOn: timestamp("createdOn", { withTimezone: true, precision: 6 }),
    expiresOn: timestamp("expiresOn", { withTimezone: true, precision: 6 }).notNull(),
    statusId: integer("statusId"),
  }
);