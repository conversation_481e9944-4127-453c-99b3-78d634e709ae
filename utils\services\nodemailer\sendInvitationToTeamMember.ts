import transporter from ".";
import nodemailer from "nodemailer";

export const sendInvitationToTeamMember = async (
    fullName: string,
    email: string,
    agencyName: string,
    registrationLink: string,
    isRegistered: boolean
) => {
    let subject = `Invitation to Join ${agencyName} as a Team Member`;
    let html = "";

    if (isRegistered) {
        html = `
    <p>Dear ${fullName},</p>
    <p>You have been invited to join <b>${agencyName}</b> as a team member.</p>
    <p>
      <a href="${registrationLink}" style="display:inline-block;padding:10px 20px;background-color:#4CAF50;color:white;text-decoration:none;border-radius:5px;">
        Accept Invite
      </a>
    </p>
    <p>Please note, this invitation link is valid for 24 hours. After expiration, you will need to request ${agencyName} to send a new invitation.</p>
    <p>Best regards,<br/>${agencyName} Team</p>
  `;
    } else {
        html = `
    <p>Dear ${fullName},</p>
    <p>You have been invited to join <b>${agencyName}</b> as a team member.</p>
    <p>
      <a href="${registrationLink}" style="display:inline-block;padding:10px 20px;background-color:#007BFF;color:white;text-decoration:none;border-radius:5px;">
        Accept & Register
      </a>
    </p>
    <p>Please note, this invitation link is valid for 24 hours. After expiration, you will need to request ${agencyName} to send a new invitation.</p>
    <p>Best regards,<br/>${agencyName} Team</p>
  `;
    }

    const mailOptions: nodemailer.SendMailOptions = {
        from: process.env.VERIFICATION_EMAIL as string,
        to: email,
        subject,
        html,
    };

    await transporter.sendMail(mailOptions);
};