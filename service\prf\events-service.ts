import { EventsRepository } from "../../repo/prf/events-repository";

export class EventsService {

    private eventsRepo = new EventsRepository();

    async getEventsCountByProfileId(id: number) {
        const count = await this.eventsRepo.getEventsCountByProfileId(id);
        const eventsCountData = {
            EventsCount: count,
            EventsAllowedLimit: 10
        }
        return eventsCountData;
    }

}