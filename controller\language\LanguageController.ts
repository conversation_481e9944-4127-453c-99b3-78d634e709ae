import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import { LanguageService } from "../../service/LanguageService";
import { LanguageDTO } from "../../dto/language/LanguageDTO";

export class LanguageController {

    private languageService = new LanguageService();

    getAllLanguages = asyncHandler(
        async (req: Request, res: Response) => {
            try {
                const result: LanguageDTO[] = await this.languageService.getAllLanguages();
                return responseData(res, 200, "Languages fetched successfully", result);
            } catch (error) {
                console.error("Error fetching languages:", error);
                return errorCatchResponse(res, "Something went wrong");
            }
        }
    );

}