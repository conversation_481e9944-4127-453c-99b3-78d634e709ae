import { Request, response, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { errorCatchResponse, responseData } from "../../../utils/response";
import { AGENTS } from "../../../utils/database/queries/agentDetails";
import { AUTH } from "../../../utils/database/queries/auth";

const isValidDate = (d: any) => {
  const parsed = new Date(d);
  return parsed instanceof Date && !isNaN(parsed.getTime());
};

export const getDashboardData = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { startDate, endDate, status } = req.query;

    const safeStart = isValidDate(startDate) ? startDate : null;
    const safeEnd = isValidDate(endDate) ? endDate : null;

    let statusId: number | null = null;

    // If status is provided and not "All", fetch the corresponding statusId
    if (status && status !== "All") {
      const statusNames = Array.isArray(status)
        ? status.map((s) => String(s))
        : [String(status)];

      const { rows: statusRows } = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusRows.length > 0) {
        statusId = statusRows[0].id;
      } else {
        return errorCatchResponse(res, "Invalid status provided");
      }
    }

    if (safeStart && safeEnd) {
      const query = AGENTS.DASHBOARD_AGENT_APPLICATIONS_COUNT_WITH_FILTER;

      const params = statusId
        ? [safeStart, safeEnd, statusId]
        : [safeStart, safeEnd];

      const { rows } = await db.query(query, params);

      return responseData(res, 200, "Data retrieved successfully", rows);
    } else {
      const query = AGENTS.DASHBOARD_AGENT_APPLICATIONS_COUNT;

      const params = [statusId ?? null];

      const { rows } = await db.query(query, params);

      return responseData(res, 200, "Data retrieved successfully", rows);
    }
  } catch (err) {
    console.error("Error retrieving dashboard data:", err);

    return errorCatchResponse(res, "Something went wrong");
  }
});