import cron from 'node-cron';
import { db } from '../../config/database';
import { AGENTS } from '../../utils/database/queries/agentDetails';
import { checkExpiryAndSendEmails } from '../../utils/helperFunctions/check.documents.expiry';
import { sendMonthlyReferralCSV } from '../../controller/admin/salesperson.controller';

/**
 * Starts a cron job to check for document expiry and send notifications.
 * 
 * The cron job is scheduled to run daily at 12:00 PM (noon).
 * 
 * It performs the following tasks:
 * - Logs the start of the document expiry check.
 * - Queries the database to retrieve document details for agents.
 * - Checks for expired documents and sends email notifications as needed.
 * - Logs the completion of the document expiry notifications.
 */
export const startDocumentExpiryCron = () => {
  cron.schedule('0 12 * * *', async () => {
    // cron.schedule('*/5 * * * * *', async () => {
    console.log('[CRON] Running document expiry check...');

    const { rows } = await db.query(AGENTS.GET_DOCUMENT_DETAILS_BY_PROFILE_ID);
    await checkExpiryAndSendEmails(rows);
    console.log('[CRON] Completed document expiry notifications.');
  });
};

// Cron job — runs on the last day 
export const sendEmailToSalespersonsCron = () => {
cron.schedule("59 23 * * *", async () => {
  const today = new Date();
  const tomorrow = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

  if (tomorrow.getDate() === 1) {
    console.log("Running monthly referral CSV cron job on last day of month...");

    const salespersons = await db.query(`SELECT id FROM look.salespersons`);
    for (const sp of salespersons.rows) {
      try{

        await sendMonthlyReferralCSV(sp.id);
      }catch{
        console.error(`Failed to send monthly referral CSV for salesperson ${sp.id}`);
      }
    }
  } else {
    console.log("Not the last day of the month. Skipping cron job.");
  }
});
}