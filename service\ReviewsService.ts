import { ReviewRepository } from "../repo/ReviewsRepository"; 
import { ReviewDTO } from "../dto/reviews/ReviewDTO";
import { GetAllReviewsResponseDTO } from "../dto/reviews/GetAllReviewsResponseDTO";
import { GetAllReviewsQueryDTO } from "../dto/reviews/GetAllReviewsQueryDTO";
import { ReviewStatsDTO } from "../dto/reviews/ReviewStatsDTO";
import {  UpdateReviewStatusDTO,  } from "../dto/reviews/CreateUpdateReviewDTO";
  

export class ReviewService {
  private reviewRepo = new ReviewRepository();

  async getAllReviews(
    query: GetAllReviewsQueryDTO
  ): Promise<GetAllReviewsResponseDTO> {
    const {
      page = 1,
      limit = 10,
      status,
      search,
      type, // 'agent' or 'agency'
    } = query;

    // Validate and map type
    let accountType: string | undefined;
    if (type) {
      if (type === "agent") accountType = "Individual";
      else if (type === "agency")
        accountType = "Company/Agency/PropertyDeveloper";
      else
        throw new Error("Invalid type parameter. Must be 'agent' or 'agency'");
    }

    // Convert status name to ID if provided
    let statusId: number | undefined;
    if (status) {
      try {
        const statusIdcheck = await this.reviewRepo.getStatusIdByName(status);
        if (statusIdcheck !== null) {
          statusId = statusIdcheck.id;
        }
      } catch (error) {
        // fallback: try parsing as integer
        const parsedStatus = parseInt(status);
        if (isNaN(parsedStatus)) {
          throw new Error(`Invalid status value: ${status}`);
        }
        statusId = parsedStatus;
      }
    }

    // Build filters and queryParams for filtered queries
    const filters: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    if (search) {
      filters.push(`(
        LOWER(CONCAT(reviewer."firstName", ' ', reviewer."lastName")) LIKE LOWER($${paramIndex})
        OR LOWER(reviewer.email) LIKE LOWER($${paramIndex})
        OR LOWER(CONCAT(reviewee."firstName", ' ', reviewee."lastName")) LIKE LOWER($${paramIndex})
        OR LOWER(reviewee.email) LIKE LOWER($${paramIndex})
        OR LOWER(agn.name) LIKE LOWER($${paramIndex})
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }
    if (typeof statusId === "number") {
      filters.push(`r."statusId" = $${paramIndex}`);
      queryParams.push(statusId);
      paramIndex++;
    }
    if (accountType) {
      filters.push(`reviewee."accountType" = $${paramIndex}`);
      queryParams.push(accountType);
      paramIndex++;
    }

    const offset = (page - 1) * limit;
    let reviews, totalCount;

    if (filters.length > 0) {
      // Use filtered queries
      reviews = await this.reviewRepo.getFilteredReviews(limit, offset, filters, [...queryParams]);
      totalCount = await this.reviewRepo.getFilteredReviewsCount(filters, [...queryParams]);
    } else {
      // Use unfiltered queries
      reviews = await this.reviewRepo.getAllReviews(limit, offset);
      totalCount = await this.reviewRepo.getAllReviewsCount();
    }

    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);
    const pagination = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows,
      pagination,
    };
  }

  async getReviewById(id: number): Promise<ReviewDTO> {
    const review = await this.reviewRepo.getReviewById(id);
    if (!review) {
      throw new Error("Review not found");
    }
    return review;
  }

  async createReview(
    reviewerId: number,
    revieweeId: number,
    reviewText: string,
    rating: number
  ): Promise<ReviewDTO> {
    // Validate required fields
    if (!revieweeId || !reviewText || !rating) {
      throw new Error(
        "Missing required fields: revieweeId, reviewText, and rating"
      );
    }
    if (rating < 1 || rating > 5) {
      throw new Error("Rating must be between 1 and 5");
    }
    if (reviewerId === revieweeId) {
      throw new Error("You cannot review yourself");
    }

    // Check if reviewee exists and is valid type
    const revieweeExists = await this.reviewRepo.getReviewIfExists(revieweeId);
    if (!revieweeExists) {
      throw new Error("Reviewee not found");
    }
    const reviewee = revieweeExists.rows[0];
    if (
      ["Individual", "Company/Agency/PropertyDeveloper"].indexOf(
        reviewee.accountType
      ) === -1
    ) {
      throw new Error("You can only review agents or agencies");
    }

    // Check for existing review
    const existingReview = await this.reviewRepo.checkExistingReview(
      reviewerId,
      revieweeId
    );
    if (existingReview) {
      throw new Error("You have already reviewed this profile");
    }

    // Get reviewer login
    const reviewerLogin = await this.reviewRepo.getReviewerLogin(reviewerId);
    if (!reviewerLogin.rows.length) {
      throw new Error("Reviewer login not found");
    }

    // Get pending status ID
    const pendingStatusQuery = await this.reviewRepo.getPendingStatus();
    if (!pendingStatusQuery.rows.length) {
      throw new Error("Pending status not found in database");
    }
    const pendingStatusId = pendingStatusQuery.rows[0].id;

    // Call repository with validated and mapped parameters
    return this.reviewRepo.createReview(
      reviewerId,
      revieweeId,
      reviewText,
      rating,
      pendingStatusId,
      reviewerLogin.rows[0].id
    );
  } 

  async updateReviewStatus(
    id: number,
    statusData: UpdateReviewStatusDTO,
    adminId: number
  ): Promise<void> {
    // Validate statusData
    if (!statusData || typeof statusData !== "object") {
      throw new Error("Status data is required and must be an object");
    }
    if (!statusData.status || typeof statusData.status !== "string") {
      throw new Error("Status is required and must be a string");
    }
    const status = statusData.status.trim();
    if (!status) {
      throw new Error("Status cannot be empty");
    }

    // Map status name to ID
    let statusId: number | undefined;
    try {
      const statusIdcheck = await this.reviewRepo.getStatusIdByName(status);
      if (statusIdcheck !== null) {
        statusId = statusIdcheck.id;
      }
    } catch (error) {
      // Ignore error, fallback below
    }
    if (typeof statusId === "undefined") {
      switch (status.toLowerCase()) {
        case "approved":
        case "approve":
        case "published":
        case "publish":
          statusId = 2;
          break;
        case "rejected":
        case "reject":
          statusId = 3;
          break;
        case "pending":
          statusId = 1;
          break;
        case "deleted":
        case "hidden":
          statusId = 4;
          break;
        default:
          throw new Error("Invalid status");
      }
    }

    // Get current status
    const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);

    // Call repository with validated and mapped parameters
    await this.reviewRepo.updateReviewStatus(id, statusId, adminId);

    // Log review action
    await this.reviewRepo.logReviewAction(
      id,
      "status_update",
      currentStatus,
      statusId,
      statusData.note || null,
      adminId
    );

    // Add note if provided
    if (statusData.note) {
      await this.addReviewNote(id, statusData.note, adminId);
    }
  }

  async deleteReview(id: number, modifiedBy: number): Promise<void> {
    await this.reviewRepo.deleteReview(id, modifiedBy);
  }

  async fetReviewStats(): Promise<ReviewStatsDTO> {
    // Get status IDs dynamically
    const pendingStatusObj = await this.reviewRepo.getStatusIdByName("Pending");
    const approvedStatusObj = await this.reviewRepo.getStatusIdByName("Publish");
    const rejectedStatusObj = await this.reviewRepo.getStatusIdByName("Rejected");
    const hiddenStatusObj = await this.reviewRepo.getStatusIdByName("Hidden");

    if (!pendingStatusObj || !approvedStatusObj || !rejectedStatusObj || !hiddenStatusObj) {
      throw new Error("One or more status IDs could not be found.");
    }

    const { statsRow, ratingRows } = await this.reviewRepo.getReviewStats(
      pendingStatusObj.id,
      approvedStatusObj.id,
      rejectedStatusObj.id,
      hiddenStatusObj.id
    );

    return {
      totalReviews: parseInt(statsRow?.total_reviews) || 0,
      pendingReviews: parseInt(statsRow?.pending_reviews) || 0,
      approvedReviews: parseInt(statsRow?.approved_reviews) || 0,
      rejectedReviews: parseInt(statsRow?.rejected_reviews) || 0,
      hiddenReviews: parseInt(statsRow?.hidden_reviews) || 0,
      flaggedReviews: parseInt(statsRow?.flagged_reviews) || 0,
      averageRating: parseFloat(statsRow?.avg_rating) || 0,
      ratingDistribution: Array.isArray(ratingRows)
        ? ratingRows.map((row: any) => ({
            rating: row.rating,
            count: parseInt(row.count),
          }))
        : [],
    };
  }

  async getProfileReviews(
    profileId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {
    return this.reviewRepo.getProfileReviews(profileId, page, limit);
  }

  async getProfileRatingStats(profileId: number) {
    return this.reviewRepo.getProfileRatingStats(profileId);
  }

  async getUserReviews(
    userId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {


    return this.reviewRepo.getUserReviews(userId, page, limit);
  }

  async flagReview(id: number, flagged: boolean = true): Promise<void> {
    await this.reviewRepo.flagReview(id, flagged);
  }

  async addReviewNote(
    reviewId: number,
    note: string,
    adminId: number
  ): Promise<void> {
    if (!note || note.trim().length === 0) {
      throw new Error("Note cannot be empty");
    }
    await this.reviewRepo.addReviewNote(reviewId, note, adminId);
  }

  async getReviewNotes(reviewId: number) {
    return this.reviewRepo.getReviewNotes(reviewId);
  }

  async getReviewHistory(reviewId: number) {
    return this.reviewRepo.getReviewHistory(reviewId);
  }

  async hideReview(id: number, reason: string, adminId: number): Promise<void> {
    // Get hidden status ID
    const hiddenStatusId = await this.reviewRepo.getHiddenStatusId();
    // Get current status
    const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);
    // Update status
    await this.reviewRepo.updateReviewStatus(id, hiddenStatusId, adminId);
    // Log action
    await this.reviewRepo.logReviewAction(
      id,
      "hide",
      currentStatus,
      hiddenStatusId,
      reason,
      adminId
    );
    // Add note if reason provided
    if (reason) {
      await this.addReviewNote(id, `Hidden: ${reason}`, adminId);
    }
  }

  async restoreReview(id: number, adminId: number): Promise<void> {
    // Get previous status before hidden
    let previousStatusId = await this.reviewRepo.getPreviousStatusBeforeHidden(
      id
    );
    // If not found, get last status before hidden
    if (!previousStatusId) {
      previousStatusId = await this.reviewRepo.getLastStatusBeforeHidden(id);
    }
    if (!previousStatusId) {
      throw new Error(
        "Cannot restore review: No previous status found before it was hidden"
      );
    }
    // Get current status
    const currentStatus = await this.reviewRepo.getCurrentReviewStatus(id);
    // Update status
    await this.reviewRepo.updateReviewStatus(id, previousStatusId, adminId);
    // Log action
    await this.reviewRepo.logReviewAction(
      id,
      "restore",
      currentStatus,
      previousStatusId,
      "Review restored",
      adminId
    );
    // Add note
    await this.addReviewNote(id, "Review restored", adminId);
  }
  async getStatusIdByName(statusName: string): Promise<number | null> {
       const currentStatus = await this.reviewRepo.getStatusIdByName(statusName);
    if (currentStatus !== null) {
      return currentStatus.id;
    } else {
      // Fallback to correct status IDs based on seeds
      switch (statusName.toLowerCase()) {
        case "pending":
          return 3; // Pending status ID from seeds
        case "approved":
        case "published":
        case "publish":
        case "confirmed":
          return 30; // Confirmed status ID from seeds (used as Approved for reviews)
        case "rejected":
          return 8; // Rejected status ID from seeds
        case "deleted":
        case "hidden":
          return 31; // Hidden status ID from seeds
        default:
          throw new Error(`Status '${statusName}' not found`);
      }
    }
  }
   
}
