import Stripe from "stripe";
import stripeKeys from "../../../config/stripeConfigs";
import { CardInfo } from "../../../dto/billing/CardInfoDTO";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

export class BillingFunction {
  /** Resolve card info using only the subscription id (no PI/Charge needed). */
  static async getCardFromSubscription(subId: string): Promise<CardInfo> {
    // 1) Subscription (default PM + customer id)
    const sub = await stripe.subscriptions.retrieve(subId, {
      expand: [
        "default_payment_method",
        "customer",
        "customer.invoice_settings.default_payment_method",
      ],
    });

    // Try subscription.default_payment_method first
    const subPM = sub.default_payment_method as Stripe.PaymentMethod | null;
    if (subPM && subPM.type === "card" && subPM.card) {
      return {
        brand: subPM.card.brand ?? null,
        last4: subPM.card.last4 ?? null,
        expMonth: subPM.card.exp_month ?? null,
        expYear: subPM.card.exp_year ?? null,
      };
    }

    // 2) Customer default payment method
    const customerId =
      typeof sub.customer === "string" ? sub.customer : sub.customer?.id;
    if (customerId) {
      const customer = await stripe.customers.retrieve(customerId, {
        expand: ["invoice_settings.default_payment_method"],
      });

      const custPM = (customer as any)?.invoice_settings
        ?.default_payment_method as Stripe.PaymentMethod | string | undefined;

      if (
        custPM &&
        typeof custPM !== "string" &&
        custPM.type === "card" &&
        custPM.card
      ) {
        return {
          brand: custPM.card.brand ?? null,
          last4: custPM.card.last4 ?? null,
          expMonth: custPM.card.exp_month ?? null,
          expYear: custPM.card.exp_year ?? null,
        };
      }

      // 3) Fallback: first attached card
      const list = await stripe.paymentMethods.list({
        customer: customerId,
        type: "card",
        limit: 1,
      });
      const first = list.data[0];
      if (first?.type === "card" && first.card) {
        return {
          brand: first.card.brand ?? null,
          last4: first.card.last4 ?? null,
          expMonth: first.card.exp_month ?? null,
          expYear: first.card.exp_year ?? null,
        };
      }
    }

    // No card found
    return { brand: null, last4: null, expMonth: null, expYear: null };
  }

  // ---------- Helpers (private, small, single-purpose) ----------
  static toNumber(v: any, fallback: number) {
    const n = Number(v);
    return Number.isFinite(n) && n > 0 ? n : fallback;
  }

  static formatAmount(value: number, currency?: string) {
    const cur = (currency || "AED").toUpperCase();
    return `${cur} ${Number(value || 0).toLocaleString(undefined, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })}`;
  }
}
