import { db } from "../../config/database";
import { NOTESQUERIES } from "../../utils/database/queries/NotesQueries";

export class NoteRepository {
  async createNote(
    entityId: number,
    note: string,
    entityType: string
  ) {
    const params = [
      10, // p_fnid (create note)
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null, // lead-related placeholders
      entityType, // p_entity_type
      entityId, // p_entity_id
      note, // p_note
      null,
      null,
      null, // sorting/filter
      1, // page
      10, // size
      null, // search
    ];

    const client = await db.connect();
    try {
      const placeholders = params.map((_, i) => `$${i + 1}`).join(", ");
      const query = NOTESQUERIES.GET_SERVICE_NOTES.replace("{placeholders}", placeholders);
      const { rows } = await client.query(query, params);

      const result = rows[0].sp_leads_notes;
      if (result.type === "error") throw new Error(result.message);

      return result.data;
    } finally {
      client.release();
    }
  }

  async getNotesByEntityId(entityId: number, entityType: string) {
    const client = await db.connect();
    try {
      await client.query("BEGIN");
      const { rows } = await client.query(NOTESQUERIES.GET_ALL_NOTES, [
        entityId,
        entityType,
      ]);
      await client.query("COMMIT");
      return rows;
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    } finally {
      client.release();
    }
  }
}
