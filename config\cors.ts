import dotenv from "dotenv";
dotenv.config();

const allowedOrigins = [
  `${process.env.FRONTEND_URL}`,
  "http://localhost:3000",
  "http://localhost:5000",
  "http://localhost:3001",
  "http://localhost:8080",
  "http://localhost:8081",
  "https://dev-faa.findanyagent.ae",
  "https://dashboard-faa.findanyagent.ae",
  "https://adm.findanyagent.ae",
  "https://findanyagent.ae"
];

const corsOptions = {
  origin: allowedOrigins,
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  credentials: true,
};

export default corsOptions;
