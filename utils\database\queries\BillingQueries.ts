export const BillingQueries = {
  SUMMARY: `
    SELECT
      COALESCE(SUM(CASE WHEN ps.name = 'Paid'      THEN s.price::numeric ELSE 0 END), 0) AS total_revenue,
      COALESCE(SUM(CASE WHEN sp.name = 'Expired'   THEN s.price::numeric ELSE 0 END), 0) AS expired_revenue,
      COALESCE(SUM(CASE WHEN sp.name = 'Cancelled' THEN s.price::numeric ELSE 0 END), 0) AS cancelled_revenue,
      COUNT(*)::int AS total_transactions
    FROM list.subscription s
    LEFT JOIN look."packagetype" pt ON pt.id = s."packageTypeId"
    LEFT JOIN look.status ps        ON ps.id = s."paymentStatusId"
    LEFT JOIN look.status sp        ON sp.id = s."statusId"            -- fixed join
    WHERE s.price::numeric > 0
      AND COALESCE(pt.price::numeric, 0) > 0                           -- exclude free packages too
  `,

  /**
   * List billing transactions with pagination and filtering
   * @param p - Parameters for filtering and pagination
   */
  LIST: (p: any) => `
    WITH base AS (
      SELECT
        s.id,
        s."createdOn"::timestamptz AS date,
        s."profileId"              AS profile_id,
        'Subscription'::text       AS type,
        pt.name                    AS plan_name,
        s.price::numeric           AS amount_value,
        s.currency::text           AS currency,
        ps.name                    AS status,
        s."subscriptionId"         AS stripe_subscription_id,

        -- Agent/Agency display
        CASE
          WHEN LOWER(pf."accountType") = 'individual' THEN
            NULLIF(BTRIM(CONCAT_WS(' ', pf."firstName", pf."lastName")), '')
          ELSE
            a."name"
        END                        AS agent_name,
        pf."email"                 AS agent_email

      FROM list.subscription s
      LEFT JOIN look."packagetype" pt ON pt.id = s."packageTypeId"
      LEFT JOIN look.status ps        ON ps.id = s."paymentStatusId"
      LEFT JOIN prf.profile pf        ON pf.id = s."profileId"
      LEFT JOIN agn.agencies a        ON a."profileId" = s."profileId"
      WHERE s.price::numeric > 0
        AND COALESCE(pt.price::numeric, 0) > 0

        -- search (email, agency name, TXN_000123, plan name)
        AND (
          $1::text IS NULL OR
          pf."email" ILIKE '%' || $1 || '%' OR
          a."name"   ILIKE '%' || $1 || '%' OR
          CONCAT('TXN_', LPAD(s.id::text, 6, '0')) ILIKE '%' || $1 || '%' OR
          pt.name    ILIKE '%' || $1 || '%'
        )

        -- type filter (keep your existing behaviour)
        AND (
          $2::text IS NULL OR
          LOWER($2) = LOWER('All') OR
          LOWER('Subscription') = LOWER($2)
        )

        -- status filter via single param ($3):
        -- if numeric -> match s."paymentStatusId"
        -- else       -> match status name ps.name
        AND (
          $3::text IS NULL
          OR (
            $3 ~ '^[0-9]+$' AND s."paymentStatusId" = $3::int
          )
          OR (
            NOT ($3 ~ '^[0-9]+$') AND LOWER(ps.name) = LOWER($3)
          )
        )

        -- date range (inclusive)
        AND ($4::date IS NULL OR s."createdOn"::date >= $4::date)
        AND ($5::date IS NULL OR s."createdOn"::date <= $5::date)
    )
    SELECT
      id,
      date,
      profile_id,
      type,
      plan_name,
      amount_value,
      currency,
      status,
      stripe_subscription_id,
      agent_name,
      agent_email,
      COUNT(*) OVER() AS total_count
    FROM base
    ORDER BY
      ${
        p.sortBy === "amount"
          ? "amount_value"
          : p.sortBy === "status"
          ? "status"
          : "date"
      } ${p.sortDir === "asc" ? "ASC" : "DESC"}
    LIMIT $6::int OFFSET $7::int
  `,

  /**
   * Parameters for the LIST query
   * @param p - Parameters for filtering and pagination
   */
  LIST_PARAMS: (p: any) => [
    p.search || null, // $1
    p.type || null, // $2  (e.g., 'Subscription' or 'All')
    p.status || null, // $3  ('Paid' | 'Pending' | 'Disputed' OR numeric id as string, e.g. '2')
    p.dateFrom || null, // $4
    p.dateTo || null, // $5
    p.limit, // $6
    p.offset, // $7
  ],
};
