import { TABLE } from "../table";

export const ServiceQueries = {
  // LIST (repo builds WHERE for alias s)
  LIST: (where: string, orderBy: string, limit: number, offset: number) => `
    SELECT
      s.*,
      st.name AS statusName,
      COALESCE(imgs.images, '[]'::json) AS images,
      COALESCE(svc_types.items, '[]'::json) AS services,
      COALESCE(locs.items, '[]'::json) AS locations,
      json_build_object(
        'id', dt.id,
        'name', dt.name
      ) AS durationType,

      -- 👇 Profile or Agency name (same logic as GET_FILTERED_PAYMENTS)
      CASE
        WHEN LOWER(p."accountType") = LOWER('Individual') THEN
          json_build_object(
            'id', p.id,
            'firstName', p."firstName",
            'middleName', p."middleName",
            'lastName', p."lastName",
            'phone', p."phone",
            'email', p."email",
            'profileImage', p."profileImage"
          )
        ELSE
          json_build_object(
            'id', a.id,
            'firstName', a."name",   -- agency name as firstName placeholder
            'middleName', p."middleName",
            'lastName', p."lastName",
            'phone', p."phone",
            'email', p."email",
            'profileImage', p."profileImage"
          )
      END AS profile

    FROM agn.services s

    -- Profile join
    LEFT JOIN prf.profile p
      ON p.id = s."profileId"

    -- Agency join
    LEFT JOIN agn.agencies a
      ON a."profileId" = s."profileId"

    -- Duration join (lookup type)
    LEFT JOIN ${TABLE.TYPE} dt
      ON dt.id = s."duration"

    -- Status join
    LEFT JOIN ${TABLE.STATUS} st
      ON st.id = s."statusId"

    -- Images join
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'id', i.id,
          'serviceId', i."serviceId",
          'url', i."url"
        )
      ) AS images
      FROM prf.images i
      WHERE i."serviceId" = s.id
        AND COALESCE(i."isDeleted", false) = false
    ) imgs ON TRUE

    -- Service types join (multi)
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'value', ls.id,
          'label', ls.name
        ) ORDER BY ls.name
      ) AS items
      FROM ${TABLE.SERVICE_SERVICE_TYPES} sst
      JOIN list.services ls
        ON ls.id = sst."serviceTypeId"
      WHERE sst."serviceId" = s.id
    ) svc_types ON TRUE

    -- Locations join (multi)
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'value', l.id,
          'label', l.name
        ) ORDER BY l.name
      ) AS items
      FROM ${TABLE.SERVICE_LOCATIONS} sl
      JOIN list.location l
        ON l.id = sl."locationId"
      WHERE sl."serviceId" = s.id
    ) locs ON TRUE

    ${where}
    ${orderBy}
    LIMIT ${limit} OFFSET ${offset};
  `,

  LIST_COUNT: (where: string) => `
    SELECT COUNT(*)::int AS total
    FROM agn.services s
    ${where};
  `,

  // Dashboard cards (available cap=3; tweak if needed)
  DASH_COUNTS: `
    WITH active_ct AS (
      SELECT COUNT(*)::int AS c
      FROM agn.services s
      JOIN look.status st ON st.id = s."statusId"
      WHERE s."createdBy" = $1 
        AND LOWER(st."name") = 'activated'
        AND COALESCE(s."isDeleted", false) = false
    ),
    free_ct AS (
      SELECT COUNT(*)::int AS c
      FROM agn.services
      WHERE "createdBy" = $1 AND COALESCE("isFree", false) = true
    ),
    total_ct AS (
      SELECT COUNT(*)::int AS c
      FROM agn.services
      WHERE "createdBy" = $1
    ),
    views_ct AS (
      SELECT COUNT(*)::int AS c
      FROM agn.service_views sv
      JOIN agn.services s ON s.id = sv."serviceId"
      WHERE s."createdBy" = $1
    )
    SELECT
      GREATEST(0, 3 - (SELECT c FROM total_ct))::int AS available_listings,
      (SELECT c FROM active_ct) AS active_listings,
      (SELECT c FROM views_ct)  AS total_views,
      (SELECT c FROM free_ct)   AS free_services;
  `,

  ADMIN_DASH_COUNTS: `
    WITH total_ct AS (
      SELECT COUNT(*)::int AS c
      FROM agn.services s
      WHERE COALESCE(s."isDeleted", false) = false
    ),
    status_ct AS (
      SELECT
        st.id   AS status_id,
        st.name AS status_name,
        COUNT(s.id)::int AS count
      FROM agn.services s
      LEFT JOIN look.status st
        ON st.id = s."statusId"
      WHERE COALESCE(s."isDeleted", false) = false
      GROUP BY st.id, st.name
    )
    SELECT
      (SELECT c FROM total_ct) AS total_services,
      json_agg(
        json_build_object(
          'statusId', sc.status_id,
          'status',   sc.status_name,
          'count',    sc.count
        )
      ) AS services_by_status
    FROM status_ct sc;
  `,

  GET_BY_ID: `
    SELECT
      s.*,
      st.name AS statusName,
      COALESCE(imgs.images, '[]'::json) AS images,
      COALESCE(svc_types.items, '[]'::json) AS services,
      COALESCE(locs.items, '[]'::json) AS locations,
      json_build_object(
        'id', dt.id,
        'name', dt.name
      ) AS durationType,

      -- 👇 Profile or Agency info (same as LIST)
      CASE
        WHEN LOWER(p."accountType") = LOWER('Individual') THEN
          json_build_object(
            'id', p.id,
            'firstName', p."firstName",
            'middleName', p."middleName",
            'lastName', p."lastName",
            'phone', p."phone",
            'email', p."email",
            'profileImage', p."profileImage"
          )
        ELSE
          json_build_object(
            'id', a.id,
            'firstName', a."name",   -- agency name as display
            'middleName', p."middleName",
            'lastName', p."lastName",
            'phone', p."phone",
            'email', p."email",
            'profileImage', p."profileImage"
          )
      END AS profile

    FROM agn.services s

    -- Profile join
    LEFT JOIN prf.profile p
      ON p.id = s."profileId"

    -- Agency join
    LEFT JOIN agn.agencies a
      ON a."profileId" = s."profileId"

    -- Duration join (lookup type)
    LEFT JOIN ${TABLE.TYPE} dt
      ON dt.id = s."duration"

    -- Status join
    LEFT JOIN look.status st
      ON st.id = s."statusId"

    -- Images join
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'id', i.id,
          'serviceId', i."serviceId",
          'url', i."url"
        )
      ) AS images
      FROM prf.images i
      WHERE i."serviceId" = s.id
        AND COALESCE(i."isDeleted", false) = false
    ) imgs ON TRUE

    -- Service types join (multi)
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'value', ls.id,
          'label', ls.name
        ) ORDER BY ls.name
      ) AS items
      FROM ${TABLE.SERVICE_SERVICE_TYPES} sst
      JOIN list.services ls
        ON ls.id = sst."serviceTypeId"
      WHERE sst."serviceId" = s.id
    ) svc_types ON TRUE

    -- Locations join (multi)
    LEFT JOIN LATERAL (
      SELECT json_agg(
        json_build_object(
          'value', l.id,
          'label', l.name
        ) ORDER BY l.name
      ) AS items
      FROM ${TABLE.SERVICE_LOCATIONS} sl
      JOIN list.location l
        ON l.id = sl."locationId"
      WHERE sl."serviceId" = s.id
    ) locs ON TRUE

    WHERE s.id = $1
      AND COALESCE(s."isDeleted", false) = false
    LIMIT 1;
  `,

  CREATE: `
    INSERT INTO agn.services (
      "profileId","agencyId","statusId","createdBy",
      "title","duration","isRemote","isFree",
      "specialOffer","websiteUrl","description","price","createdOn"
    ) VALUES (
      $1,$2,$3,$4,
      $5,$6,$7,$8,$9,
      $10,$11,$12, NOW()
    )
    RETURNING id;
  `,

  UPDATE: `
    UPDATE agn.services SET
      "profileId"   = $2,
      "agencyId"    = $3,
      "statusId"    = $4,
      "modifiedBy"  = $5,
      "modifiedOn"  = NOW(),
      "title"       = $6,
      "duration"    = $7,
      "isRemote"    = $8,
      "isFree"      = $9,
      "specialOffer"= $10,
      "websiteUrl"  = $11,
      "description" = $12,
      "price"       = $13
    WHERE id = $1;
  `,

  // New: bulk insert service types
  SERVICE_TYPES_BULK_INSERT: `
    WITH payload AS (
      SELECT json_array_elements_text($2::json) AS sid
    )
    INSERT INTO agn.service_service_types ("serviceId","serviceTypeId")
    SELECT $1::int, sid::int FROM payload
    ON CONFLICT DO NOTHING;
  `,

  // New: bulk insert service locations
  SERVICE_LOCATIONS_BULK_INSERT: `
    WITH payload AS (
      SELECT json_array_elements_text($2::json) AS lid
    )
    INSERT INTO agn.service_locations ("serviceId","locationId")
    SELECT $1::int, lid::int FROM payload
    ON CONFLICT DO NOTHING;
  `,

  // New: clear mappings before re-inserting
  SERVICE_TYPES_DELETE_BY_SERVICE: `
    DELETE FROM agn.service_service_types WHERE "serviceId" = $1;
  `,

  SERVICE_LOCATIONS_DELETE_BY_SERVICE: `
    DELETE FROM agn.service_locations WHERE "serviceId" = $1;
  `,

  UPDATE_STATUS: `
    UPDATE agn.services
    SET "statusId" = $2, "modifiedOn" = NOW()
    WHERE id = $1;
  `,

  DELETE: `
    UPDATE agn.services
    SET "isDeleted" = TRUE,
        "modifiedOn" = NOW()
    WHERE id = $1;
  `,

  // Images
  PHOTOS_BULK_INSERT: `
    WITH payload AS (
      SELECT json_array_elements_text($5::json) AS u
    )
    INSERT INTO prf.images ("profileId","serviceId","statusId","createdBy","url")
    SELECT
      $2::int AS "profileId",
      $1::int AS "serviceId",
      $3::int AS "statusId",
      $4::int AS "createdBy",
      u       AS "url"
    FROM payload;
  `,

  PHOTOS_DELETE_MANY: `
    DELETE FROM prf.images
    WHERE id = ANY($1::int[])
      AND COALESCE("isDeleted", false) = false;
  `,

  // Views
  ADD_VIEW_EVENT: `
    INSERT INTO agn.service_views ("serviceId","viewerId")
    VALUES ($1,$2)
    RETURNING id;
  `,

  VIEWS_FOR_AGENT_TOTAL: `
    SELECT COUNT(*)::int AS total
    FROM agn.service_views sv
    JOIN agn.services s ON s.id = sv."serviceId"
    WHERE s."createdBy" = $1;
  `,

  PHOTOS_SELECT_BY_ID: `
    SELECT id,
          "serviceId",
          url,
          title,
          description,
          "createdOn",
          "createdBy"
    FROM prf.images
    WHERE id = ANY($1::int[]);
  `,

  FIND_BY_PROFILE_WITH_SERVICE: `
    SELECT 
      s.id AS value,
      ls.name AS label
    FROM prf.services s
    LEFT JOIN list.services ls
      ON s."serviceId" = ls.id
    WHERE s."profileId" = $1
    ORDER BY s."createdOn" DESC;
  `,

  FIND_CHILD_TYPES_BY_PARENT: `
    SELECT 
      d.id,
      d.name,
      COALESCE(
        json_agg(
          json_build_object(
            'id', c.id,
            'name', c.name,
            'parentId', c."parentId"
          )
        ) FILTER (WHERE c.id IS NOT NULL),
        '[]'
      ) AS children
    FROM ${TABLE.TYPE} d
    LEFT JOIN ${TABLE.TYPE} c
      ON c."parentId" = d.id
    WHERE LOWER(d.name) = LOWER($1)
    GROUP BY d.id, d.name
    ORDER BY d.id DESC;
  `,
};
