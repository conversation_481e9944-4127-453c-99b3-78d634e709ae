import { z } from "zod";
import { Request, Response, NextFunction } from "express";
import { error } from "../response";
 

const leadIdsSchema = z.object({
  leadIds: z.array(z.number().int().positive())
});

export function validateLeadIds(req: Request, res: Response, next: NextFunction): void {
  const result = leadIdsSchema.safeParse(req.body);
  if (!result.success) {
    error(res, 400, "All lead IDs must be valid positive integers.");
    return;
  }
  req.body.leadIds = result.data.leadIds; 
  next();
}