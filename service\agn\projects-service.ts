import { ProjectsRepository } from "../../repo/agn/projects-repository";

export class ProjectsService {

    private projectsRepo = new ProjectsRepository();

    async getProjectsCountByProfileId(id: number) {
        const count = await this.projectsRepo.getProjectsCountByProfileId(id);
        const projectsCountData = {
            ProjectsCount: count,
            ProjectsAllowedLimit: 10
        }
        return projectsCountData;
    }
}