import express from "express";
import {
  create<PERSON>alesperson,
  deleteSalesperson,
  getAllSalespersons,
  getSalespersonById,
  getSalespersonReferals,
  sendSalespersonReferralsCSV,
  updateSalesperson,
  updateSalespersonStatus,
} from "../../../controller/admin/salesperson.controller";
import { storageData } from "../../../utils/services/multer";
import { getPaymentsBySalesperson, sendPaymentsBySalesperson } from "../../../controller/admin/referral-management";
const router = express.Router();

const upload = storageData("salespersons");

// -------------------- ROUTES --------------------
router.get("/", getAllSalespersons); // List all
router.get("/commissions/:salespersonId", getSalespersonReferals); // List all
router.get("/commissions/reports/:salespersonId", sendSalespersonReferralsCSV); // List all
router.get("/commissions/payments/:salespersonId", getPaymentsBySalesperson); // List all
router.get("/commissions/paymentsemail/:salespersonId", sendPaymentsBySalesperson); // List all
router.get("/:id", getSalespersonById); // Get by ID
router.post("/", upload.none(), createSalesperson); // Create
router.put("/:id", upload.none(), updateSalesperson); // Update
router.delete("/:id", deleteSalesperson); // Delete
router.patch("/:id/status", upload.none(), updateSalespersonStatus); // Update status only

export default router;
