import { Router } from "express";
import { PaymentController } from "../../../controller/agents/payments/PaymentController";

const router = Router();
const paymentController = new PaymentController();

// GET all payments with pagination, filters, sorting
router.get("/", paymentController.getAll);

// GET payment by ID (full detail for invoice, etc.)
router.get("/invoices", paymentController.getInvoices);

// GET current subscription details for the logged-in user
router.get("/subscription-details", paymentController.getCurrentSubscription);

router.get("/:id", paymentController.getById);

// Optional: GET filter options for UI dropdowns
router.get("/filters/options", paymentController.getFilterOptions);

export default router;
