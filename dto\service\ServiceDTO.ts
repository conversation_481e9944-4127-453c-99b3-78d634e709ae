import { z } from "zod";

/** Coercions */
export const zInt = z.coerce.number().int();
export const zNum = z.coerce.number();

/** helper for single-or-array → array */
const zIds = z
  .union([z.array(zInt), zInt])
  .transform((v) => (Array.isArray(v) ? v : [v]))
  .refine((arr) => arr.length > 0, {
    message: "At least one selection is required",
  });


/** CREATE */
export const CreateServiceSchema = z
  .object({
    profileId: zInt,
    agencyId: zInt.nullish().transform((v) => (v == null ? null : Number(v))),

    // ✅ new: arrays
    serviceIds: zIds,
    locationIds: zIds.optional().default([]),

    statusId: zInt,
    createdBy: zInt,

    title: z
      .string()
      .trim()
      .min(1, { message: "Service title is required" })
      .max(255, { message: "Service title cannot exceed 255 characters" }),

    duration: zInt.refine((v) => v > 0, {
      message: "Duration is required and must be greater than zero",
    }),

    description: z
      .string()
      .trim()
      .min(1, { message: "Service description is required" }),

    isRemote: z.coerce.boolean().default(false),
    isFree: z.coerce.boolean().default(false),

    specialOffer: z.string().trim().max(255).optional(),
    websiteUrl: z.string().url().optional(),

    price: zNum.nullish().refine((v) => v == null || v >= 0, {
      message: "Price must be a non-negative number",
    }),
  })
  // Conditional validation
  .superRefine((data, ctx) => {
    // If not free => price required
    if (
      data.isFree === false &&
      (data.price === null || data.price === undefined || data.price <= 0)
    ) {
      ctx.addIssue({
        code: "custom",
        path: ["price"],
        message:
          "Price is required and must be greater than 0 when the service is not free",
      });
    }

    // If not remote => at least one locationId required
    if (
      data.isRemote === false &&
      (!data.locationIds || data.locationIds.length === 0)
    ) {
      ctx.addIssue({
        code: "custom",
        path: ["locationIds"],
        message: "Select at least one location when the service is not remote",
      });
    }
  });

export type CreateServiceDTO = z.infer<typeof CreateServiceSchema>;
