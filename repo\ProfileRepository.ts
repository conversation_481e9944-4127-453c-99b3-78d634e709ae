import { db } from "../config/database";
import { ProfileQueries } from "../utils/database/queries/ProfileQueries";
import { TABLE } from "../utils/database/table";


export class ProfileRepository {

  // Query method for SELECT_PROFILE_WITH_LOGIN
  async selectProfileWithLogin(id: number) {
    const { rows } = await db.query(ProfileQueries.SELECT_PROFILE_WITH_LOGIN, [id]);
    return rows[0];
  }

  // Query method for UPDATE_PROFILE_BASIC
  async updateProfileBasicQuery(profileImage: string, firstName: string, middleName: string, lastName: string, id: number) {
    const result = await db.query(ProfileQueries.UPDATE_PROFILE_BASIC, [profileImage, firstName, middleName, lastName, id]);
    
    if (result.rowCount === 0) {
      throw new Error("Failed to update profile basic information - profile not found");
    }
    
    return result.rows[0];
  }

  // Query method for UPDATE_PROFILE_CONTACT
  async updateProfileContactQuery(locationId: number, address: string, id: number) {
    const result = await db.query(ProfileQueries.UPDATE_PROFILE_CONTACT, [locationId, address, id]);
    
    if (result.rowCount === 0) {
      throw new Error("Failed to update profile contact information - profile not found");
    }
    
    return result.rows[0];
  }

  // Query method for UPDATE_PROFILE_PROFESSIONAL_INFO
  async updateProfileProfessionalInfoQuery(experience: number, details: string, id: number) {
    const result = await db.query(ProfileQueries.UPDATE_PROFILE_PROFESSIONAL_INFO, [experience, details, id]);
    
    if (result.rowCount === 0) {
      throw new Error("Failed to update professional information - profile not found");
    }
    
    return result.rows[0  ];
  }

  // Query method for GET_PROFILE_PASSWORD
  async getProfilePasswordQuery(id: number) {
    const { rows } = await db.query(ProfileQueries.GET_PROFILE_PASSWORD, [id]);
    return rows[0]?.passwordHash;
  }

  // Query method for UPDATE_PROFILE_STATUS
  async updateProfileStatusQuery(statusId: number, id: number) {
    const result = await db.query(ProfileQueries.UPDATE_PROFILE_STATUS, [statusId, id]);
    
    if (result.rowCount === 0) {
      throw new Error("Failed to update profile status - profile not found");
    }
    
    return result.rows[0];
  }

  // Business logic methods that use the query methods
  async getProfileById(id: number) {
    return await this.selectProfileWithLogin(id);
  }

  async updateProfileBasic(id: number, profileImage: string, firstName: string, lastName: string, middleName: string) {
    return await this.updateProfileBasicQuery(profileImage, firstName, middleName, lastName, id);
  }

  async updateProfileContact(id: number, locationId: number, address: string) {
    return await this.updateProfileContactQuery(locationId, address, id);
  }

  async updateProfessionalInfo(id: number, details: string, experience: number) {
    return await this.updateProfileProfessionalInfoQuery(experience, details, id);
  }

  async getPreviousPassword(id: number) {
    return await this.getProfilePasswordQuery(id);
  }

  async updateProfileStatus(id: number, statusId: number) {
    return await this.updateProfileStatusQuery(statusId, id);
  }

}

