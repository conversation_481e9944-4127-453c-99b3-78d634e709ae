import { sql } from "drizzle-orm";
import { PoolClient } from "pg";
import { drizzleDb } from "../config/database";

export class AgentLicensesRepository {

    async addAgentLicense(
        agentId: number,
        roleId: number,
        roleType: string,
        hasLicense: boolean,
        licenseNumber: string | null,
        licenseExpiryDate: string | null,
        licenseFile: string[] | null,
        dbClient: PoolClient
    ) {
        const query = `
    INSERT INTO agentlicenses (
      "agentId", "roleId", "roletype", "hasLicense", "licenseNumber", 
      "licenseexpiryDate", "licenseFile"
    )
    VALUES ($1, $2, $3, $4, $5, $6, $7)
    RETURNING *;
  `;
        const values = [
            agentId,
            roleId,
            roleType,
            hasLicense,
            licenseNumber,
            licenseExpiryDate,
            licenseFile
        ];

        return dbClient.query(query, values);
    }

    async getLicensesByAgentIdAndRoleId(agentId: number, roleId: number) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM agentLicenses 
            WHERE "agentId" = ${agentId} 
              AND "roleId" = ${roleId}`
        );
        return result;
    }


}