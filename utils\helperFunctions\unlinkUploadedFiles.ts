import fs from 'fs/promises';
import { promisify } from 'util';
const unlinkAsync = promisify(fs.unlink);

export const unlinkUploadedFiles = async (files: any) => {
  await Promise.all(
    files.map(async (filePath: string) => {
      try {
        await fs.unlink(filePath);
      } catch (err: any) {
        // Ignore if file does not exist, log other errors
        if (err.code !== 'ENOENT') console.error('File unlink error:', err);
      }
    })
  );
}

export async function safeUnlink(p: string) {
  try {
    await unlinkAsync(p);
  } catch (e) {
    console.log(e)
  }
}