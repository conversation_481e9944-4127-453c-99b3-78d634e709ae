import { TABLE } from "../table";

export const WEBHOOK = {
  GET_PACKAGE_TYPE_ID_BY_STRIPE_PLAN_ID:
    'SELECT "id" FROM look.packagetype WHERE "stripePlanId" = $1 LIMIT 1',
  GET_PACKAGE:
    "SELECT * FROM look.sp_packagetype($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13, $14, $15)",
  GET_ACTIVE_SUBSCRIPTION: `SELECT id FROM list.subscription
           WHERE "profileId" = $1 AND "packageTypeId" = $2 AND "statusId" = 1
           LIMIT 1`,
  UPDATE_SUBSCRIPTION: `UPDATE list.subscription
             SET "startDate" = $1,
                 "endDate"   = $2,
                 "modifiedOn"= $3,
                 "subscriptionId" = $4
           WHERE id = $5`,
  GET_OTHER_ACTIVE_SUBSCRIPTION: `SELECT id FROM list.subscription
             WHERE "profileId" = $1 AND "packageTypeId" <> $2 AND "statusId" = 1`,
  CANCEL_SUBSCRIPTION: `UPDATE list.subscription
               SET "statusId" = $1
             WHERE id = $2`,
  CREATE_SUBSCRIPTION:
    "SELECT * FROM look.sp_subscription($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14, $15, $16)",
   UPDATE_CUSTOMER_ID_IN_PROFILE: `
        UPDATE prf.profile
        SET
          "customerId" = $1
        WHERE id = $2
        RETURNING *;
      `,
  GET_SUBSCRIPTION_BY_SUBSCRIPTION_ID:   `SELECT id FROM list.subscription WHERE "subscriptionId" = $1`,
  GET_REFERRAL_BY_USER_ID:  `SELECT * FROM "referralRecords" WHERE "userId" = $1`,
  GET_SALES_PERSON_BY_ID:  `SELECT * FROM ${TABLE.SALES_PERSONS} WHERE "id" = $1`,
  GET_COMMISSION_BY_USER_AND_SALES_PERSON: `SELECT * FROM ${TABLE.COMMISION} WHERE "userId" = $1 AND "salesPersonId" = $2 WHERE "isSubscribed" = false`,
  UPDATE_COMMISSION: `UPDATE ${TABLE.COMMISION} SET "commissionAmount" = $1, "isSubscribed" = true, "status" = $2, "subscriptionId" = $3, "referralId" = $4 WHERE id = $5 RETURNING *`,
   INSERT_COMMISSION: `INSERT INTO ${TABLE.COMMISION} ("userId", "salesPersonId", "commissionAmount", "status", "subscriptionId", "referralId", "isSubscribed") VALUES ($1, $2, $3, $4, $5, $6, true) RETURNING *`,
};
