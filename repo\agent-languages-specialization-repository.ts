import { db } from "../config/database";
import { TABLE } from "../utils/database/table";

export class AgentLanguagesSpecializationRepository {

  // Query method for DELETE_AGENT_LANGUAGE
  async deleteAgentLanguagesQuery(profileId: number) {
    const result = await db.query(
      `DELETE FROM "${TABLE.AGENT_LANGUAGE}"
       WHERE "profileId" = $1`,
      [profileId]
    );
    return result;
  }

  // Query method for ADD_UPDATE_PROFILE_LANGUAGES
  async addAgentLanguageQuery(languageId: number, profileId: number) {
    const result = await db.query(
      `INSERT INTO "${TABLE.AGENT_LANGUAGE}" ("languageId", "profileId")
       VALUES ($1, $2)`,
      [languageId, profileId]
    );
    return result;
  }

  // Query method for FETCH_ALL_AGENT_SPECIALIZATION
  async fetchAllAgentSpecializationsQuery(profileId: number) {
    const { rows } = await db.query(
      `SELECT "name", "statusId" FROM "${TABLE.AGENT_SPECIALIZATION}" WHERE "profileId" = $1`,
      [profileId]
    );
    return rows;
  }

  // Query method for SET_SPECIALIZATION_STATUS
  async setSpecializationStatusQuery(statusId: number, name: string, profileId: number) {
    const result = await db.query(
      `UPDATE "${TABLE.AGENT_SPECIALIZATION}"
       SET "statusId" = $1
       WHERE "name" = $2 AND "profileId" = $3
       RETURNING *`,
      [statusId, name, profileId]
    );
    return result;
  }

  // Query method for ADD_UPDATE_PROFILE_SPECIALIZATION
  async addAgentSpecializationQuery(name: string, profileId: number) {
    const result = await db.query(
      `INSERT INTO "${TABLE.AGENT_SPECIALIZATION}" ("name", "profileId")
       VALUES ($1, $2)`,
      [name, profileId]
    );
    return result;
  }

  // Business logic methods that use the query methods
  async deleteAgentLanguages(profileId: number) {
    return await this.deleteAgentLanguagesQuery(profileId);
  }

  async addAgentLanguage(languageId: number, profileId: number) {
    return await this.addAgentLanguageQuery(languageId, profileId);
  }

  async fetchAllAgentSpecializations(profileId: number) {
    return await this.fetchAllAgentSpecializationsQuery(profileId);
  }

  async setSpecializationStatus(statusId: number, name: string, profileId: number) {
    return await this.setSpecializationStatusQuery(statusId, name, profileId);
  }

  async addAgentSpecialization(name: string, profileId: number) {
    return await this.addAgentSpecializationQuery(name, profileId);
  }

  async updateAgentLanguagesAndSpecializations(profileId: number, languages: number[], specializations: string[]) {
    // Delete existing languages
    await this.deleteAgentLanguages(profileId);

    // Add new languages
    for (const languageId of languages) {
      await this.addAgentLanguage(languageId, profileId);
    }

    // Get all existing specializations
    const allSpecsRows = await this.fetchAllAgentSpecializations(profileId);
    const allSpecs = allSpecsRows.reduce((acc: any, row: any) => {
      acc[row.name] = row.statusId;
      return acc;
    }, {});

    // Update existing specializations or add new ones
    for (const spec of specializations) {
      if (allSpecs[spec]) {
        const result = await this.setSpecializationStatus(1, spec, profileId);
        if (result.rowCount === 0) {
          throw new Error(`Failed to update specialization status for: ${spec}`);
        }
      } else {
        await this.addAgentSpecialization(spec, profileId);
      }
    }

    // Deactivate specializations not in the new list
    for (const existingSpec in allSpecs) {
      if (!specializations.includes(existingSpec)) {
        const result = await this.setSpecializationStatus(2, existingSpec, profileId);
        if (result.rowCount === 0) {
          throw new Error(`Failed to deactivate specialization: ${existingSpec}`);
        }
      }
    }

    return { success: true };
  }
}
