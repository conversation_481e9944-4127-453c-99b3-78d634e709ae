import z from "zod";
import { ProfileStatus } from "../enums/profile-status.enum";
 
export const profileUpdateSchema = z.object({
  profileImage: z.string().trim().nonempty("Profile image is required"),
  firstName: z
    .string()
    .trim()
    .min(2, "First name must be at least 2 characters")
    .nonempty("First name is required"),
  lastName: z
    .string()
    .trim()
    .min(2, "Last name must be at least 2 characters")
    .nonempty("Last name is required"),
  nationality: z.string().trim().nonempty("Nationality is required"),
  gender: z.string().trim().nonempty("Gender is required"),
}); 

export const profileLocationSchema = z.object({
  locationId: z
    .number()
    .refine((val) => !isNaN(val), { message: "Location ID must be a number" }),
  address: z.string().trim().nonempty("Address is required"),
});

 
export const professionalInfoSchema = z.object({
  details: z.string().nullable(),
  experience: z.number().nullable(),
  languages: z.array(z.number()).nullable(),
  specialization: z.array(z.string()).nullable(),
});

export const profileStatusSchema = z.object({
  status: z.string().refine(
    (value) => Object.values(ProfileStatus).includes(value as ProfileStatus),
    {
      message: `Status must be either '${ProfileStatus.ACTIVATED}' or '${ProfileStatus.DEACTIVATED}'`
    }
  )
});
