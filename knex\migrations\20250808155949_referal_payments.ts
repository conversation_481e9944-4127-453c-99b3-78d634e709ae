import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable("referal_payments", function (table) {
    table.increments("id").primary();
    table.bigInteger("salesPersonId").notNullable();
    table.text("period").notNullable();
    table.text("amount").nullable();
    table.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {}
