// migrations/20250822_create_service_views.ts
import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable("agn.service_views", (t) => {
    t.increments("id").primary();
    t.integer("serviceId")
      .notNullable()
      .references("id")
      .inTable("agn.services")
      .onDelete("CASCADE");
    t.integer("viewerId")
      .nullable()
      .references("id")
      .inTable("prf.profile")
      .onDelete("CASCADE");
    t.timestamps(true, true);
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTable("agn.service_views");
}
