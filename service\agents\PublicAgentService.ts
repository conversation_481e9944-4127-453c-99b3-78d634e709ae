import { PublicAgentRepository } from "../../repo/agents/PublicAgentRepository";

export class PublicAgentService {
  private repo = new PublicAgentRepository();

  async getAllAgents() {
    return this.repo.getAllAgents();
  }

  async getPropertiesByProfileId(profileId: number) {
    const agent = await this.repo.getAgentIdByProfile(profileId);
    const agentId = agent.rows[0]?.id;
    if (!agentId) {
      throw new Error("Agent not found for the given profile ID");
    }
    return this.repo.getPropertiesByAgentId(agentId);
  }
}
