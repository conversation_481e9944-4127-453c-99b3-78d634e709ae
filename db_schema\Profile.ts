import { integer, serial, text, timestamp, pgSchema, jsonb, bigint, boolean, date, varchar } from "drizzle-orm/pg-core";

export const prfSchema = pgSchema("prf");

export const profile = prfSchema.table(
  "profile",
  {
    id: serial("id").notNull().primaryKey(),
    code: varchar("code", { length: 255 }),
    firstName: varchar("firstName", { length: 255 }),
    middleName: text("middleName"),
    lastName: varchar("lastName", { length: 255 }),
    localName: text("localName"),
    emiratesId: text("emiratesId"),
    nationalityId: integer("nationalityId"),
    locationId: integer("locationId"),
    address: text("address"),
    email: text("email"),
    phone: text("phone"),
    typeId: integer("typeId"),
    statusId: integer("statusId"),
    createdBy: integer("createdBy"),
    createdOn: timestamp("createdOn", { withTimezone: true, precision: 6 }),
    modifiedBy: integer("modifiedBy"),
    modifiedOn: timestamp("modifiedOn", { withTimezone: true, precision: 6 }),
    designation: varchar("designation", { length: 256 }),
    shortDescription: text("shortDescription"),
    description: text("description"),
    specialization: text("specialization"),
    experience: varchar("experience", { length: 255 }),
    languages: text("languages"),
    industry: varchar("industry", { length: 255 }),
    certified: boolean("certified").default(false),
    certificateNumber: varchar("desicertificateNumbergnation", { length: 256 }),
    expiryDate: date("expiryDate"),
    contactNumber: varchar("contactNumber", { length: 256 }),
    whatsappContact: varchar("whatsappContact", { length: 256 }),
    contactEmail: varchar("contactEmail", { length: 256 }),
    cardHolderName: varchar("cardHolderName", { length: 256 }),
    cardType: varchar("cardType", { length: 256 }),
    cardNumber: bigint("card_number", { mode: "number" }),
    accountType: varchar("accountType", { length: 255 }),
    association: boolean("association").default(false),
    issuedBy: varchar("issuedBy", { length: 255 }),
    profileImage: varchar("profileImage", { length: 255 }),
    isProfileCompleted: boolean("isProfileCompleted").default(true),
    rejectionReason: text("rejectionReason"),
    tempData: jsonb("tempData"),
    requiredFields: jsonb("requiredFields"),
    isLicensed: boolean("isLicensed").default(false),
    licenseTag: varchar("licenseTag", { length: 255 }).default("'No Selection'::character varying"),
    customerId: varchar("customerId", { length: 255 })
  }
);