import { BillingRepository } from "../../repo/billings/BillingRepository";
import { CardInfo } from "../../dto/billing/CardInfoDTO";
import { AdminBillingListResponse } from "../../dto/billing/AdminBillingListResponseDTO";
import { AdminBillingTransaction } from "../../dto/billing/AdminBillingTransactionDTO";
import { DbListParams } from "../../dto/billing/DbListParamsDTO";
import { BillingFunction } from "../../utils/helperFunctions/billing/BillingFunction";
import { PropertyHelper } from "../../utils/helperFunctions/property/PropertyHelper";

const formatRef = (id: number) => `TXN_${String(id).padStart(6, "0")}` as const;

export class BillingService {
  private repo = new BillingRepository();

  // ---------- Summary ----------
  async getSummary() {
    const r = await this.repo.getSummaryRow();
    return {
      totalRevenue: Number(r.total_revenue || 0),
      pendingRefunds: Number(r.expired_revenue || 0),
      disputedAmount: Number(r.cancelled_revenue || 0),
      totalTransactions: Number(r.total_transactions || 0),
    };
  }

  // ---------- List Transactions ----------
  async getTransactions(q: any): Promise<AdminBillingListResponse> {
    const page = BillingFunction.toNumber(q.page, 1);
    const pageSize = BillingFunction.toNumber(q.pageSize, 10);

    const statusId = await this.repo.getStatusIdByName(q.status);

    const dbParams: DbListParams = {
      search: q.search || "",
      type: q.type || "",
      status: statusId,
      dateFrom: q.dateFrom || null,
      dateTo: q.dateTo || null,
      sortBy: PropertyHelper.pick(q.sortBy, ["date", "amount", "status"], "date"),
      sortDir: PropertyHelper.pick(q.sortDir, ["asc", "desc"], "desc"),
      limit: pageSize,
      offset: (page - 1) * pageSize,
    };

    const { rows } = await this.repo.listTransactions(dbParams);

    const total = rows[0]?.total_count ? Number(rows[0].total_count) : 0;
    const totalPages = Math.ceil(total / pageSize);

    const transactions: AdminBillingTransaction[] = await Promise.all(
      rows.map((r: any) => this.mapRowToTransaction(r))
    );

    return {
      transactions,
      pagination: { total, totalPages, currentPage: page, perPage: pageSize },
    };
  }

  async getCardInfo(subscriptionId?: string | null): Promise<CardInfo> {
    if (!subscriptionId) {
      return { brand: null, last4: null, expMonth: null, expYear: null };
    }
    try {
      return await BillingFunction.getCardFromSubscription(subscriptionId);
    } catch {
      // swallow Stripe errors, keep base row
      return { brand: null, last4: null, expMonth: null, expYear: null };
    }
  }

  async mapRowToTransaction(r: any): Promise<AdminBillingTransaction> {
    const card = await this.getCardInfo(r.stripe_subscription_id);
    const amount = Number(r.amount_value || 0);

    return {
      id: r.id,
      profileId: r.profile_id,
      agentName: r.agent_name,
      agentEmail: r.agent_email,
      amount,
      amountFormatted: BillingFunction.formatAmount(amount, r.currency),
      type: r.type,
      status: r.status,
      description: r.plan_name ? `${r.plan_name} - ${r.type}` : r.type,
      date: new Date(r.date).toISOString(),
      paymentMethod: card.last4 ? `Credit Card ***${card.last4}` : "Card / N/A",
      reference: formatRef(r.id),
      stripe: {
        subscriptionId: r.stripe_subscription_id || null,
        customerId: undefined,
        paymentIntentId: undefined,
        chargeId: undefined,
        cardBrand: card.brand,
        cardLast4: card.last4,
        cardExpMonth: card.expMonth,
        cardExpYear: card.expYear,
      },
    };
  }
}
