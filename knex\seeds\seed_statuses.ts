import type { Knex } from "knex";

export async function seed(knex: Knex): Promise<void> {
  // Deletes ALL existing entries
  await knex("look.status").del();

  // Inserts seed entries
  await knex("look.status").insert([
    { id: 1, name: "Activated", description: "Account is active" },
    { id: 2, name: "Deactivated", description: "Account is deactivated" },
    { id: 3, name: "Pending", description: "Waiting for approval" },
    {
      id: 4,
      name: "Suspended",
      description: "Account is temporarily suspended",
    },
    {
      id: 5,
      name: "Archived",
      description: "Account is archived and cannot be used",
    },
    {
      id: 6,
      name: "Incomplete",
      description: "Profile is incomplete and requires additional information",
    },
    {
      id: 7,
      name: "Blacklisted",
      description: "Restrict access, can submit an appeal",
    },
    { id: 8, name: "Rejected", description: "Profile is rejected." },
    { id: 9, name: "Expired Soon", description: "Expired soon status." },
    { id: 10, name: "Verified", description: "Verified" },
    { id: 11, name: "Expired", description: "Expired status." },
    { id: 12, name: "Cancelled", description: "Cancelled" },
    { id: 13, name: "Paid", description: "Paid" },
    { id: 14, name: "New", description: "New Status" },
    { id: 15, name: "Contacted", description: "Contacted Status" },
    { id: 16, name: "Qualified", description: "Qualified Status" },
    { id: 17, name: "Lost", description: "Lost Status" },
    { id: 18, name: "Converted", description: "Converted Status" },
    { id: 19, name: "Re-opened", description: "Re-opened Status" },
    { id: 20, name: "Active", description: "Active Status" },
    { id: 21, name: "Inactive", description: "Inactive Status" },
    { id: 22, name: "Publish", description: "Publish status" },
    { id: 23, name: "Draft", description: "Draft status" },
    { id: 24, name: "Available", description: "Available status" },
    { id: 25, name: "Sold", description: "Sold status" },
    { id: 26, name: "Rented", description: "Rented status" },
    { id: 27, name: "Unpublished", description: "Unpublished status" },
    { id: 28, name: "Subscribe", description: "Subscribe status" },
    { id: 29, name: "UnSubscribed", description: "UnSubscribed status" },
    { id: 30, name: "Confirmed", description: "Confirmed status" },
    { id: 31, name: "Hidden", description: "Hidden status" },
    { id: 32, name: "Blocked", description: "Blocked status" },
  ]);
}
