import { db } from "../config/database";

export class AgentDetailsRepository {

  // Query method for updating agent details gender/nationality
  async updateAgentDetailsGenderQuery(nationality: string, gender: string, profileId: number) {
    const result = await db.query(
      `UPDATE agn."agentDetails"
       SET
         "nationality" = $1,
         "gender" = $2
       WHERE "profile_id" = $3
       RETURNING *`,
      [nationality, gender, profileId]
    );

    if (result.rowCount === 0) {
      throw new Error("Failed to update agent details - agent details not found");
    }

    return result.rows[0];
  }

  // Business logic method
  async updateAgentDetailsGender(profileId: number, nationality: string, gender: string) {
    return await this.updateAgentDetailsGenderQuery(nationality, gender, profileId);
  }
}
