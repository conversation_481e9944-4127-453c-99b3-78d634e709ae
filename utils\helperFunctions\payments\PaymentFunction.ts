import Stripe from "stripe";
import { PaymentDTO } from "../../../dto/payment/PaymentDTO";
import { DbFilterParams } from "../../../dto/payment/DbFilterParamsDTO";
import { CurrentSubscriptionDTO } from "../../../dto/payment/CurrentSubscriptionDTO";
import stripeKeys from "../../../config/stripeConfigs";
import { PropertyHelper } from "../property/PropertyHelper";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

export class PaymentFunction {
  // ---------- sort enums ----------
  static SORT_BY = ["date", "amount", "status"] as const;
  static SORT_DIR = ["asc", "desc"] as const;

  // ---------- tiny utils ----------
  static toISO(sec?: number | null) {
    return sec ? new Date(sec * 1000).toISOString() : null;
  }

  // ---------- filters ----------
  static toDbFilters(query: any, statusId: number | null): DbFilterParams {
    const page = PropertyHelper.toInt(query.page ?? 1, 1);
    const pageSize = PropertyHelper.toInt(query.pageSize ?? 10, 10);

    return {
      search: String(query.search || "").trim() || null,
      planId: query.plan ? Number(query.plan) : null,
      listing:
        query.listing && String(query.listing).toLowerCase() !== "all"
          ? String(query.listing)
          : null,
      type:
        query.type && String(query.type).toLowerCase() !== "all"
          ? String(query.type)
          : null,
      statusId,
      dateFrom: query.dateFrom ? String(query.dateFrom) : null,
      dateTo: query.dateTo ? String(query.dateTo) : null,
      profileId: query.profileId ? Number(query.profileId) : null,
      sortBy: PropertyHelper.pick(query.sortBy, this.SORT_BY, "date"),
      sortDir: PropertyHelper.pick(query.sortDir, this.SORT_DIR, "desc"),
      limit: pageSize,
      offset: (page - 1) * pageSize,
      page,
      pageSize,
    };
  }

  // ---------- mappers ----------
  static mapPaymentRow(r: any): PaymentDTO {
    return {
      id: r.id,
      paymentId: r.payment_id,
      date: r.date,
      type: r.type,
      description: r.description,
      amount: r.amount_formatted,
      amountValue: Number(r.amount_value || 0),
      currency: r.currency,
      statusId: r.status_id,
      statusName: r.status_name,
      invoiceNo: r.invoice_no || null,
      packageTypeId: r.package_type_id,
      packageTypeName: r.package_type_name,
      interval: r.interval,
      profile: r.profile
        ? {
            id: r.profile.id,
            firstName: r.profile.firstName,
            middleName: r.profile.middleName || null,
            lastName: r.profile.lastName,
            phone: r.profile.phone || null,
            email: r.profile.email,
            profileImage: r.profile.profileImage || null,
          }
        : null,
      hostedInvoiceUrl: (r as any).hostedInvoiceUrl ?? null,
      invoicePdfUrl: (r as any).invoicePdfUrl ?? null,
    };
  }

  // ---------- Stripe enrichment ----------
  static async enrichWithStripeInvoiceLinks(
    p: PaymentDTO & { subscriptionId?: string | null }
  ): Promise<PaymentDTO> {
    if (!p.subscriptionId) return p;

    try {
      const sub = await stripe.subscriptions.retrieve(p.subscriptionId, {
        expand: ["latest_invoice"],
      });

      let hostedInvoiceUrl: string | null = null;
      let invoicePdfUrl: string | null = null;

      if (sub.latest_invoice) {
        if (typeof sub.latest_invoice === "string") {
          const inv = await stripe.invoices.retrieve(sub.latest_invoice);
          hostedInvoiceUrl = inv.hosted_invoice_url ?? null;
          invoicePdfUrl = inv.invoice_pdf ?? null;
        } else {
          const inv = sub.latest_invoice as Stripe.Invoice;
          hostedInvoiceUrl = inv.hosted_invoice_url ?? null;
          invoicePdfUrl = inv.invoice_pdf ?? null;
        }
      }

      const { subscriptionId, ...rest } = p;
      return { ...rest, hostedInvoiceUrl, invoicePdfUrl } as PaymentDTO;
    } catch {
      const { subscriptionId, ...rest } = p;
      return rest as PaymentDTO;
    }
  }

  // --- Build Base Subscription DTO ---
  static buildBaseSubscription(row: any): CurrentSubscriptionDTO {
    return {
      plan: {
        name: row.package_type_name ?? null,
        price: row.amount_value != null ? Number(row.amount_value) : null,
        interval: row.interval ?? null,
        currency: row.currency ?? null,
        status: row.status_name ?? null,
      },
      nextBilling: {
        dateISO: null,
        amount: null,
        currency: row.currency ?? null,
        autoRenew: null,
      },
      paymentMethod: null,
    };
  }

  // --- Build Current Subscription (Main Entrypoint) ---
  static async buildCurrentSubscription(
    row: any
  ): Promise<CurrentSubscriptionDTO> {
    const base = this.buildBaseSubscription(row);

    const subscriptionId: string | null =
      row.subscriptionId || row.subscription_id || null;
    const stripeCustomerId: string | null = row.stripe_customer_id || null;

    if (!subscriptionId || !stripeCustomerId) return base;

    try {
      const sub = await this.fetchSubscription(subscriptionId);

      // Update billing info
      this.updateBillingInfo(base, sub);

      // Attach upcoming invoice preview
      await this.attachUpcomingInvoice(
        base,
        stripeCustomerId,
        subscriptionId,
        sub
      );

      // Attach payment method
      base.paymentMethod = await this.resolvePaymentMethod(
        stripeCustomerId,
        sub
      );

      return base;
    } catch {
      return base;
    }
  }

  // --- Stripe Calls ---
  private static async fetchSubscription(subscriptionId: string) {
    return stripe.subscriptions.retrieve(subscriptionId, {
      expand: ["latest_invoice", "default_payment_method"],
    });
  }

  private static async attachUpcomingInvoice(
    base: CurrentSubscriptionDTO,
    stripeCustomerId: string,
    subscriptionId: string,
    sub: Stripe.Subscription
  ) {
    try {
      const upcoming = await stripe.invoices.createPreview({
        customer: stripeCustomerId,
        subscription: subscriptionId,
      });

      base.nextBilling.amount =
        upcoming.total != null ? Number(upcoming.total) / 100 : null;
      base.nextBilling.currency = (
        upcoming.currency ||
        base.nextBilling.currency ||
        ""
      ).toUpperCase();

      let expectedTs: number | null =
        upcoming.collection_method === "charge_automatically"
          ? upcoming.next_payment_attempt ?? null
          : upcoming.collection_method === "send_invoice"
          ? upcoming.due_date ?? null
          : null;

      if (!expectedTs) expectedTs = upcoming.period_end ?? null;
      if (!expectedTs)
        expectedTs = sub.items.data[0].current_period_end ?? null;

      if (expectedTs) base.nextBilling.dateISO = this.toISO(expectedTs);
    } catch {
      // ignore invoice preview failure, return base as-is
    }
  }

  private static updateBillingInfo(
    base: CurrentSubscriptionDTO,
    sub: Stripe.Subscription
  ) {
    base.nextBilling.dateISO = this.toISO(
      sub.items.data[0].current_period_end ?? null
    );
    base.nextBilling.autoRenew = sub.cancel_at_period_end === false;
  }

  // --- Payment Method Resolution ---
  private static async resolvePaymentMethod(
    stripeCustomerId: string,
    sub: Stripe.Subscription
  ) {
    let pm: Stripe.PaymentMethod | null = null;
    const subPM =
      (sub.default_payment_method as Stripe.PaymentMethod | null) ?? null;

    if (subPM?.type === "card") pm = subPM;
    else {
      const customer = await stripe.customers.retrieve(stripeCustomerId, {
        expand: ["invoice_settings.default_payment_method"],
      });

      const custPM = (customer as any)?.invoice_settings
        ?.default_payment_method;

      if (custPM && typeof custPM !== "string" && custPM.type === "card") {
        pm = custPM;
      } else {
        const list = await stripe.paymentMethods.list({
          customer: stripeCustomerId,
          type: "card",
          limit: 1,
        });
        pm = list.data[0] ?? null;
      }
    }

    if (pm?.type === "card" && pm.card) {
      return {
        brand: pm.card.brand ?? null,
        last4: pm.card.last4 ?? null,
        expMonth: pm.card.exp_month ?? null,
        expYear: pm.card.exp_year ?? null,
        funding: pm.card.funding ?? null,
      };
    }

    return null;
  }
}
