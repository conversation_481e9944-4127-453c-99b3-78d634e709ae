import { sql } from "drizzle-orm";
import { PoolClient } from "pg";
import { drizzleDb } from "../../config/database";

export class AgentRolesRepository {

    async addAgentRole(profileId: number, serviceId: number, createdBy: number, dbClient: PoolClient) {
        const result = await dbClient.query(
            `INSERT INTO "agn"."agentRoles" ("profileId", "serviceId", "createdBy") 
             VALUES (${profileId}, ${serviceId}, ${createdBy})`
        );
        return result;
    }

    async getAgentRolesByProfileId(profileId: number) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM agn."agentRoles" WHERE "profileId" = ${profileId}`
        );
        return result;
    }

}