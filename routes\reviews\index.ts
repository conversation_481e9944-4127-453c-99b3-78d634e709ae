import express from "express";
import { authMiddleware } from "../../middleware/authMiddleware";
import { ReviewController } from "../../controller/reviews";
import { storageData } from "../../utils/services/multer";

const reviewController = new ReviewController();
const router = express.Router();
const upload = storageData("reviews");

router.get("/profile/:profileId", reviewController.getProfileReviews);
router.get("/profile/:profileId/stats", reviewController.getProfileRatingStats);

router.use(authMiddleware);

router.post("/", upload.none(), reviewController.createReview);
router.get("/my-reviews", reviewController.getUserReviews);
router.get("/:id", reviewController.getReviewById);
// router.put("/:id/flag", upload.none(), reviewController.flagReview);
router.get("/:id/history", reviewController.getReviewHistory);

export default router;
