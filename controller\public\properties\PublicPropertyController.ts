import { Request, Response } from "express";
import { PropertyService } from "../../../service/properties/PropertyService";
import asyncHandler from "../../../middleware/trycatch";
import { responseData, response } from "../../../utils/response";

export class PublicPropertyController {
  private propertyService = new PropertyService();

  // Get featured properties
  getFeaturedProperties = asyncHandler(async (req: Request, res: Response) => {
    const limit = req.query.limit ? Number(req.query.limit) : 10;
    const result = await this.propertyService.getFeaturedProperties(limit);
    return responseData(
      res,
      200,
      "Featured properties fetched successfully",
      result
    );
  });

  // Search public properties with filters
  searchProperties = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.searchPublicProperties(req.query);
    return responseData(res, 200, "Properties fetched successfully", result);
  });

  // Get property details by slug
  getPropertyBySlug = asyncHandler(async (req: Request, res: Response) => {
    const { slug } = req.params;
    if (!slug) return response(res, 400, "Slug is required");

    const result = await this.propertyService.getPublicPropertyBySlug(slug);
    return responseData(res, 200, "Property fetched successfully", result);
  });
}
