import { PropertiesRepository } from "../../repo/agn/properties-repository";

export class PropertiesService {

    private propertiesRepo = new PropertiesRepository();

    async getPropertiesCountByProfileId(id: number) {
        const count = await this.propertiesRepo.getPropertiesCountByProfileId(id);
        const propertiesCountData = {
            PropertiesCount: count,
            PropertiesAllowedLimit: 10
        }
        return propertiesCountData;
    }

}