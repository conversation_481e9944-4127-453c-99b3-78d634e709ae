import { z } from "zod";
import { DocumentSchema } from "./documents";       
import { IndustrySchema } from "./primary-industry"; 

export const IndividualCompleteProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  nationality: z.string().min(1, "Nationality is required"),
  gender: z.string().min(1, "Gender is required"),
  phoneNumber: z.string().min(7, "Phone number is required"),
  profilePhoto: z.string(),

  industries: z.array(IndustrySchema),

  documents: DocumentSchema,

  termsAgree: z.boolean("You must agree to the terms"),
  accuracyConfirm: z.boolean("You must confirm accuracy"),
  communicationConsent: z.boolean("You must set communication consent"),

  company: z.string().optional(),

  location: z.string().min(1, "Location is required"),
});

export type IndividualCompleteProfileDto = z.infer<typeof IndividualCompleteProfileSchema>;
