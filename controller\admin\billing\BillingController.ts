import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { responseData } from "../../../utils/response";
import { BillingService } from "../../../service/billings/BillingService";

export class BillingController {
  private svc = new BillingService();

  summary = asyncHandler(async (_req: Request, res: Response) => {
    const data = await this.svc.getSummary();
    return responseData(res, 200, "Billing summary fetched", data);
  });

  list = asyncHandler(async (req: Request, res: Response) => {
    const data = await this.svc.getTransactions(req.query);
    return responseData(res, 200, "Billing transactions fetched", data);
  });
}
