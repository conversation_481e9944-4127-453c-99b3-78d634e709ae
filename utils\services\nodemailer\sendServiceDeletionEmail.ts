import nodemailer from "nodemailer";
import transporter from ".";

export const sendServiceDeletionEmail = async (
  serviceName: string,
  serviceId: number,
  ownerName: string,
  ownerEmail: string
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to: ownerEmail,
    subject: "Service Deletion Notification - FindAnyAgent",
    html: await serviceDeletionEmailTemplate(serviceName, serviceId, ownerName),
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Service deletion email sent to: ${ownerEmail} for service: ${serviceName}`);
  } catch (error) {
    console.error("Error sending service deletion email:", error);
    // Don't throw error to avoid disrupting the deletion flow
  }
};

export const serviceDeletionEmailTemplate = async (
  serviceName: string,
  serviceId: number,
  ownerName: string
): Promise<string> => {
  return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Service Deletion Notification - FindAnyAgent</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <!-- === Full-width wrapper (centers the card) === -->
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <!-- === Centered card, max 600px === -->
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <!-- Header -->
                <h1 style="margin:0 0 20px 0;font-size:22px;color:#dc3545;">
                  Service Deletion Notification
                </h1>

                <!-- Content -->
                <div style="font-size:16px;">
                  <p>Dear ${ownerName},</p>
                  
                  <p>We are writing to inform you that your service has been deleted by our admin team.</p>
                  
                  <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #dc3545;">
                    <p style="margin: 0; font-weight: bold;">Deleted Service Details:</p>
                    <p style="margin: 5px 0 0 0;"><strong>Service Name:</strong> ${serviceName}</p>
                    <p style="margin: 5px 0 0 0;"><strong>Deletion Date:</strong> ${new Date().toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}</p>
                  </div>

                  <p>If you believe this action was taken in error or if you have any questions regarding this deletion, please don't hesitate to contact our support team.</p>
                  
                  <p>You can continue to manage your other services through your dashboard, and you're welcome to create new services at any time.</p>
                </div>

                <!-- Footer -->
                <p style="margin:32px 0 0 0;font-size:14px;color:#777777;">
                  Best regards,<br />
                  <strong>FindAnyAgent Admin Team</strong><br />
                  <a href="${process.env.WEBSITE_URL || 'https://findanyagent.ae'}" style="color: #004aad;">www.FindAnyAgent.ae</a>
                </p>
              </td>
            </tr>
          </table>
          <!-- ===== End card ===== -->
        </td>
      </tr>
    </table>
    <!-- ===== End wrapper ===== -->
  </body>
</html>`;
};
