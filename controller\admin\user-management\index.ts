import { Request, Response } from "express";
import fs from 'fs';
import path from 'path';
import multer from 'multer';
import bcrypt from "bcryptjs";
import { Parse<PERSON> } from "json2csv";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { responseData, response } from "../../../utils/response";
import { validateEmail } from '../../../utils/validators';
import { sendUserInvitationEmail } from "../../../utils/services/nodemailer/sendUserInvitationEmail";
import { sendBulkEmail } from "../../../utils/services/nodemailer/sendBulkEmail";
import { sendIndividualEmail } from "../../../utils/services/nodemailer/sendIndividualEmail";
import { sendPasswordResetEmail } from "../../../utils/services/nodemailer/sendPasswordResetEmail";
import { sendAccountStatusUpdatedEmail } from "../../../utils/services/nodemailer/sendAccountStatusUpdatedEmail";
import { TABLE } from "../../../utils/database/table";


// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(process.cwd(), 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir); // Use absolute path
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname);
    }
});

export const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit per file
        files: 5 // Maximum 5 files
    }
});

// Function to generate secure random password
function generateSecurePassword(): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const special = '!@#$%&*?';

    // Ensure at least one character from each required set
    let password = '';
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += special[Math.floor(Math.random() * special.length)];

    // Fill the rest with random characters from all sets
    const allChars = uppercase + lowercase + numbers + special;
    const remainingLength = 8 - password.length;

    for (let i = 0; i < remainingLength; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

export const addNewUser = asyncHandler(async (req: Request, res: Response) => {
    const { fullName, email, userType } = req.body;
    if (!fullName || !email || !userType) {
        return response(res, 400, "Missing required fields");
    }

    const existingUser = await db.query(`SELECT id FROM prf.profile WHERE email = $1`, [email]);
    if (existingUser.rowCount && existingUser.rowCount > 0) {
        return response(res, 409, "User with this email already exists.");
    }

    // Split full name
    const [firstName, middleName, lastName] = fullName.split(" ");

    // Email content and link
    let registrationLink = "";

    if (userType === "webuser") {
        registrationLink = `${process.env.NEXT_PUBLIC_DEV_REGISTRATION_URL}`;
        await sendUserInvitationEmail(fullName, email, userType, registrationLink);
    }
    else if (["agent", "agency", "agencyadmin"].includes(userType)) {
        registrationLink = `${process.env.NEXT_PUBLIC_DASHBOARD_REGISTRATION_URL}`;
        await sendUserInvitationEmail(fullName, email, userType, registrationLink);
    }
    else if (userType === "sub-admin") {
        // 1. Insert into prf.profile
        const statusIdRes = await db.query("SELECT id FROM look.status WHERE name = 'Activated' LIMIT 1");
        const statusId = statusIdRes.rows[0]?.id;
        if (!statusId) return response(res, 500, "Status not found");

        const profileRes = await db.query(
            `INSERT INTO prf.profile ("firstName", "middleName", "lastName", "email", "statusId", "createdOn")
             VALUES ($1, $2, $3, $4, $5, NOW()) RETURNING id`,
            [firstName, middleName || "", lastName || "", email, statusId]
        );
        const profileId = profileRes.rows[0].id;

        // 2. Insert into sec.login
        const username = email.split("@")[0];
        const randomPassword = generateSecurePassword();
        const salt = await bcrypt.genSalt(10);
        const passwordHash = await bcrypt.hash(randomPassword, salt);

        const loginRes = await db.query(
            `INSERT INTO sec.login ("profileId", "username", "passwordHash", "isActivated")
             VALUES ($1, $2, $3, true) RETURNING id`,
            [profileId, username, passwordHash]
        );
        const loginId = loginRes.rows[0].id;

        // 3. Get roleId for 'admin'
        const roleRes = await db.query(`SELECT id FROM sec.roles WHERE name = 'admin' LIMIT 1`);
        const roleId = roleRes.rows[0]?.id;
        if (!roleId) return response(res, 500, "Role not found");

        // 4. Get superadmin loginId
        const superadminRes = await db.query(`SELECT id FROM sec.login WHERE username = 'superadmin' LIMIT 1`);
        const createdBy = superadminRes.rows[0]?.id;

        // 5. Insert into sec.loginrole
        await db.query(
            `INSERT INTO sec.loginrole ("loginId", "roleId", "createdBy")
             VALUES ($1, $2, $3)`,
            [loginId, roleId, createdBy]
        );

        // 6. Email credentials to sub admin
        registrationLink = `${process.env.NEXT_PUBLIC_ADMIN_LOGIN_URL}`;
        await sendUserInvitationEmail(fullName, email, userType, registrationLink, randomPassword);
    }
    else {
        return response(res, 400, "Invalid user type");
    }

    return response(res, 200, "User invitation sent successfully");
});

export const getUserDetails = asyncHandler(async (req: Request, res: Response) => {
    try {
        const { status, type, search } = req.query;
        const page = parseInt(req.query.page as string, 10) || 1;
        const limit = parseInt(req.query.limit as string, 10) || 10;
        const offset = (page - 1) * limit;

        let baseQuery = `
            FROM "prf"."profile" p
            LEFT JOIN "sec"."login" l ON p.id = l."profileId"
            LEFT JOIN "sec"."loginrole" lr ON l.id = lr."loginId"
            LEFT JOIN "sec"."roles" r ON lr."roleId" = r.id
            LEFT JOIN "agn"."agencies" a ON p.id = a."profileId"
            LEFT JOIN "look"."status" s ON p."statusId" = s.id
        `;

        let whereClause = ` WHERE r.name IS NOT NULL AND LOWER(r.name) != 'superadmin'`;
        const queryParams: any[] = [];
        let paramCount = 1;

        // Add status filter
        if (status && status !== '') {
            whereClause += ` AND s.name = $${paramCount}`;
            queryParams.push(status);
            paramCount++;
        }

        // Add type filter
        if (type && type !== '') {
            let roleName = '';
            switch (type) {
                case 'Sub-Admin': roleName = 'admin'; break;
                case 'Agent': roleName = 'agent'; break;
                case 'Agency': roleName = 'agency'; break;
                case 'Web User': roleName = 'user'; break;
                case 'Agency Admin': roleName = 'agencyAdmin'; break;
            }
            if (roleName) {
                whereClause += ` AND r.name = $${paramCount}`;
                queryParams.push(roleName);
                paramCount++;
            }
        }

        // Add search filter
        if (search && search !== '') {
            whereClause += ` AND (
                LOWER(CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName")) LIKE LOWER($${paramCount})
                OR LOWER(p.email) LIKE LOWER($${paramCount})
                OR LOWER(a.name) LIKE LOWER($${paramCount})
                OR LOWER(a."companyEmail") LIKE LOWER($${paramCount})
                OR LOWER(l.username) LIKE LOWER($${paramCount})
            )`;
            queryParams.push(`%${search}%`);
            paramCount++;
        }

        const countQuery = `SELECT COUNT(p.id) ${baseQuery} ${whereClause}`;
        const countResult = await db.query(countQuery, queryParams);
        const totalUsers = parseInt(countResult.rows[0].count, 10);
        const totalPages = Math.ceil(totalUsers / limit);

        const dataQuery = `
        SELECT 
            COALESCE(
                CASE 
                    WHEN p."accountType" = 'Individual' OR r.name = 'admin' THEN CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName")
                    WHEN p."accountType" = 'Company/Agency/PropertyDeveloper' THEN a.name
                    WHEN r.name = 'user' THEN l.username
                END
            ) AS "Full Name",
            CASE 
                WHEN p."accountType" = 'Individual' OR p."accountType" = 'Company/Agency/PropertyDeveloper' THEN r.name
                WHEN r.name = 'user' THEN 'web user'
                WHEN r.name = 'admin' THEN 'sub-admin'
            END AS "Type",
            p.email as "Email",
            s.name AS "Status",
            p.id AS "User ID",
            p."profileImage" AS "Profile Image",
            TO_CHAR(p."createdOn", 'DD/MM/YYYY HH24:MI') AS "Join Date",
            TO_CHAR(l."lastLogin", 'DD/MM/YYYY HH24:MI') AS "Last Login"
        ${baseQuery} ${whereClause}
        ORDER BY p."createdOn" DESC
        LIMIT $${paramCount} OFFSET $${paramCount + 1}
        `;
        
        const finalParams = [...queryParams, limit, offset];
        const result = await db.query(dataQuery, finalParams);

        const totalUsersQuery = `SELECT COUNT(p.id) FROM prf.profile p WHERE TRIM(p.email) != '<EMAIL>'`;
        const activeUsersQuery = `SELECT COUNT(p.id) FROM prf.profile p JOIN look.status s ON p."statusId" = s.id WHERE s.name = 'Activated'`;
        const pendingUsersQuery = `SELECT COUNT(p.id) FROM prf.profile p JOIN look.status s ON p."statusId" = s.id WHERE s.name = 'Pending'`;

        const [totalUsersRes, activeUsersRes, pendingUsersRes] = await Promise.all([
            db.query(totalUsersQuery),
            db.query(activeUsersQuery),
            db.query(pendingUsersQuery),
        ]);

        return responseData(res, 200, "Users fetched successfully", {
            users: result.rows,
            pagination: {
                totalUsers,
                totalPages,
                currentPage: page,
                limit,
            },
            counts: {
                total: parseInt(totalUsersRes.rows[0].count, 10),
                active: parseInt(activeUsersRes.rows[0].count, 10),
                pending: parseInt(pendingUsersRes.rows[0].count, 10),
            }
        });

    } catch (error) {
        console.error("Error in getUserDetailsForManagement:", error);
        return response(res, 500, "Internal Server Error");
    }
});

export const sendBulkEmailHandler = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { subject, message, isLead } = req.body;
    const to = JSON.parse(req.body?.to || []);
    const cc = JSON.parse(req.body?.cc || []);
    const bcc = JSON.parse(req.body?.bcc || []);
    const files = req.files as Express.Multer.File[];

    // Validation
    if (!subject || !message) {
      return responseData(res, 400, "Subject and message are required");
    }

    if (!Array.isArray(bcc) || bcc.length === 0) {
      return responseData(res, 400, "BCC must be a non-empty array of email addresses");
    }

    // Validate all email addresses
    const invalidEmails = bcc.filter(email => !validateEmail(email));
    if (invalidEmails.length > 0) {
      return responseData(res, 400, `Invalid email addresses: ${invalidEmails.join(', ')}`);
    }

    // Prepare attachments if any
    const attachments = await Promise.all(files.map(async file => ({
      filename: file.originalname,
      content: await fs.promises.readFile(file.path)
    })));

    // Send email to all recipients in BCC
    await sendBulkEmail(subject, message, bcc, cc, to, res, attachments);

    /* ---------- 4  <USER> <GROUP> recipients are leads, mark them contacted ---------- */
    const isLeadFlag =
      isLead === true ||
      isLead === 1 ||
      (typeof isLead === "string" &&
        ["true", "1"].includes(isLead.toLowerCase()));

    if (isLeadFlag) {
      const client = await db.connect();
      try {
        await client.query("BEGIN");
        await client.query(
          `UPDATE ${TABLE.LEADS}
               SET last_contact = NOW()
             WHERE email = ANY($1::text[])`,
          [bcc]
        );
        await client.query("COMMIT");
      } catch (dbErr) {
        await client.query("ROLLBACK");
        console.error("Failed to update last_contact:", dbErr);
        // e-mail already went out; app logic can decide if this is fatal
      } finally {
        client.release();
      }
    }

    // Clean up uploaded files
    if (files) {
      files.forEach(file => {
        fs.unlink(file.path, (err) => {
          if (err) console.error(`Error deleting file ${file.path}:`, err);
        });
      });
    }
  } catch (error) {
    console.error("Error in sendBulkEmailHandler:", error);
    return responseData(res, 500, "Failed to send bulk email");
  }
});

export const sendIndividualEmailHandler = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { subject, message, isLead } = req.body;
    const to = JSON.parse(req.body?.to || []);
    const cc = JSON.parse(req.body?.cc || []);
    const bcc = JSON.parse(req.body?.bcc || []);
    const files = req.files as Express.Multer.File[];

    // Validation
    if (!subject || !message) {
      return responseData(res, 400, "Subject and message are required");
    }

    if (!Array.isArray(to) || to.length !== 1) {
      return responseData(res, 400, "To must be an array containing exactly one email address");
    }

    const recipientEmail = to[0];
    if (!validateEmail(recipientEmail)) {
      return responseData(res, 400, "Invalid recipient email address");
    }

    // Prepare attachments if any
    const attachments = await Promise.all(files.map(async file => ({
      filename: file.originalname,
      content: await fs.promises.readFile(file.path)
    })));

    // Send email to the recipient
    await sendIndividualEmail(subject, message, recipientEmail, cc, bcc, res, attachments);

    /* ---------- 4  <USER> <GROUP> is a lead, mark them contacted ---------- */
    const isLeadFlag =
      isLead === true ||
      isLead === 1 ||
      (typeof isLead === "string" &&
        ["true", "1"].includes(isLead.toLowerCase()));

    if (isLeadFlag) {
      const client = await db.connect();
      try {
        await client.query("BEGIN");
        await client.query(
          `UPDATE ${TABLE.LEADS}
               SET last_contact = NOW()
             WHERE email = $1`,
          [recipientEmail]
        );
        await client.query("COMMIT");
      } catch (dbErr) {
        await client.query("ROLLBACK");
        console.error("Failed to update last_contact:", dbErr);
        // the email has been sent already, so we don’t abort the request
      } finally {
        client.release();
      }
    }

    // Clean up uploaded files
    if (files) {
      files.forEach(file => {
        fs.unlink(file.path, (err) => {
          if (err) console.error(`Error deleting file ${file.path}:`, err);
        });
      });
    }
  } catch (error) {
    console.error("Error in sendIndividualEmailHandler:", error);
    return responseData(res, 500, "Failed to send email");
  }
});

export const downloadUsersCSV = asyncHandler(async (req: Request, res: Response) => {
    try {
        const result = await db.query(`
            SELECT
                COALESCE(
                    CASE
                        WHEN p."accountType" = 'Individual' OR r.name = 'admin' THEN CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName")
                        WHEN p."accountType" = 'Company/Agency/PropertyDeveloper' THEN a.name
                        WHEN r.name = 'user' THEN l.username
                    END
                ) AS "Full Name",
                
                CASE
                    WHEN p."accountType" = 'Individual' OR r.name = 'admin' OR r.name = 'user' THEN p.email
                    WHEN p."accountType" = 'Company/Agency/PropertyDeveloper' THEN a."companyEmail"
                END AS "Email",
                
                CASE
                    WHEN p."accountType" = 'Individual' OR r.name = 'user' THEN r.name
                    WHEN p."accountType" = 'Company/Agency/PropertyDeveloper' THEN 'agency'
                    WHEN r.name = 'admin' THEN 'sub-admin'
                END AS "Type",
                
                s.name AS "Status",
                
                TO_CHAR(
                    p."createdOn",
                    'DD/MM/YYYY HH24:MI'
                ) AS "Join Date",
                
                TO_CHAR(
                    l."lastLogin",
                    'DD/MM/YYYY HH24:MI'
                ) AS "Last Login"
                
            FROM "prf"."profile" p
            LEFT JOIN "sec"."login" l ON p.id = l."profileId"
            LEFT JOIN "sec"."loginrole" lr ON l.id = lr."loginId"
            LEFT JOIN "sec"."roles" r ON lr."roleId" = r.id
            LEFT JOIN "agn"."agencies" a ON p.id = a."profileId"
            LEFT JOIN "look"."status" s ON p."statusId" = s.id
            WHERE r.name IS NOT NULL
                AND LOWER(r.name) != 'superadmin'
            ORDER BY p."createdOn" DESC;
        `);

        // Convert data to CSV format
        // const json2csvParser = new Parser({ fields: ["Full Name", "Email", "Type", "Status", "Join Date", "Last Login"] });
        // const csv = json2csvParser.parse(result.rows);

        const users = result.rows;

        // Define the fields for CSV
        const fields = [
            { label: "Full Name", value: "Full Name" },
            { label: "Email", value: "Email" },
            { label: "Type", value: "Type" },
            { label: "Status", value: "Status" },
            { label: "Join Date", value: "Join Date" },
            { label: "Last Login", value: "Last Login" }
        ];

        // Use json2csv to convert JSON data to CSV format
        const json2csvParser = new Parser({ fields });
        const csv = json2csvParser.parse(users);

        res.header('Content-Type', 'text/csv');
        res.attachment('users.csv');
        return res.send(csv);
    } catch (error) {
        console.error("Error in downloadUsersCSV:", error);
        return response(res, 500, "Internal Server Error");
    }
});

export const deactivateUser = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const client = await db.connect();
    try {
        await client.query('BEGIN');

        // 1. Get statusId for 'Deactivated'
        const statusRes = await client.query(`SELECT id FROM look.status WHERE name = 'Deactivated' LIMIT 1`);
        if (statusRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 500, "Status 'Deactivated' not found.");
        }
        const deactivatedStatusId = statusRes.rows[0].id;

        // 2. Update prf.profile and get user info for email
        const profileUpdateRes = await client.query(
            `UPDATE prf.profile SET "statusId" = $1 WHERE id = $2 RETURNING id, email, "firstName", "middleName", "lastName"`,
            [deactivatedStatusId, id]
        );

        if (profileUpdateRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "User not found.");
        }
        const user = profileUpdateRes.rows[0];
        const profileId = user.id;

        // 3. Update sec.login table
        const loginUpdateRes = await client.query(
            `UPDATE sec.login SET "isActivated" = FALSE WHERE "profileId" = $1 RETURNING id`,
            [profileId]
        );

        if (loginUpdateRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "Login record not found for user.");
        }
        const loginId = loginUpdateRes.rows[0].id;
        
        // 4. Invalidate/Expire all user sessions
        await client.query(`UPDATE sec.logintoken SET "expiresOn" = NOW() WHERE "userId" = $1`, [loginId]); 

        await client.query('COMMIT');
        
        // 5. Send notification email
        const fullName = `${user.firstName || ''} ${user.middleName || ''} ${user.lastName || ''}`.trim();
        if (user.email) {
            await sendAccountStatusUpdatedEmail(fullName, user.email, 'Deactivated');
        }

        return responseData(res, 200, "User deactivated successfully!");

    } catch (error) {
        await client.query('ROLLBACK');
        console.error("Error deactivating user:", error);
        return responseData(res, 500, "Failed to deactivate user. Internal Server Error.");
    } finally {
        client.release();
    }
});

export const activateUser = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params;
    const client = await db.connect();
    try {
        await client.query('BEGIN');

        // 1. Get statusId for 'Activated'
        const statusRes = await client.query(`SELECT id FROM look.status WHERE name = 'Activated' LIMIT 1`);
        if (statusRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 500, "Status 'Activated' not found.");
        }
        const activatedStatusId = statusRes.rows[0].id;

        // 2. Update prf.profile and get user info for email
        const profileUpdateRes = await client.query(
            `UPDATE prf.profile SET "statusId" = $1 WHERE id = $2 RETURNING id, email, "firstName", "middleName", "lastName"`,
            [activatedStatusId, id]
        );

        if (profileUpdateRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "User not found.");
        }
        const user = profileUpdateRes.rows[0];
        const profileId = user.id;

        // 3. Update sec.login table
        const loginUpdateRes = await client.query(
            `UPDATE sec.login SET "isActivated" = TRUE WHERE "profileId" = $1`,
            [profileId]
        );

        if (loginUpdateRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "Login record not found for user.");
        }

        await client.query('COMMIT');
        
        // 4. Send notification email
        const fullName = `${user.firstName || ''} ${user.middleName || ''} ${user.lastName || ''}`.trim();
        if(user.email) {
            await sendAccountStatusUpdatedEmail(fullName, user.email, 'Activated');
        }
        
        return responseData(res, 200, "User activated successfully!");

    } catch (error) {
        await client.query('ROLLBACK');
        console.error("Error activating user:", error);
        return responseData(res, 500, "Failed to activate user. Internal Server Error.");
    } finally {
        client.release();
    }
});

export const resetUserPassword = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params; // User ID from path parameter
    const { createdBy } = req.body;

    // Input validation
    if (!createdBy || isNaN(Number(createdBy))) {
        return responseData(res, 400, "Invalid or missing createdBy (admin userId).");
    }

    if (!id || typeof id !== 'string') {
        return responseData(res, 400, "Invalid user ID provided.");
    }

    const client = await db.connect(); // Acquire client for transaction

    try {
        await client.query('BEGIN');

        // 1. Validate that the user exists and get email/name
        const userRes = await client.query(
            `SELECT id, "firstName", "middleName", "lastName", email FROM prf.profile WHERE id = $1`,
            [id]
        );

        if (userRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "User not found.");
        }

        const user = userRes.rows[0];
        const fullName = `${user.firstName || ''} ${user.middleName || ''} ${user.lastName || ''}`.trim();
        const email = user.email;

        if (!email) {
            await client.query('ROLLBACK');
            return responseData(res, 400, "User does not have a valid email address.");
        }

        // 2. Generate and hash new password
        const newRandomPassword = generateSecurePassword();
        const salt = await bcrypt.genSalt(10);
        const newPasswordHash = await bcrypt.hash(newRandomPassword, salt);

        // 3. Update sec.login table
        const loginUpdateRes = await client.query(
            `UPDATE sec.login SET "passwordHash" = $1, "passwordResetAt" = NOW() WHERE "profileId" = $2`,
            [newPasswordHash, user.id]
        );

        if (loginUpdateRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 500, "Failed to update login password.");
        }

        // 3.1 Get loginId from sec.login table using profileId 
        const loginIdRes = await client.query(
            `SELECT id FROM sec.login WHERE "profileId" = $1`,
            [user.id]
        );

        if (loginIdRes.rowCount === 0) {
            await client.query('ROLLBACK');
            return responseData(res, 404, "Login record not found for user.");
        }

        const loginId = loginIdRes.rows[0].id;

        // 4. Log password reset event in sec.auditlogs
        await client.query(
            `INSERT INTO sec.auditlogs ("userId", action, "timestamp", "createdBy") VALUES ($1, $2, NOW(), $3)`,
            [loginId, "password_reset", createdBy]
        );

        await client.query('COMMIT');

                
        // 5. Invalidate/Expire all user sessions
        await client.query(`UPDATE sec.logintoken SET "expiresOn" = NOW() WHERE "userId" = $1`, [loginId]); 
        
        await client.query('COMMIT');

        // 6. Send email with new credentials
        await sendPasswordResetEmail(email, fullName, newRandomPassword);

        return responseData(res, 200, "Password reset successfully, credentials emailed to user.");

    } catch (error) {
        await client.query('ROLLBACK');
        console.error("Error resetting user password:", error);
        return responseData(res, 500, "Failed to reset user password. Internal Server Error.");
    } finally {
        client.release();
    }
});

export const addUserNote = asyncHandler(async (req: Request, res: Response) => {
    const { id } = req.params; // userId from URL (should match profileId in prf.profile)
    const { reason, createdBy, profileId } = req.body;

    // Input validation
    if (!reason || typeof reason !== "string" || !reason.trim()) {
        return responseData(res, 400, "Invalid or missing reason.");
    }
    if (!createdBy || isNaN(Number(createdBy))) {
        return responseData(res, 400, "Invalid or missing createdBy (admin userId).");
    }
    if (!profileId || isNaN(Number(profileId))) {
        return responseData(res, 400, "Invalid or missing profileId (user profileId).");
    }

    const client = await db.connect();
    try {
        await client.query("BEGIN");

        // Validate user (profileId)
        const userRes = await client.query(
            `SELECT id FROM prf.profile WHERE id = $1`,
            [profileId]
        );
        if (userRes.rowCount === 0) {
            await client.query("ROLLBACK");
            return responseData(res, 404, "User not found.");
        }

        // Validate admin (createdBy)
        const adminRes = await client.query(
            `SELECT id FROM prf.profile WHERE id = $1`,
            [createdBy]
        );
        if (adminRes.rowCount === 0) {
            await client.query("ROLLBACK");
            return responseData(res, 404, "Admin user not found.");
        }

        // Insert note into prf.reasons
        await client.query(
            `INSERT INTO prf.reasons 
                ("profileId", reason, "createdBy", "created_at", "updated_at", "isPrivate", "oldStatusId", "newStatusId")
             VALUES
                ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false, NULL, NULL)`,
            [profileId, reason.trim(), createdBy]
        );

        await client.query("COMMIT");
        return responseData(res, 200, "Note added successfully!");
    } catch (error) {
        await client.query("ROLLBACK");
        console.error("Error adding note:", error);
        return responseData(res, 500, "Failed to add note to database.");
    } finally {
        client.release();
    }
});

export const getUserNotes = asyncHandler(async (req: Request, res: Response) => {
    const { profileId } = req.params;
    if (!profileId || isNaN(Number(profileId))) {
        return responseData(res, 400, "Invalid or missing profileId.");
    }
    try {
        const { rows } = await db.query(
            `SELECT r.reason, r."createdBy", 
                CONCAT(p."firstName", ' ', COALESCE(NULLIF(p."middleName", ''), ''),
                    CASE WHEN p."middleName" IS NOT NULL AND p."middleName" != '' THEN ' ' ELSE '' END,
                    COALESCE(p."lastName", '')) AS "createdByName",
                r.created_at
             FROM prf.reasons r
             JOIN prf.profile p ON r."createdBy" = p.id
             WHERE r."profileId" = $1
             ORDER BY r.created_at DESC`,
            [profileId]
        );
        return responseData(res, 200, "Notes fetched successfully!", rows);
    } catch (error) {
        console.error("Error fetching notes:", error);
        return responseData(res, 500, "Failed to fetch notes from database.");
    }
});
