export type DbFilterParams = {
  search: string | null;
  planId: number | null;
  listing: string | null;
  type: string | null;
  statusId: number | null;
  dateFrom: string | null;
  dateTo: string | null;
  profileId: number | null;
  sortBy: "date" | "amount" | "status";
  sortDir: "asc" | "desc";
  limit: number;
  offset: number;
  page: number; // used by service, not queries; ok to carry
  pageSize: number; // used by service, not queries; ok to carry
};
