/agent/payments:
  get:
    tags:
      - Agent Payments
    summary: Get filtered agent payments
    operationId: getFilteredPayments
    description: Fetch agent payments using search, plan, status, date range, sorting, and pagination. Excludes free plans (price = 0).
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema: { type: integer, default: 1 }
      - name: pageSize
        in: query
        schema: { type: integer, default: 10 }
      - name: search
        in: query
        schema: { type: string }
      - name: plan
        in: query
        schema: { type: integer, description: Package type ID }
      - name: sortBy
        in: query
        schema:
          type: string
          enum: [date, amount, status]
          default: date
      - name: sortDir
        in: query
        schema:
          type: string
          enum: [asc, desc]
          default: desc
    responses:
      "200":
        description: Payments fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: integer }
                success: { type: boolean }
                message: { type: string }
                data:
                  type: object
                  properties:
                    payments:
                      type: array
                      items:
                        $ref: "#/components/schemas/Payment"
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    statusCounts:
                      type: array
                      items:
                        $ref: "#/components/schemas/PaymentStatusCount"

/agent/payments/{id}:
  get:
    tags:
      - Agent Payments
    summary: Get agent payment by ID
    operationId: getPaymentById
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema: { type: integer }
    responses:
      "200":
        description: Payment found
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: integer }
                success: { type: boolean }
                message: { type: string }
                data:
                  $ref: "#/components/schemas/Payment"
      "404":
        description: Payment not found

/agent/payments/filters/options:
  get:
    tags:
      - Agent Payments
    summary: Get payment filter agent options
    operationId: getPaymentFilterOptions
    security:
      - cookieAuth: []
    responses:
      "200":
        description: Filter options fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: integer }
                success: { type: boolean }
                message: { type: string }
                data:
                  type: object
                  properties:
                    plans:
                      type: array
                      items:
                        type: object
                        properties:
                          id: { type: integer }
                          name: { type: string }
                          price: { type: number }
                          currency: { type: string }
                          interval: { type: string }
                    types:
                      type: array
                      items:
                        type: object
                        properties:
                          type: { type: string }
                    listings:
                      type: array
                      items:
                        type: object
                        properties:
                          listing: { type: string }

/agent/payments/subscription-details:
  get:
    tags:
      - Agent Payments
    summary: Get current active subscription details
    operationId: getCurrentSubscriptionDetails
    description: >
      Retrieves the currently active subscription for the authenticated agent,
      enriched with Stripe data such as next billing date, amount, currency,
      auto-renewal flag, and payment method details.
    security:
      - cookieAuth: []
    responses:
      "200":
        description: Subscription details fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: integer }
                success: { type: boolean }
                message: { type: string }
                data:
                  type: object
                  properties:
                    plan:
                      type: object
                      properties:
                        name: { type: string, nullable: true }
                        price: { type: number, nullable: true }
                        interval: { type: string, nullable: true }
                        currency: { type: string, nullable: true }
                        status: { type: string, nullable: true }
                    nextBilling:
                      type: object
                      properties:
                        dateISO:
                          { type: string, format: date-time, nullable: true }
                        amount: { type: number, nullable: true }
                        currency: { type: string, nullable: true }
                        autoRenew: { type: boolean, nullable: true }
                    paymentMethod:
                      type: object
                      nullable: true
                      properties:
                        brand: { type: string, nullable: true }
                        last4: { type: string, nullable: true }
                        expMonth: { type: integer, nullable: true }
                        expYear: { type: integer, nullable: true }
                        funding: { type: string, nullable: true }
      "401":
        description: Unauthorized (missing or invalid cookie)
      "500":
        description: Server error

/agent/payments/invoices:
  get:
    tags:
      - Agent Payments
    summary: Get invoices with Stripe invoice URLs
    operationId: getAgentInvoices
    description: >
      Fetches invoices for the authenticated agent. Accepts search, status, plan, type,
      date range, sorting, and pagination parameters. Each invoice will include Stripe's
      hosted invoice URL and PDF link if available.
    security:
      - cookieAuth: []
    parameters:
      - name: search
        in: query
        schema: { type: string, nullable: true, default: "" }
      - name: status
        in: query
        schema: { type: string, nullable: true, default: "" }
      - name: plan
        in: query
        schema: { type: integer, nullable: true }
      - name: type
        in: query
        schema: { type: string, nullable: true, default: "" }
      - name: dateFrom
        in: query
        schema: { type: string, format: date, nullable: true }
      - name: dateTo
        in: query
        schema: { type: string, format: date, nullable: true }
      - name: sortBy
        in: query
        schema:
          type: string
          enum: [date, amount, status]
          default: date
      - name: sortDir
        in: query
        schema:
          type: string
          enum: [asc, desc]
          default: desc
      - name: page
        in: query
        schema: { type: integer, minimum: 1, default: 1 }
      - name: pageSize
        in: query
        schema: { type: integer, minimum: 1, maximum: 200, default: 20 }
    responses:
      "200":
        description: Invoices fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status: { type: integer }
                success: { type: boolean }
                message: { type: string }
                data:
                  type: object
                  properties:
                    payments:
                      type: array
                      items:
                        allOf:
                          - $ref: "#/components/schemas/Payment"
                          - type: object
                            properties:
                              subscriptionId: { type: string, nullable: true }
                              hostedInvoiceUrl: { type: string, nullable: true }
                              invoicePdfUrl: { type: string, nullable: true }
                    pagination:
                      $ref: "#/components/schemas/Pagination"
                    statusCounts:
                      type: array
                      items:
                        $ref: "#/components/schemas/PaymentStatusCount"
      "401":
        description: Unauthorized (missing or invalid cookie)
      "500":
        description: Server error

components:
  schemas:
    Profile:
      type: object
      properties:
        id: { type: integer }
        firstName: { type: string }
        middleName: { type: string, nullable: true }
        lastName: { type: string, nullable: true }
        phone: { type: string, nullable: true }
        email: { type: string }
        profileImage: { type: string, nullable: true }

    Payment:
      type: object
      properties:
        id: { type: integer }
        paymentId: { type: string }
        date: { type: string, format: date-time }
        type:
          {
            type: string,
            enum: ["Subscription", "Ad Boost", "Listing Boost", "Other"],
          }
        description: { type: string }
        amount:
          { type: string, description: "Formatted amount, e.g., 'AED 199.00'" }
        amountValue: { type: number, description: "Raw numeric amount" }
        currency: { type: string }
        statusId: { type: integer }
        statusName: { type: string }
        invoiceNo: { type: string, nullable: true }
        packageTypeId: { type: integer, nullable: true }
        packageTypeName: { type: string, nullable: true }
        interval: { type: string, nullable: true }
        profile:
          $ref: "#/components/schemas/Profile"

    PaymentStatusCount:
      type: object
      properties:
        status_id: { type: integer }
        status_name: { type: string }
        count: { type: integer }
