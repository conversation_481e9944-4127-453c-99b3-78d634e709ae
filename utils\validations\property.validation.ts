import { ValidationError } from "../errors";

interface SanitizeOptions {
  nonNullableKeys: string[];
  booleanKeys: string[];
}

export async function sanitizeAndValidate(
  data: Record<string, any>,
  options: SanitizeOptions
): Promise<Record<string, any>> {
  const { nonNullableKeys = [], booleanKeys = [] } = options;
  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(data)) {
    let sanitizedValue: any = value;

    // Convert empty strings to null
    if (typeof value === 'string' && value.trim() === '') {
      sanitizedValue = null;
    }

    // Convert to boolean if listed in booleanKeys
    if (booleanKeys.includes(key)) {
      if (typeof sanitizedValue === 'string') {
        sanitizedValue = sanitizedValue.toLowerCase() === 'true';
      } else {
        sanitizedValue = Boolean(sanitizedValue);
      }
    }

    // Validate non-nullable fields
    if (
      nonNullableKeys.includes(key) &&
      (sanitizedValue === null || sanitizedValue === undefined)
    ) {
      throw new ValidationError(`Missing required field: ${key}`);
    }

    sanitized[key] = sanitizedValue;
  }

  return sanitized;
}