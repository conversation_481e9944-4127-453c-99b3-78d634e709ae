import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

const PORT = parseInt(process.env.DATABASE_PORT || "5432", 10);
const DATABASE_URL = `postgres://${process.env.DATABASE_USER}:${process.env.DATABASE_PASSWORD}@${process.env.DATABASE_HOST}:${PORT}/${process.env.DATABASE_NAME}`;

export default defineConfig({
  out: './drizzle',
  schema: './db_schema',
  dialect: 'postgresql',
  dbCredentials: {
    url: DATABASE_URL,
  },
});