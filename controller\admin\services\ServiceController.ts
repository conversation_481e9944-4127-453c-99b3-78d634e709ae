import { Request, Response } from "express";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
import { ServicesService } from "../../../service/services/ServicesService";
import { NotesService } from "../../../service/notes/NotesService";

export class ServiceController {
  private servicesService = new ServicesService();
  private noteService = new NotesService();

  getAllServices = asyncHandler(async (req: Request, res: Response) => {
    try {
      const queryFilter = {
        ...req.query,
        isDeleted: false,
      };
      const result = await this.servicesService.getAllServicesForAdmin(
        queryFilter
      );
      return responseData(res, 200, "Services fetched successfully", result);
    } catch (err: any) {
      console.error("Get All Services Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch services."
      );
    }
  });

  // READ single
  getServiceById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.servicesService.getServiceById(
        Number(req.params.id)
      );
      return responseData(res, 200, "Service fetched successfully", result);
    } catch (err: any) {
      console.error("Get Service Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch service."
      );
    }
  });

  // PATCH statusId
  updateStatus = asyncHandler(async (req: Request, res: Response) => {
    try {
      await this.servicesService.updateStatus(
        Number(req.params.id),
        req.body.status,
        req.body.reason,
        req.user?.id!,
        req.user?.role || ""
      );
      return response(res, 200, "Status updated successfully");
    } catch (err: any) {
      console.error("Update Status Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to update status."
      );
    }
  });

  // DELETE
  deleteService = asyncHandler(async (req: Request, res: Response) => {
    try {
      await this.servicesService.deleteService(Number(req.params.id));
      return response(res, 200, "Service deleted successfully.");
    } catch (err: any) {
      console.error("Delete Service Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to delete service."
      );
    }
  });

  getFilterOptionsAndTypes = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        const services =
          await this.servicesService.getFilterOptionsAndTypesForAdmin();
        return responseData(
          res,
          200,
          "Filter options fetched successfully.",
          services
        );
      } catch (err: any) {
        console.error("Fetching Filter Options Error:", err);
        return response(
          res,
          err.statusCode || 500,
          err.message || "Failed to fetch filter options."
        );
      }
    }
  );

  createNote = asyncHandler(async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const { note } = req.body;

    const result = await this.noteService.createPropertyNote(
      id,
      note,
      "service"
    );

    return responseData(res, 201, "Note added successfully.", result);
  });

  getNotes = asyncHandler(async (req: Request, res: Response) => {
    const id = Number(req.params.id);
    const result = await this.noteService.getNotesByModuleNameAndId(
      id,
      "service"
    );
    return responseData(res, 200, "Notes fetched successfully.", result);
  });
}
