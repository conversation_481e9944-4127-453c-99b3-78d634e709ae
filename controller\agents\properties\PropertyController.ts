import { Request, Response } from "express";
import { PropertyService } from "../../../service/properties/PropertyService";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
import { sanitizeAndValidate } from "../../../utils/validations/property.validation";

export class PropertyController {
  private propertyService = new PropertyService();

  getAllProperties = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.propertyService.getAllProperties(req.query);
      return responseData(res, 200, "Properties fetched successfully", result);
    } catch (err: any) {
      console.error("Get All Properties Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch properties."
      );
    }
  });

  getPropertyById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.propertyService.getPropertyById(
        Number(req.params.id)
      );
      return responseData(res, 200, "Property fetched successfully", result);
    } catch (err: any) {
      console.error("Get Property Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to fetch property."
      );
    }
  });

  createOrUpdateProperty = asyncHandler(async (req: Request, res: Response) => {
    try {
      // 🔹 Sanitize input here (only this moved from service)
      const sanitized = await sanitizeAndValidate(req.body, {
        nonNullableKeys: ["name", "locationId", "price", "size", "listingType"],
        booleanKeys: [
          "parking",
          "swimmingPools",
          "gym",
          "isFeatured",
          "isVerified",
          "furnished",
        ],
      });

      // replace req.body with sanitized data
      req.body = sanitized;

      const result = await this.propertyService.createOrUpdateProperty(req);
      return responseData(res, 200, "Property saved successfully", result);
    } catch (err: any) {
      console.error("Property Save Error:", err);

      let message: any;
      try {
        message = JSON.parse(err.message);
      } catch {
        message = err.message;
      }

      return response(
        res,
        err.statusCode || 500,
        message || "An unexpected error occurred while saving property."
      );
    }
  });

  updateStatus = asyncHandler(async (req: Request, res: Response) => {
    try {
      await this.propertyService.updatePropertyStatus(
        Number(req.params.id),
        req.body.status
      );
      return response(res, 200, "Status updated successfully");
    } catch (err: any) {
      console.error("Update Status Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to update status."
      );
    }
  });

  toggleFlag = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.propertyService.togglePropertyFlag(
        Number(req.params.id),
        req.body.column
      );
      return response(res, 200, result);
    } catch (err: any) {
      console.error("Toggle Flag Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to toggle flag."
      );
    }
  });

  deleteProperty = asyncHandler(async (req: Request, res: Response) => {
    try {
      await this.propertyService.deleteProperty(Number(req.params.id));
      return response(res, 200, "Property deleted successfully.");
    } catch (err: any) {
      console.error("Delete Property Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to delete property."
      );
    }
  });

  updatePhotos = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.propertyService.updatePropertyPhotos(req);
      return responseData(
        res,
        200,
        "Property photos updated successfully.",
        result
      );
    } catch (err: any) {
      console.error("Update Photos Error:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Failed to update photos."
      );
    }
  });
}
