version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
  pre_build:
    commands:
      - echo "Installing dependencies..."
      - npm install --production
  build:
    commands:
      - echo "Building the project..."
      - echo "No build needed for Node.js API"
      - ls -la
  post_build:
    commands:
      - echo "Build completed successfully"
      - ls -la
      - ls -la scripts/

artifacts:
  files:
    - ./**/*
    - package.json
    - package-lock.json
    - Dockerfile
    - docker-compose.yml
    - drizzle.config.ts
    - appspec.yml
  discard-paths: no
