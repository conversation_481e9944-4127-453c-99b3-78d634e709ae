import Stripe from "stripe";
import { PaymentRepository } from "../../repo/payments/PaymentRepository";
import { AppError } from "../../utils/errors/ValidationError";
import stripeKeys from "../../config/stripeConfigs";
import { GetAllPaymentsResponseDTO } from "../../dto/payment/GetAllPaymentsResponseDTO";
import { PaymentDTO } from "../../dto/payment/PaymentDTO";
import { GetAllInvoicesResponseDTO } from "../../dto/payment/GetAllInvoicesResponseDTO";
import { CurrentSubscriptionDTO } from "../../dto/payment/CurrentSubscriptionDTO";
import { PaymentFunction } from "../../utils/helperFunctions/payments/PaymentFunction";
import { InvoiceFunction } from "../../utils/helperFunctions/invoices/InvoiceFunction";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

export class PaymentService {
  private repo = new PaymentRepository();

  async getAll(query: any): Promise<GetAllPaymentsResponseDTO> {
    const statusId = await this.repo.findStatusIdByName(
      query.status && String(query.status).toLowerCase() !== "all"
        ? String(query.status)
        : null
    );
    const filters = PaymentFunction.toDbFilters(query, statusId);

    const rows = await this.repo.fetchPayments(filters);
    const statusCountsRows = await this.repo.fetchPaymentStatusCounts(filters);

    const total = Number(rows[0]?.total_count || 0);
    const totalPages = Math.ceil(total / filters.pageSize);

    const payments = rows.map(PaymentFunction.mapPaymentRow);
    const statusCounts = statusCountsRows.map((r: any) => ({
      status_id: r.status_id,
      status_name: r.status_name,
      count: Number(r.count || 0),
    }));

    return {
      payments,
      pagination: {
        total,
        totalPages,
        currentPage: filters.page,
        perPage: filters.pageSize,
      },
      statusCounts,
    };
  }

  async getById(id: number): Promise<PaymentDTO> {
    if (!id || Number.isNaN(Number(id)))
      throw new AppError("Invalid payment id.", 400);

    const r = await this.repo.fetchPaymentDetailById(id);
    if (!r) throw new AppError("Payment not found", 404);

    const base = PaymentFunction.mapPaymentRow(r);
    const subscriptionId: string | undefined =
      (r as any).subscriptionId ||
      (r as any).stripeSubscriptionId ||
      (r as any).subscription_id;

    return PaymentFunction.enrichWithStripeInvoiceLinks({
      ...base,
      subscriptionId,
    });
  }

  async getFilterOptions() {
    const [plans, types, listings] = await Promise.all([
      this.repo.fetchPlans(),
      this.repo.fetchPaymentTypes(),
      this.repo.fetchListingTypes(),
    ]);
    return { plans, types, listings };
  }

  async getAllInvoices(query: any): Promise<GetAllInvoicesResponseDTO> {
    const statusId = await this.repo.findStatusIdByName(
      query.status && String(query.status).toLowerCase() !== "all"
        ? String(query.status)
        : null
    );
    const filters = PaymentFunction.toDbFilters(query, statusId);

    const rows = await this.repo.fetchInvoices(filters);
    const total = Number(rows[0]?.total_count || 0);
    const totalPages = Math.ceil(total / filters.pageSize);

    const base = rows.map((r: any) => ({
      ...PaymentFunction.mapPaymentRow(r),
      subscriptionId: r.subscriptionId || null,
    }));

    const invoices = await InvoiceFunction.mapWithConcurrency(base, 5, (p) =>
      PaymentFunction.enrichWithStripeInvoiceLinks(p)
    );

    const statusCountsRows = await this.repo.fetchInvoiceStatusCounts(filters);
    const statusCounts = statusCountsRows.map((r: any) => ({
      status_id: r.status_id,
      status_name: r.status_name,
      count: Number(r.count || 0),
    }));

    return {
      invoices,
      pagination: {
        total,
        totalPages,
        currentPage: filters.page,
        perPage: filters.pageSize,
      },
      statusCounts,
    };
  }

  async getCurrentSubscription(
    profileId: number
  ): Promise<CurrentSubscriptionDTO | null> {
    if (!profileId) throw new AppError("Invalid profile id.", 400);

    const activeStatusId = await this.repo.getActivatedAccountStatusId();
    const row = await this.repo.fetchActiveSubscriptionByProfile(
      profileId,
      activeStatusId
    );

    if (!row) return null;

    // delegate logic to helpers
    return await PaymentFunction.buildCurrentSubscription(row);
  }
}
