import { Router } from "express";
import { ProfileController } from "../../../controller/agents/profile.controller";
import { requestValidatorMiddleware } from "../../../middleware/requestValidatorMiddleware";
import { professionalInfoSchema, profileLocationSchema, profileUpdateSchema } from "../../../utils/validations/profileUpdate.validation";

const router = Router();

const profileController = new ProfileController();
 

// GET profile by ID
router.get("/", profileController.getProfileById);
router.put("/basic", requestValidatorMiddleware(profileUpdateSchema), profileController.updateProfileBasic);
router.put("/info", requestValidatorMiddleware(profileLocationSchema), profileController.updateProfileContact);
router.put("/professional", requestValidatorMiddleware(professionalInfoSchema), profileController.updateProfessionalInfo);
router.put("/status/:status", profileController.updateProfileStatus);


export default router;
