// src/dto/property/PropertyUpsertDTO.ts
import { z } from "zod";

/**
 * Accepts both strings and numbers/booleans from forms.
 * Use z.coerce.* where payloads arrive as strings (query/body).
 */
export const PropertyUpsertSchema = z
  .object({
    // identity / linkage
    id: z.coerce.number().int().positive().optional().nullable(), // create=no id; update=id>0
    code: z.string().trim().min(1).optional(),
    slug: z.string().trim().min(1).optional(),

    // required core
    name: z.string().trim().min(2, "name must be at least 2 characters"),
    locationId: z.coerce
      .number()
      .int()
      .positive("locationId must be a positive integer"),
    price: z.coerce.number().positive("price must be > 0"),
    size: z.coerce.number().positive("size must be > 0"),
    listingType: z.coerce
      .number()
      .int()
      .positive("listingType must be a positive integer"),

    // optionals (coerced)
    local: z.string().trim().min(1).optional(),
    propertyTypeId: z.coerce.number().int().positive().optional().nullable(),
    apartmentTypeId: z.coerce.number().int().positive().optional().nullable(),
    totalRooms: z.coerce.number().int().min(0).optional().nullable(),
    address: z.string().trim().optional(),
    currencyId: z.coerce.number().int().positive().optional().nullable(),
    permitNo: z.string().trim().optional(),
    statusId: z.coerce.number().int().positive().optional().nullable(),
    startDate: z.string().datetime().or(z.string().date()).optional(), // ISO or YYYY-MM-DD
    expiryDate: z.string().datetime().or(z.string().date()).optional(),
    completionStatus: z.coerce.number().int().min(0).optional().nullable(),
    ownershipTypeId: z.coerce.number().int().positive().optional().nullable(),
    metaTitle: z.string().trim().optional(),
    metaDescription: z.string().trim().optional(),
    bedrooms: z.coerce.number().int().min(0).optional().nullable(),
    bathrooms: z.coerce.number().int().min(0).optional().nullable(),
    permitId: z.coerce.number().int().positive().optional().nullable(),
    unitNo: z.string().trim().optional(),
    projectId: z.coerce.number().int().positive().optional().nullable(),
    tagLine: z.string().trim().optional(),

    // booleans (coerced)
    parking: z.coerce.boolean().optional().default(false),
    swimmingPools: z.coerce.boolean().optional().default(false),
    gym: z.coerce.boolean().optional().default(false),
    isFeatured: z.coerce.boolean().optional().default(false),
    isVerified: z.coerce.boolean().optional().default(false),
    furnished: z.coerce.boolean().optional().default(false),

    // media fields coming from upload step
    govtIssuedQr: z.string().trim().optional().nullable(),

    // features can come as CSV string or array → we validate AFTER split
    features: z
      .union([
        z.string().trim().optional(),
        z.array(z.string().trim()).optional(),
      ])
      .optional(),
  })
  // business rules examples (optional)
  .refine((d) => d.price > 0, {
    path: ["price"],
    message: "price must be greater than 0",
  })
  .refine((d) => d.size > 0, {
    path: ["size"],
    message: "size must be greater than 0",
  });

export type PropertyUpsertDTO = z.infer<typeof PropertyUpsertSchema>;

// After you split features into an array, validate with this:
export const FeaturesArraySchema = z
  .array(z.string().trim().min(1))
  .max(100, "Too many features (max 100).");

export type FeaturesArrayDTO = z.infer<typeof FeaturesArraySchema>;

/** Helper to format zod errors for API responses */
export function formatZodError(err: z.ZodError) {
  return err.issues.map((i) => ({
    path: i.path.join("."),
    message: i.message,
  }));
}
