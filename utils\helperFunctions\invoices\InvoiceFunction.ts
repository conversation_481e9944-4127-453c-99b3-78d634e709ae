export class InvoiceFunction {
  /**
   * Run async mapping with concurrency limit.
   * @param items Array of items to process
   * @param limit Max number of concurrent workers
   * @param fn Async mapping function
   */
  static async mapWithConcurrency<T, R>(
    items: T[],
    limit: number,
    fn: (x: T, i: number) => Promise<R>
  ): Promise<R[]> {
    const out: R[] = new Array(items.length);
    let i = 0;

    const workers = new Array(Math.min(limit, items.length))
      .fill(0)
      .map(async () => {
        while (i < items.length) {
          const idx = i++;
          out[idx] = await fn(items[idx], idx);
        }
      });

    await Promise.all(workers);
    return out;
  }
}
