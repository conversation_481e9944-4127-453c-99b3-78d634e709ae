import { db } from "../../config/database";
import { AUTH } from "../database/queries/auth";
import OTPPurpose from "../enums/OTPPurpose";
import { generateOTP } from "../services/nodemailer/register";
import { Response } from "express";
import { sendForgotPasswordOTP } from "../../utils/services/nodemailer/forgetPassword";
import { sendVerificationEmail } from "../services/nodemailer/accountVerification";
import bcrypt from "bcryptjs";
import { CryptoHelper } from "./CryptoHelper";

export class OTPHelper {

  static async generateAndSendNewOTP(loginData: any, email: string, res: Response, otpPurpose: OTPPurpose) {
    const otp = generateOTP();
    const hashedOtp: string = await CryptoHelper.hashOTP(otp, 10);
    const expireTime = new Date(Date.now() + 10 * 60 * 1000);

    await db.query(AUTH.UPDATE_OTP_INTO_LOGIN, [
      hashedOtp,
      expireTime, //@ts-ignore
      otpPurpose,
      loginData.id,
    ]);

    otpPurpose === OTPPurpose.ACCOUNT_VERIFICATION
      ? await sendVerificationEmail(loginData?.username, email, res, otp) :
      await sendForgotPasswordOTP(loginData?.username, email, res, otp);
  }
  
}