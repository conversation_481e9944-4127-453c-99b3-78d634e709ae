// storage.ts
import fs from "fs";
import path from "path";
import multer from "multer";
import { Request } from "express";

// ✅ Reusable storage generator
export const storageData = (name: string) => {
  const storage = multer.diskStorage({
    destination: (
      req: Request,
      file: Express.Multer.File,
      cb: CallableFunction
    ) => {
      const uploadPath = `public/${name}`;

      try {
        fs.mkdirSync(uploadPath, { recursive: true });
        cb(null, uploadPath);
      } catch (err: any) {
        console.error("❌ Error creating folder:", err);
        cb(err.message, null);
      }
    },

    filename: (
      req: Request,
      file: Express.Multer.File,
      cb: CallableFunction
    ) => {
      const ext = path.extname(file.originalname).toLowerCase();

      // 🛠 Fix weird ".svg+xml" case
      let finalExt = ext;
      if (ext === ".svg+xml") {
        finalExt = ".svg";
      }

      const baseName = path
        .basename(file.originalname, ext)
        .replace(/[^\w.]/g, "_");

      const finalName = `${Date.now()}-${baseName}${finalExt}`;

      cb(null, finalName);
    },
  });

  const upload = multer({ storage });

  return upload;
};

// ✅ Error handler for multer
export const uploadErrorHandler = (
  err: any,
  req: any,
  res: any,
  next: any
) => {
  if (err instanceof multer.MulterError) {
    // Multer-specific errors
    if (err.code === "LIMIT_UNEXPECTED_FILE") {
      return res.status(400).json({
        status: 400,
        success: false,
        message: `Too many files uploaded for field "${err.field}"`,
      });
    }

    if (err.code === "LIMIT_FILE_COUNT") {
      return res.status(400).json({
        status: 400,
        success: false,
        message: `Maximum number of files exceeded.`,
      });
    }

    return res.status(400).json({
      status: 400,
      success: false,
      message: err.message,
    });
  } else if (err) {
    // General errors
    return res.status(500).json({
      status: 500,
      success: false,
      message: "Something went wrong",
      error: err,
    });
  }

  next();
};
