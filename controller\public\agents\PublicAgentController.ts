import { Request, Response } from "express";
import { PublicAgentService } from "../../../service/agents/PublicAgentService";
import asyncHandler from "../../../middleware/trycatch";
import { responseData, response } from "../../../utils/response";

export class PublicAgentController {
  private agentService = new PublicAgentService();

  // Get all agents
  getAllAgents = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.agentService.getAllAgents();
    return responseData(res, 200, "Agents fetched successfully", result);
  });

  // Get properties by agent profile ID
  getPropertiesByProfileId = asyncHandler(
    async (req: Request, res: Response) => {
      const profileId = Number(req.params.profileId);

      if (!profileId || isNaN(profileId)) {
        return response(res, 400, "Valid profileId is required");
      }

      try {
        const result = await this.agentService.getPropertiesByProfileId(
          profileId
        );
        return responseData(
          res,
          200,
          "Properties fetched successfully",
          result
        );
      } catch (err: any) {
        return response(res, 404, err.message || "Agent not found");
      }
    }
  );
}
