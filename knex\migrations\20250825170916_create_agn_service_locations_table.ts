import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(`agn.${TABLE.SERVICE_LOCATIONS}`, (table) => {
    table.increments("id").primary();
    table
      .integer("serviceId")
      .notNullable()
      .references("id")
      .inTable("agn.services")
      .onDelete("CASCADE");

    table
      .integer("locationId")
      .notNullable()
      .references("id")
      .inTable("list.location")
      .onDelete("RESTRICT");

    // optional metadata
    table
      .timestamp("createdOn", { useTz: true })
      .notNullable()
      .defaultTo(knex.fn.now());

    // ensure no duplicate mapping per (serviceId, locationId)
    table.unique(["serviceId", "locationId"], {
      indexName: "uniq_service_locations_pair",
    });

    // quick lookup by service
    table.index(["serviceId"], "idx_service_locations_serviceId");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(`agn.${TABLE.SERVICE_LOCATIONS}`);
}
