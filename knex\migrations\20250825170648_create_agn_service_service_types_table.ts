import type { K<PERSON> } from "knex";
import { TABLE } from "../../utils/database/table";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.createTable(
    `agn.${TABLE.SERVICE_SERVICE_TYPES}`,
    (table) => {
      table.increments("id").primary();
      table
        .integer("serviceId")
        .notNullable()
        .references("id")
        .inTable("agn.services")
        .onDelete("CASCADE");

      table
        .integer("serviceTypeId")
        .notNullable()
        .references("id")
        .inTable("list.services")
        .onDelete("RESTRICT");

      // optional metadata
      table
        .timestamp("createdOn", { useTz: true })
        .notNullable()
        .defaultTo(knex.fn.now());

      // ensure no duplicate mapping per (serviceId, serviceTypeId)
      table.unique(["serviceId", "serviceTypeId"], {
        indexName: "uniq_service_service_types_pair",
      });

      // quick lookup by service
      table.index(["serviceId"], "idx_service_service_types_serviceId");
    }
  );
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.dropTableIfExists(`agn.${TABLE.SERVICE_SERVICE_TYPES}`);
}
