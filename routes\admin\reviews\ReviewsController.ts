import express from "express";
import { AdminReviewController } from "../../../controller/admin/reviews/AdminReviewController";
import { storageData } from "../../../utils/services/multer";
import { requestValidatorMiddleware } from "../../../middleware/requestValidatorMiddleware";
import { UpdateReviewStatusDTO } from "../../../dto/reviews/UpdateReviewStatusDTO";

const adminReviewController = new AdminReviewController();

const router = express.Router();
const upload = storageData("reviews");

router.get("/", adminReviewController.getAllReviews);

router.get("/stats", adminReviewController.getReviewsStats);

router.get("/:id", adminReviewController.getReviewById);

router.patch("/:id/status", upload.none(),  requestValidatorMiddleware(UpdateReviewStatusDTO), adminReviewController.updateReviewStatus);

router.delete("/:id", adminReviewController.deleteReview);

router.post("/:id/notes", upload.none(), adminReviewController.addReviewNote);

router.get("/:id/notes", adminReviewController.getReviewNotes);

router.get("/:id/history", adminReviewController.getReviewHistory);

router.patch("/:id/flag", upload.none(), adminReviewController.flagReview);

router.patch("/:id/hide", upload.none(), adminReviewController.hideReview);

router.patch("/:id/restore", upload.none(), adminReviewController.restoreReview);

export default router;
