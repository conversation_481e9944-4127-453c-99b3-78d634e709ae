import { sql } from "drizzle-orm";
import { drizzleDb } from "../../config/database";
import { TABLE } from "../../utils/database/table";

export class AgentDetailsRepository {

    async getAgentDetailsByProfileId(profileId: number) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM agn."agentdetails" WHERE profile_id = ${profileId}`
        );
        return result;
    }


}