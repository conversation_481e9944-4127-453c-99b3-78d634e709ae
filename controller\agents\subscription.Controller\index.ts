import { Request, response, Response } from "express";
import async<PERSON>and<PERSON> from "../../../middleware/trycatch";
import { error, errorResponse, responseData } from "../../../utils/response";
import { db } from "../../../config/database";
import { sendSubscriptionStatusEmail } from "../../../utils/services/nodemailer/sendSubscriptionStatusEmail";
import { AGENT_ACCOUNT_TYPE } from "../../../utils/enums/account.enum";
import Stripe from "stripe";
import stripeKeys from "../../../config/stripeConfigs";
import { AUTH } from "../../../utils/database/queries/auth";
import { SubscriptionQueries } from "../../../utils/database/queries/SubscriptionQueries";

const stripe = new Stripe(stripeKeys.stripe.secretKey);

// --- Package Types ---
export const getAllPackageTypes = asyncHandler(
  async (req: Request, res: Response) => {
    const params = [
      1,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      "name",
      null,
      null,
      null,
      null,
    ];

    const { rows } = await db.query(
      "SELECT * FROM look.sp_packagetype($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)",
      params
    );
    const result = rows[0];
    const spResult = result.sp_packagetype;
    const { rows: settingsRow } = await db.query(
      `SELECT * FROM web.settings ORDER BY id ASC LIMIT 1`
    );
    if (spResult.type === "error") return error(res, 400, spResult.message);
    return responseData(res, 200, spResult.message, {
      settings: settingsRow.length ? settingsRow[0] : {},
      packages: spResult.data,
    });
  }
);

// Create Subscription stripe checkout session with Package Type ID
export const createSubscriprionWithPackageTypeId = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();

    try {
      await client.query("BEGIN");

      const user = req.user;
      let { packageTypeId } = req.body;

      const { rows: profileRows } = await db.query(AUTH.SELECT_BY_ID, [
        user?.id,
      ]);

      const { rows: statusRows } = await db.query(AUTH.SELECT_STATUS_BY_ID, [
        profileRows[0].statusId,
      ]);

      if (
        !statusRows.length ||
        statusRows[0].name.toLowerCase() !== "activated"
      ) {
        await client.query("ROLLBACK");
        return error(
          res,
          403,
          "Your account is not active. Please contact support or complete the activation process before purchasing a subscription."
        );
      }

      packageTypeId = Number(packageTypeId);
      if (!packageTypeId) {
        await client.query("ROLLBACK");
        return error(res, 400, "packageTypeId is required");
      }

      // STEP 1: Get package details
      const pkgParams = [
        0,
        packageTypeId,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        null,
        "id",
        null,
        null,
        null,
        null,
      ];

      const pkgResult = await client.query(
        "SELECT * FROM look.sp_packagetype($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12,$13, $14, $15)",
        pkgParams
      );

      const pkgData = pkgResult.rows[0]?.sp_packagetype;
      if (!pkgData || pkgData.type === "error" || !pkgData.data) {
        await client.query("ROLLBACK");
        return error(res, 400, "Package type not found");
      }

      if (pkgData.data.statusId !== 1) {
        await client.query("ROLLBACK");
        return error(res, 400, "Package type is not active");
      }

      // STEP 2: Get discount details
      const discountResult = await db.query(
        `SELECT * FROM web.discounts WHERE id = $1 LIMIT 1`,
        [pkgData.data.discountId]
      );

      const discount = discountResult.rows[0] || {};

      const isFree = Number(pkgData.data.price) === 0;

      // STEP 3: If Free Plan
      if (isFree) {
        await client.query("COMMIT");

        /* STEP 3: upsert subscription */
        const now = new Date();
        const endDate = new Date(now);
        endDate.setMonth(now.getMonth() + 1);

        /* STEP 1: grab “Cancelled” status id */
        const statusNames = ["Cancelled"];
        const statusRes = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(statusNames),
          statusNames
        );
        if (statusRes.rows.length === 0) throw new Error("Status not found");
        const cancelledStatusId = statusRes.rows[0].id;

        // — cancel *other* active subs
        const { rows: oldSubs } = await client.query(
          SubscriptionQueries.GET_SUBSCRIPTION_BY_PACKAGE_ID,
          [user.id, packageTypeId]
        );
        for (const sub of oldSubs) {
          await client.query(
            `UPDATE list.subscription
               SET "statusId" = $1
             WHERE id = $2`,
            [cancelledStatusId, sub.id]
          );
        }

        const paymentStatusNames = ["Paid"];
        const statusPaymentRes = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(paymentStatusNames),
          statusNames
        );

        if (statusPaymentRes.rows.length === 0)
          throw new Error("Status not found");
        const PaymentStatusId = statusPaymentRes.rows[0].id;

        // — insert new sub via look.sp_subscription
        const price = pkgData.data.price || 0;
        const currency = pkgData.data.currency || "AED";
        const subParams = [
          2, //  1  p_fnid  (INSERT/UPDATE)
          null, //  2  p_id    (null ⇒ INSERT)
          null, //  3  p_subscriptionid  (Stripe ID)
          pkgData.data.name, //  4  p_package          (plan name)  ❖
          null, //  5  p_typeid           (unused)
          null, //  6  p_details          (unused)
          1, //  7  p_statusid         (1 = Active)
          user.id, //  8  p_profileid
          packageTypeId, //  9  p_packagetypeid
          now, // 10  p_startdate
          endDate, // 11  p_enddate
          null, // 12  p_renewaldate
          price, // 13  p_price
          currency, // 14  p_currency
          PaymentStatusId, // 15  p_paymentstatusid
          null, // 16  p_sortby  (unused when fnid=2)
        ];

        const subRes = await client.query(
          "SELECT * FROM look.sp_subscription($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16)",
          subParams
        );

        const subData = subRes.rows[0]?.sp_subscription;
        if (!subData || subData.type === "error")
          return error(res, 403, subData?.message || "Failed to subscribe");
 
        return responseData(res, 200, "Free subscription activated.", {
          noRedirect: true,
          message: `Subscribed to ${pkgData.data.name} successfully.`,
          packageTypeId: pkgData.data.id,
          packageTypeName: pkgData.data.name,
          packageTypePrice: pkgData.data.price,
          packageTypeCurrency: pkgData.data.currency,
          packageTypeColorTheme: pkgData.data.colorTheme,
          packageTypeFeatures: pkgData.data.features || [],
        });
      }

      if (!pkgData.data.stripePlanId) {
        await client.query("ROLLBACK");
        return error(res, 400, "Something went wrong, please try again.");
      }

      // STEP 4: Create Stripe Checkout Session
      const discountCouponId = discount?.stripeDiscountId;

      // Stripe Checkout session creation
      const sessionOptions: any = {
        mode: "subscription",
        customer_email: req.user?.email,
        payment_method_types: ["card"],
        line_items: [
          {
            price: pkgData?.data?.stripePlanId,
            quantity: 1,
          },
        ],
        success_url: `${process.env.FRONTEND_URL}/checkout/success?sessionId={CHECKOUT_SESSION_ID}&userId=${req.user.id}`,
        cancel_url: `${process.env.FRONTEND_URL}/checkout/cancel?sessionId={CHECKOUT_SESSION_ID}&userId=${req.user.id}`,
      };

      if (discountCouponId) {
        sessionOptions.discounts = [{ coupon: discountCouponId }];
      }

      const session = await stripe.checkout.sessions.create(sessionOptions);

      await client.query("COMMIT");

      return responseData(res, 200, "Subscription Checkout session created.", {
        sessionId: session.id,
        sessionUrl: session.url,
        packageTypeId: pkgData.data.id,
        packageTypeName: pkgData.data.name,
        packageTypePrice: pkgData.data.price,
        packageTypeCurrency: pkgData.data.currency,
        packageTypeStripePlanId: pkgData.data.stripePlanId,
        packageTypeColorTheme: pkgData.data.colorTheme,
        packageTypeUserType: pkgData.data.userType,
        packageTypeStatusId: pkgData.data.statusId,
        packageTypeFeatures: pkgData.data.features || [],
        packageTypeDescription: pkgData.data.description,
      });
    } catch (err) {
      await client.query("ROLLBACK");
      console.error("Transaction error:", err);
      return error(
        res,
        500,
        "An error occurred while creating or updating the subscription."
      );
    } finally {
      client.release();
    }
  }
);

export const cancelSubscription = asyncHandler(
  async (req: Request, res: Response) => {
    const profileId = req.user.id;
    const { subscriptionId } = req.params;

    const client = await db.connect();
    try {
      await client.query("BEGIN");

      const { rows } = await client.query(
        `SELECT s.*, p."firstName", p."middleName", p."lastName", p.email,
                pt.name AS "packageName"
         FROM list.subscription s
         JOIN prf.profile p ON s."profileId" = p.id
         JOIN look.packagetype pt ON s."packageTypeId" = pt.id
        WHERE s.id = $1 AND s."profileId" = $2`,
        [subscriptionId, profileId]
      );

      if (rows.length === 0) {
        await client.query("ROLLBACK");
        return error(
          res,
          404,
          "No active subscription found for this profile."
        );
      }

      const subscription = rows[0];
      const fullName = [
        subscription.firstName,
        subscription.middleName,
        subscription.lastName,
      ]
        .filter(Boolean)
        .join(" ");

      // STEP 1: Cancel on Stripe (if paid)
      if (
        subscription.subscriptionId &&
        !subscription.subscriptionId.startsWith("free_")
      ) {
        try {
          await stripe.subscriptions.cancel(subscription.subscriptionId);
        } catch (err) {
          console.error("Stripe cancellation failed:", err);
        }
      }

      // STEP 2: Get “Cancelled” status ID
      const cancelStatusNames = ["Cancelled"];
      const statusResp = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(cancelStatusNames),
        cancelStatusNames
      );
      if (statusResp.rows.length === 0) {
        await client.query("ROLLBACK");
        return error(res, 400, "Cancelled status not found");
      }
      const cancelledStatusId = statusResp.rows[0].id;

      // STEP 3: Update subscription record manually
      const now = new Date();
      await client.query(
        `UPDATE list.subscription
         SET "statusId" = $1,
             "modifiedOn" = $2
         WHERE id = $3`,
        [cancelledStatusId, now, subscriptionId]
      );

      // STEP 4: Send cancellation email
      await sendSubscriptionStatusEmail(
        fullName,
        subscription.email,
        subscription.packageName,
        "Cancelled",
        res
      );

      // STEP 5: Activate free plan
      const statusNames = ["Activated"];
      const statusRes = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      if (statusRes.rows.length === 0) throw new Error("Status not found");
      const activeStatusId = statusRes.rows[0].id;

      const { rows: freePkgRows } = await client.query(
        `SELECT * FROM look.packagetype
         WHERE price = $1 AND "statusId" = $2 AND "userType" = $3
         ORDER BY id ASC
         LIMIT 1`,
        [
          0,
          activeStatusId,
          AGENT_ACCOUNT_TYPE.INDIVIDUAL === req.user.accountType
            ? "agent"
            : "agency",
        ]
      );

      if (!freePkgRows.length) {
        console.warn("No active free package found");
        await client.query("COMMIT");
      } else {
        const freePkg = freePkgRows[0];

        /* STEP 3: upsert subscription */
        const now = new Date();
        const endDate = new Date(now);
        endDate.setMonth(now.getMonth() + 1);

        /* STEP 1: grab “Cancelled” status id */
        const statusNames = ["Cancelled"];
        const statusRes = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(statusNames),
          statusNames
        );
        if (statusRes.rows.length === 0) {
          await client.query("ROLLBACK");
          return error(res, 400, "Cancelled status not found");
        }
        const cancelledStatusId = statusRes.rows[0].id;

        // — cancel *other* active subs
        const { rows: oldSubs } = await client.query(
          SubscriptionQueries.GET_SUBSCRIPTION_BY_PACKAGE_ID,
          [profileId, freePkg.id]
        );
        for (const sub of oldSubs) {
          await client.query(
            `UPDATE list.subscription
               SET "statusId" = $1
             WHERE id = $2`,
            [cancelledStatusId, sub.id]
          );
        }

        const paymentStatusNames = ["Paid"];
        const statusPaymentRes = await client.query(
          AUTH.SELECT_ACCOUNT_STATUS(paymentStatusNames),
          statusNames
        );

        if (statusPaymentRes.rows.length === 0) {
          await client.query("ROLLBACK");
          return error(res, 400, "Payment status not found");
        }
        const PaymentStatusId = statusPaymentRes.rows[0].id;

        // — insert new sub via look.sp_subscription
        const price = freePkg.price || 0;
        const currency = freePkg.currency || "AED";

        const subParams = [
          2, //  1  p_fnid  (INSERT/UPDATE)
          null, //  2  p_id    (null ⇒ INSERT)
          null, //  3  p_subscriptionid  (Stripe ID)
          freePkg.name, //  4  p_package          (plan name)  ❖
          null, //  5  p_typeid           (unused)
          null, //  6  p_details          (unused)
          1, //  7  p_statusid         (1 = Active)
          profileId, //  8  p_profileid
          freePkg.id, //  9  p_packagetypeid
          now, // 10  p_startdate
          endDate, // 11  p_enddate
          null, // 12  p_renewaldate
          price, // 13  p_price
          currency, // 14  p_currency
          PaymentStatusId, // 15  p_paymentstatusid
          null, // 16  p_sortby  (unused when fnid=2)
        ];

        await client.query(
          "SELECT * FROM look.sp_subscription($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14,$15,$16)",
          subParams
        );

        await client.query("COMMIT");
      }

      return responseData(
        res,
        200,
        "Subscription cancelled and free plan activated.",
        {
          cancelledSubscriptionId: subscriptionId,
          freePackageActivated: true,
        }
      );
    } catch (err) {
      console.error("Cancel Subscription Error:", err);
      await client.query("ROLLBACK");
      return error(res, 500, "Failed to cancel subscription.");
    } finally {
      client.release();
    }
  }
);

export const deleteSubscription = asyncHandler(
  async (req: Request, res: Response) => {
    const profileId = req.user.id;
    const { subscriptionId } = req.params;

    const { rows } = await db.query(
      'SELECT s.*, p."firstName", p."middleName", p."lastName", p.email, pt.name AS "packageName" FROM list.subscription s ' +
        'JOIN prf.profile p ON s."profileId" = p.id ' +
        'JOIN look.packagetype pt ON s."packageTypeId" = pt.id ' +
        'WHERE s.id = $1 AND s."profileId" = $2',
      [subscriptionId, profileId]
    );

    if (rows.length === 0) return error(res, 404, "Subscription not found");

    const subscription = rows[0];
    const fullName = [
      subscription.firstName,
      subscription.middleName,
      subscription.lastName,
    ]
      .filter(Boolean)
      .join(" ");

    const params = [
      3,
      subscriptionId,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
    ];
    const result = await db.query(
      "SELECT * FROM look.sp_subscription($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)",
      params
    );
    const spResult = result.rows[0]?.sp_subscription;

    if (!spResult || spResult.type === "error") {
      return error(
        res,
        400,
        spResult?.message || "Failed to delete subscription"
      );
    }

    await sendSubscriptionStatusEmail(
      fullName,
      subscription.email,
      subscription.packageName,
      "Deleted",
      res
    );
    return responseData(res, 200, spResult.message, spResult.data);
  }
);

export const getFeatureValuesByUserType = asyncHandler(
  async (req: Request, res: Response) => {
    const userType =
      AGENT_ACCOUNT_TYPE.INDIVIDUAL === req.user.accountType
        ? "agent"
        : "agency";

    const profileId = req.user.id;

    // STEP 1: Fetch current active subscription
    const subQuery = `
      SELECT s."id", s."packageTypeId", p.name AS "packageName"
      FROM list.subscription s
      JOIN look.packagetype p ON s."packageTypeId" = p.id
      WHERE s."profileId" = $1 AND s."statusId" = 1
      LIMIT 1;
    `;

    const { rows: subRows } = await db.query(subQuery, [profileId]);
    const activePackageId = subRows[0]?.packageTypeId;
    const activePackageName = subRows[0]?.packageName;
    const subscriptionId = subRows[0]?.id;

    // STEP 2: Fetch all packages with their feature values
    const { rows: packages } = await db.query(
      `SELECT 
      p.id AS "packageId",
      p.name,
      p."userType",
      p.price,
      p.currency,
      p."statusId",
      p."colorTheme",
      p."stripePlanId",
      p.interval,
      p."discountId",
      CASE
        WHEN d.id IS NOT NULL THEN jsonb_build_object(
          'id', d.id,
          'name', d.name,
          'type', d.type,
          'value', d.value,
          'createdAt', d."created_at"
        )
      ELSE NULL
      END AS discount,
      json_agg(
        json_build_object(
          'featureName', f."featureName",
          'featureValue', v."featureValue",
          'featureType', f."featureType",
          'featureConstant', f."featureConstant",
          'displayOrder', f."displayOrder"
        )
        ORDER BY f."displayOrder"
      ) FILTER (WHERE f."featureId" IS NOT NULL) AS features
    FROM look.packagetype p
    LEFT JOIN look.packagefeaturevalues v ON p.id = v."packageTypeId"
    LEFT JOIN look.packagefeaturesmeta f ON v."featureId" = f."featureId"
    LEFT JOIN web.discounts d ON p."discountId" = d.id
    WHERE p."userType" = $1 AND p."statusId" = 1
    GROUP BY p.id, d.id
    ORDER BY p.price ASC, p.id;`,
      [userType]
    );

    // STEP 3: Mark the current subscribed package
    const enhancedPackages = packages.map((pkg) => ({
      ...pkg,
      isActive: pkg.packageId === activePackageId,
      subscriptionId: subscriptionId,
      subscribedPackageName:
        pkg.packageId === activePackageId ? activePackageName : null,
    }));

    const { rows: settingsRow } = await db.query(
      `SELECT * FROM web.settings ORDER BY id ASC LIMIT 1`
    );

    return responseData(res, 200, "Feature values fetched", {
      settings: settingsRow.length ? settingsRow[0] : {},
      packages: enhancedPackages,
    });
  }
);
