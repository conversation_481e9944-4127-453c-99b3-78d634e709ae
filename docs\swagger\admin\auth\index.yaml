components:
  securitySchemes:
    adminAuthToken:    
      type: api<PERSON>ey
      in: cookie
      name: adminAuthToken  
       
paths:
  /admin/logout:
    post:
      tags:
        - Auth
      summary: Admin Logout
      description: Logs out the admin user by clearing the authentication cookie (`adminAuthToken`).
      security:
        - cookieAuth: []   # Custom cookie-based auth
      responses:
        "200":
          description: Logout successful, cookie cleared
          headers:
            Set-Cookie:
              description: Clears the `adminAuthToken` cookie
              schema:
                type: string
                example: adminAuthToken=; HttpOnly; Secure; SameSite=Strict; Max-Age=0
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 200
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: Logout successful
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 401
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 
                      - Unauthorized access
                      - Invalid token
                      - Session expired, please sign in again!
                      - Unauthorized access
        "400":
          description: Bad Request — Unauthorized user access token 
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 400
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: 
                      - You are not authorized to access this resource.
                      - Failed to log out
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: number
                    example: 500
                  success:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: Internal server error!
