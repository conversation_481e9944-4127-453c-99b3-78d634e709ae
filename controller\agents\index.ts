import { Request, Response } from "express";
import { AUTH } from "../../utils/database/queries/auth";
import { db } from "../../config/database";
import {
  errorCatchResponse,
  errorResponse,
  error,
  response,
  responseData,
} from "../../utils/response";
import as<PERSON><PERSON>and<PERSON> from "../../middleware/trycatch";
import { TABLE } from "../../utils/database/table";
import { AGENTS } from "../../utils/database/queries/agentDetails";
import { upsertToken } from "../../utils/helperFunctions/upsertToken";
import { getSignedJwt } from "../../utils/services/jwt";
import { loginSchema } from "../../utils/validations/auth.validation";
import bcrypt from "bcryptjs";
import { sendApplicationSubmittedEmail } from "../../utils/services/nodemailer/sendApplicationSubmittedEmail";
import { REFERRALS } from "../../utils/database/queries/referrals";
import { sendAdminApplicationSubmittedEmail } from "../../utils/services/nodemailer/sendApplicationSubmittedEmailAdmin";
import { sendUserInvitationEmail } from "../../utils/services/nodemailer/sendUserInvitationEmail";
import { sendInvitationToTeamMember } from "../../utils/services/nodemailer/sendInvitationToTeamMember";
import { v4 as uuidv4 } from "uuid";
import { sendPasswordResetEmail } from "../../utils/services/nodemailer/sendPasswordResetEmail";
import { AgentService } from "../../service/agent-services";
import { IndividualCompleteProfileDto } from "../../dto/individual/individual-complete-profile";
import { CompanyProfileDto } from "../../dto/company-profile/company-profile";

const baseUrl = process.env.BASE_URL?.replace(/\/+$/, "");

export const getCountsByProfileId = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const profileId: number = Number(req.params.id)
      if (!Number.isFinite(profileId)) throw { statusCode: 400, message: "Invalid id" };
      const countsData = await AgentService.getCountsByProfileId(profileId);

      console.log(countsData);
      return responseData(res, 200, "Counts fetched successfully", countsData);
    } catch (err: any) {
      console.error("Error occured while getting counts:", err);
      return response(
        res,
        err.statusCode || 500,
        err.message || "Error occured while getting counts:"
      );
    }
  });


export const createAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const {
      workType,
      operationArea,
      licenseType,

      licenseNumber,
      licenseExpiryDate,
      licenseIssueDate,

      yearOfExperience,
      otherForFreelancer,

      employerName,
      position,
      employedLocation,

      industryMission,
      industrySubCategory,
      specializationMission,

      summary,
      languages,
      areaCovered,
      nationality,
      servicesOffered,
    } = req.body;

    if (!workType) {
      return errorResponse(res, "Work Type is required");
    }

    try {
      const userId = req.user.id;
      const profileId = Number(userId);

      const existingAgent = await db.query(AGENTS.CHECK_EXISTING_AGENT, [
        profileId,
      ]);
      if (existingAgent.rows.length > 0) {
        return response(
          res,
          400,
          "You have already created an account. Please update it."
        );
      }

      const existingAccount = await db.query(AGENTS.CHECK_EXISTING_ACCOUNT, [
        profileId,
      ]);
      if (existingAccount.rows.length > 0) {
        return response(
          res,
          400,
          "You have already created an account. Please update it."
        );
      }

      const profilePhotos =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.profilePhoto?.map((file) => `/agentDetails/${file.filename}`) || [];
      const licenseDocs =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.licenseDoc?.map((file) => `/agentDetails/${file.filename}`) || [];
      const freelancePermitDocs =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.freelancePermitDoc?.map(
          (file) => `/agentDetails/${file.filename}`
        ) || [];
      const tradeLicenseDocs =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.tradeLicenseDoc?.map((file) => `/agentDetails/${file.filename}`) ||
        [];
      const employmentLetters =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.employmentDoc?.map((file) => `/agentDetails/${file.filename}`) || [];
      const certification =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.certifications?.map((file) => `/agentDetails/${file.filename}`) ||
        [];

      const emiratesId =
        (
          req.files as { [key: string]: Express.Multer.File[] }
        )?.emiratesId?.map((file) => `/agentDetails/${file.filename}`) || [];
      const visa =
        (req.files as { [key: string]: Express.Multer.File[] })?.visa?.map(
          (file) => `/agentDetails/${file.filename}`
        ) || [];
      const passport =
        (req.files as { [key: string]: Express.Multer.File[] })?.passport?.map(
          (file) => `/agentDetails/${file.filename}`
        ) || [];

      const CreateQueryToStoreData = await db.query(AGENTS.CREATE_AGENT, [
        profileId,
        3,
        profileId,
      ]);

      if (!CreateQueryToStoreData.rows[0]) {
        return errorResponse(res, "Error creating agent profile");
      }
      const QueryToStoreInAccounts = await db.query(AGENTS.CREATE_ACCOUNT, [
        profileId,
        0,
        profileId,
      ]);

      if (!QueryToStoreInAccounts.rows[0]) {
        return errorResponse(res, "Error creating agent Profile account");
      }
      const query: any = {
        licenseType,
        licenseNumber,
        licenseExpiryDate,
        profileId,
        profilePhotos,
        workType,
        operationArea,
        licenseDocs,
        freelancePermitDocs,
        tradeLicenseDocs,
        employmentLetters,
        industryMission,
        industrySubCategory,
        specializationMission,
        yearOfExperience,
        otherForFreelancer,
        employerName,
        position,
        employedLocation,
        summary,
        languages,
        areaCovered,
        certification,
        emiratesId,
        visa,
        passport,
      };

      if (workType.trim() === "licensedAgent") {
        if (!licenseType) {
          return errorResponse(res, "License Type is required");
        }
        if (!licenseNumber) {
          return errorResponse(res, "License Number is required");
        }
        if (!licenseExpiryDate) {
          return errorResponse(res, "License Expiry Date is required");
        }
        query.licenseType = licenseType.trim();
        query.licenseNumber = licenseNumber.trim();
        query.licenseExpiryDate = licenseExpiryDate.trim();
        query.licenseDocs = licenseDocs;

        const CreateQueryToStoreData = await db.query(
          AGENTS.ADD_LICIENCE_DETAILS,
          [
            Number(query.licenseType),
            query.licenseNumber,
            licenseIssueDate,
            query.licenseExpiryDate,
            profileId,
          ]
        );
        if (!CreateQueryToStoreData.rows[0]) {
          return errorResponse(
            res,
            "Error creating agent profile license details"
          );
        }
      }

      if (workType === "freelancer") {
        if (!yearOfExperience) {
          return errorResponse(res, "Year Of Experience is required");
        }

        query.yearOfExperience = yearOfExperience.trim() || null;
        query.freelancePermitDocs = freelancePermitDocs;
        query.tradeLicenseDocs = tradeLicenseDocs || null;
        query.otherForFreelancer = otherForFreelancer.trim() || null;
      }

      if (workType === "employed") {
        if (!employerName) {
          return errorResponse(res, "Employer Name is required");
        }
        if (!position) {
          return errorResponse(res, "Position is required");
        }
        if (!employedLocation) {
          return errorResponse(res, "Employed Location is required");
        }

        query.employerName = employerName.trim() || null;
        query.position = position.trim() || null;
        query.employedLocation = employedLocation.trim() || null;
        query.employmentLetters = employmentLetters || null;
      }

      query.industrySubCategory = industrySubCategory.trim() || null;
      query.specializationMission = specializationMission.trim() || null;
      query.operationArea = operationArea.trim() || null;
      query.summary = summary.trim() || null;
      query.languages = languages || null;
      query.areaCovered = areaCovered || null;

      query.servicesOffered = servicesOffered || null;
      query.nationality = nationality || null;

      query.certifications = certification;
      query.emiratesId = emiratesId;
      query.passport = passport;
      query.visa = visa;

      const agentId = CreateQueryToStoreData.rows[0].id;

      const queryToInsertAgentDetails = await db.query(
        `INSERT INTO ${TABLE.AGENT_DETAILS} (
          agent_id, profile_id, "workType", "operationArea", "licenseDocs", "freelancePermitDocs", "tradeLicenseDocs",
          "industrySubCategory", "specializationMission", "yearOfExperience", "otherForFreelancer", "employerName",
          position, "employedLocation", "employmentLetters", summary, languages, "areaCovered", certifications,"profilePhotos",
          nationality , "servicesOffered","emiratesId", "visa", "passport"
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7,
          $8, $9, $10, $11, $12,
          $13, $14, $15, $16, $17, $18, $19,$20 
          , $21 , $22 ,$23 ,$24,$25
        ) RETURNING *`,
        [
          Number(agentId),
          profileId,

          query.workType,
          query.operationArea,
          query.licenseDocs,
          query.freelancePermitDocs,
          query.tradeLicenseDocs,

          query.industrySubCategory,
          query.specializationMission,
          query.yearOfExperience,
          query.otherForFreelancer,
          query.employerName,
          query.position,
          query.employedLocation,
          query.employmentLetters,
          query.summary,
          query.languages,
          query.areaCovered,
          query.certifications,
          query.profilePhotos,
          query.nationality,
          query.servicesOffered,
          query.emiratesId,
          query.visa,
          query.passport,
        ]
      );

      if (!queryToInsertAgentDetails.rows[0]) {
        console.log(
          "Error inserting agent details:",
          queryToInsertAgentDetails
        );
        return errorResponse(res, "Error creating agent profile details");
      }
      return response(res, 200, "Agent profile created successfully");
    } catch (error) {
      console.error("Error  creating agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while creating agent profile"
      );
    }
  }
);

export const activateDeactivateAccount = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user.id;
    const profileId = Number(userId);

    try {
      const currentStatusQuery = await db.query(
        `SELECT "statusId" FROM ${TABLE.PROFILE_TABLE} WHERE "id" = $1`,
        [profileId]
      );

      const currentStatus = currentStatusQuery.rows[0]?.statusId;
      if (!currentStatus) {
        return errorResponse(res, "User not found.");
      }

      if (currentStatus === 1 || currentStatus === 2) {
        const newStatus = currentStatus === 1 ? 2 : 1;

        await db.query(
          `UPDATE ${TABLE.PROFILE_TABLE} SET "statusId" = $1 WHERE "id" = $2`,
          [newStatus, profileId]
        );

        return response(
          res,
          200,
          `Account has been ${
            newStatus === 1 ? "activated" : "deactivated"
          } successfully.`
        );
      }

      if (currentStatus === 3) {
        return errorResponse(res, "Your account has not been approved yet.");
      }

      if (currentStatus === 4 || currentStatus === 5) {
        const message =
          currentStatus === 4
            ? "Account is temporarily suspended."
            : "Account is archived and cannot be used.";
        return errorResponse(res, message);
      }

      return errorResponse(res, "Invalid account status.");
    } catch (error) {
      console.error("Error activating/deactivating account:", error);
      return errorCatchResponse(
        res,
        "An error occurred while activating/deactivating account"
      );
    }
  }
);

export const getAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.user.id;
    const profileId = Number(userId);

    // Validate profileId
    if (!profileId || isNaN(profileId)) {
      return errorResponse(res, "Invalid profile ID");
    }

    const loginId = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
      profileId,
    ]);

    // Check if login record exists
    if (!loginId.rows[0]) {
      return errorResponse(res, "Login record not found for this profile");
    }

    console.log(loginId.rows[0].id);
    try {
      const userResult = await db.query(AUTH.SELECT_PROFILE_WITH_LOGIN, [
        loginId.rows[0].id,
      ]);

      if (userResult.rows.length === 0) {
        return errorResponse(res, "No account found.");
      }

      let user = userResult.rows[0];
      delete user.passwordHash;
      if (user.profileImage) {
        user.profileImage = user.profileImage;
      }
      let compayDetails = null;
      if (user.accountRole === "Company/Agency/PropertyDeveloper") {
        const companyResult = await db.query(
          `SELECT * FROM ${TABLE.AGENCIES} WHERE "profileId" = $1`,
          [profileId]
        );
        compayDetails = companyResult.rows[0];
      }
      const agentResult = await db.query(
        `SELECT 
           p."profileId", 
           p."statusId", 
           s."name" AS "agentProfileStatus", 
           s."description" AS "statusDescription"
         FROM "${TABLE.AGENT}" p
         LEFT JOIN "${TABLE.STATUSES}" s ON p."statusId" = s.id
         WHERE p."profileId" = $1`,
        [profileId]
      );
      const accountResult = await db.query(
        `SELECT * FROM ${TABLE.ACCOUNT} WHERE "profileId" = $1`,
        [profileId]
      );

      const agentDetailsResult = await db.query(
        `SELECT * FROM ${TABLE.AGENT_DETAILS} WHERE profile_id = $1`,
        [profileId]
      );

      const agentDetails = agentDetailsResult.rows[0] || null;

      let operationAreas = null;
      // 2. Fetch operation areas
      if (agentDetails && agentDetails.operationArea) {
        const operationAreasResult = await db.query(
          `SELECT oa.id, oa.name, oa.nationality
            FROM list.countries oa
            WHERE oa.id = ANY (string_to_array($1, ',')::int[])
          `,
          [agentDetails.operationArea]
        );
        operationAreas = operationAreasResult.rows;
      }

      let industryMissionNames: string[] = [];
      let industrySubMissionNames: string[] = [];
      if (agentDetails) {
        const fileFields = [
          "profilePhotos",
          "licenseDocs",
          "freelancePermitDocs",
          "tradeLicenseDocs",
          "employmentLetters",
          "certifications",
        ];

        fileFields.forEach((field) => {
          if (Array.isArray(agentDetails[field])) {
            agentDetails[field] = agentDetails[field].map((path: string) =>
              path ? baseUrl + path : path
            );
          }
        });

        if (agentDetails.industryMission) {
          const missionIds = agentDetails.industryMission
            .split(",")
            .map((id: string) => parseInt(id.trim(), 10))
            .filter((id: number) => !isNaN(id)); // Filter out NaN values

          if (missionIds.length > 0) {
            const serviceNamesResult = await db.query(
              `SELECT name FROM list.services WHERE id = ANY($1::int[])`,
              [missionIds]
            );
            industryMissionNames = serviceNamesResult.rows.map(
              (row: any) => row.name
            );
          }
        }
        if (agentDetails.industrySubCategory) {
          const missionIds = agentDetails.industrySubCategory
            .split(",")
            .map((id: string) => parseInt(id.trim(), 10))
            .filter((id: number) => !isNaN(id)); // Filter out NaN values

          if (missionIds.length > 0) {
            const serviceNamesResult = await db.query(
              `SELECT name FROM list.services WHERE id = ANY($1::int[])`,
              [missionIds]
            );
            industrySubMissionNames = serviceNamesResult.rows.map(
              (row: any) => row.name
            );
          }
        }
      }

      const agentlicenses = await db.query(
        `SELECT * FROM agentlicenses WHERE "agentId" = $1 OR "agencyId" = $1`,
        [profileId]
      );
      const companyrole = await db.query(
        `SELECT * FROM companyrole  WHERE "companyId" = $1`,
        [profileId]
      );

      const responseObject = {
        user,
        agent: agentResult.rows[0] || null,
        account: accountResult.rows[0] || null,
        agentDetails: agentDetailsResult.rows[0] || null,
        operationAreas,
        industryMissionNames,
        industrySubMissionNames,
        agentlicenses: agentlicenses.rows,
        compayDetails,
        companyrole: companyrole.rows,
      };

      return responseData(
        res,
        200,
        "Agent profile retrieved successfully.",
        responseObject
      );
    } catch (error) {
      console.error("Error retrieving agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while retrieving agent profile"
      );
    }
  }
);

export const updateAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const profileId = Number(userId);

      const profilePhotos =
        (req.files as any)?.profilePhoto?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];
      const licenseDocs =
        (req.files as any)?.licenseDoc?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];
      const freelancePermitDocs =
        (req.files as any)?.freelancePermitDoc?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];
      const tradeLicenseDocs =
        (req.files as any)?.tradeLicenseDoc?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];
      const employmentLetters =
        (req.files as any)?.employmentDoc?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];
      const certifications =
        (req.files as any)?.certifications?.map(
          (file: Express.Multer.File) => `/agentDetails/${file.filename}`
        ) || [];

      const {
        workType,
        operationArea,
        licenseType,
        licenseNumber,
        licenseExpiryDate,
        licenseIssueDate,
        yearOfExperience,
        otherForFreelancer,
        employerName,
        position,
        employedLocation,
        industryMission,
        industrySubCategory,
        specializationMission,
        summary,
        languages,
        areaCovered,

        workTypeCategory,
        servicesOffered,
        nationality,
      } = req.body;

      const agentProfile = await db.query(
        `SELECT * FROM ${TABLE.AGENT} WHERE "profileId" = $1`,
        [profileId]
      );

      if (!agentProfile.rows.length) {
        return errorResponse(res, "Agent profile does not exist");
      }

      const licenseFieldsMap = {
        licenseTypeId:
          licenseType !== undefined ? Number(licenseType) : undefined,
        licenseNo: licenseNumber,
        licenseIssueDate,
        licenseExpiredDate: licenseExpiryDate,
      };

      const licenseKeys = [];
      const licenseValues = [];
      let paramIndex = 1;

      for (const [key, value] of Object.entries(licenseFieldsMap)) {
        if (value !== undefined) {
          licenseKeys.push(`"${key}" = $${paramIndex++}`);
          licenseValues.push(value);
        }
      }

      if (licenseKeys.length > 0) {
        licenseValues.push(profileId);
        const licenseQuery = `
            UPDATE ${TABLE.ACCOUNT}
            SET ${licenseKeys.join(", ")}
            WHERE "profileId" = $${paramIndex}
            RETURNING *;
          `;

        await db.query(licenseQuery, licenseValues);
      }

      const updateFields: any = {};

      if (workType) updateFields.workType = workType.trim();
      if (workTypeCategory)
        updateFields.workTypeCategory = workTypeCategory.trim();
      if (nationality) updateFields.nationality = nationality.trim();
      if (operationArea) updateFields.operationArea = operationArea.trim();
      if (licenseDocs.length) updateFields.licenseDocs = licenseDocs;
      if (freelancePermitDocs.length)
        updateFields.freelancePermitDocs = freelancePermitDocs;
      if (tradeLicenseDocs.length)
        updateFields.tradeLicenseDocs = tradeLicenseDocs;
      if (yearOfExperience)
        updateFields.yearOfExperience = yearOfExperience.trim();
      if (otherForFreelancer)
        updateFields.otherForFreelancer = otherForFreelancer.trim();
      if (employerName) updateFields.employerName = employerName.trim();
      if (position) updateFields.position = position.trim();
      if (employedLocation)
        updateFields.employedLocation = employedLocation.trim();
      if (industrySubCategory)
        updateFields.industrySubCategory = industrySubCategory.trim();
      if (specializationMission)
        updateFields.specializationMission = specializationMission.trim();
      if (summary) updateFields.summary = summary.trim();
      if (languages) updateFields.languages = languages;
      if (areaCovered) updateFields.areaCovered = areaCovered;
      if (certifications.length) updateFields.certifications = certifications;
      if (profilePhotos.length) updateFields.profilePhotos = profilePhotos;
      if (employmentLetters.length)
        updateFields.employmentLetters = employmentLetters;
      if (servicesOffered.length)
        updateFields.servicesOffered = servicesOffered;

      const keys = Object.keys(updateFields).filter(
        (key) => updateFields[key] !== undefined
      );
      const values = keys.map((key) => updateFields[key]);
      const setClause = keys
        .map((key, index) => `"${key}" = $${index + 1}`)
        .join(", ");

      let columnNames = keys.map((key) => `"${key}"`).join(", ");
      let placeholders = keys.map((_, index) => `$${index + 1}`).join(", ");

      if (keys.length === 0) {
        return errorResponse(res, "No valid fields to update");
      }

      const result = await db.query(
        `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
          keys.length + 1
        } RETURNING *`,
        [...values, profileId]
      );

      if (!result.rows[0]) {
        columnNames += `, "profile_id"`;
        placeholders += `, $${keys.length + 1}`;
        values.push(profileId);
        const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames})
        VALUES (${placeholders})
        RETURNING *`;
        const insertResult = await db.query(insertQuery, values);
      }

      return response(res, 200, "Agent profile updated successfully");
    } catch (error) {
      console.error("Error updating agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

export const completeAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    // >>> Go herer
    const client = await db.connect();
    await client.query("BEGIN");

    const toArray = (val: any) => (Array.isArray(val) ? val : val ? [val] : []);

    const employmentProof = toArray(req.body?.employmentProof) || [];
    const licenseDocs = toArray(req.body?.licenseDoc);
    const freelancePermitDocs = toArray(req.body?.freelancePermitDoc);
    const emiratesId = toArray(req.body?.emiratesId);
    const visa = toArray(req.body?.visa);
    const passport = toArray(req.body?.passport);

    const profilePhotos = req.body?.profilePhoto || null;
    const profilePhoto = req.body?.profilePhoto ? [req.body.profilePhoto] : [];

    const allImages = [
      ...employmentProof,
      ...licenseDocs,
      ...freelancePermitDocs,
      ...emiratesId,
      ...visa,
      ...passport,
      ...profilePhoto,
    ];

    const form8Data = Array.isArray(req.body.form8)
      ? req.body.form8.map((item: any) => ({
        roleId: item.id || null,
        roletype: item.roletype || null,
        hasLicense: item.hasLicense || null,
        licenseNumber: item.licenseNumber || null,
        licenseAuthority: item.licenseAuthority || null,
        licenseAuthorityOther: item.licenseAuthorityOther || null,
        licenseexpiryDate: item.licenseexpiryDate || null,
        licenseFile: toArray(item.licenseFile) || [],
      }))
      : [];

    try {
      const userId = req.user.id;
      const profileId = Number(userId);
      const loginId = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);

      const {
        first_name,
        middle_name,
        last_name,
        nationality,
        gender,
        phone_number,
        workType,
        company,
        employerName,
        licenseNumber,
        companyEmail,
        companyPhone,
        hasFreelancerPermit,
        freelancerLicenseNumber,
        freelancerLicenseNumberExpiryDate,
        freelancerLicenseAuthority,
        freelancerLicenseAuthorityOther,
        primaryIndustry,
        primaryIndustryOther,
        agentType,
        agentTypeOther,
        hasLicense,
        licenseAuthority,
        licenseAuthorityOther,
        termsAgree,
        accuracyConfirm,
        communicationConsent,
        referralId,
        employmentProofExpiry,
        emiratesIdExpiry,
        visaExpiry,
        passportExpiry,
        location,
      } = req.body;

      const licenseExpiryDate = req.body.licenseExpiryDate
        ? req.body.licenseExpiryDate
        : null;

      const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const existingAgent = agentProfile.rows[0];

      if (!existingAgent) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile does not exist");
      }

      const updateFields: any = {};

      if (workType) updateFields.workType = workType.trim();
      if (nationality) updateFields.nationality = nationality.trim();
      if (gender) updateFields.gender = gender.trim();
      if (licenseDocs.length != 0) updateFields.licenseDocs = licenseDocs;
      if (freelancePermitDocs.length != 0)
        updateFields.freelancePermitDocs = freelancePermitDocs;
      if (visa.length != 0) updateFields.visa = visa;
      if (passport.length != 0) updateFields.passport = passport;
      if (employerName) updateFields.employerName = employerName.trim();
      if (employmentProof.length != 0)
        updateFields.employmentProof = employmentProof;
      if (companyEmail) updateFields.companyEmail = companyEmail.trim();
      if (companyPhone) updateFields.companyPhone = companyPhone.trim();
      if (hasFreelancerPermit)
        updateFields.hasFreelancerPermit =
          hasFreelancerPermit === "yes" ? true : false;
      if (freelancerLicenseNumber)
        updateFields.freelancerLicenseNumber = freelancerLicenseNumber.trim();
      if (freelancerLicenseNumberExpiryDate)
        updateFields.freelancerLicenseNumberExpiryDate =
          freelancerLicenseNumberExpiryDate.trim();
      if (freelancerLicenseAuthority)
        updateFields.freelancerLicenseAuthority =
          freelancerLicenseAuthority.trim();
      if (emiratesId.length) updateFields.emiratesId = emiratesId;
      if (freelancerLicenseAuthorityOther)
        updateFields.freelancerLicenseAuthorityOther =
          freelancerLicenseAuthorityOther.trim();
      if (agentType)
        updateFields.agentRole = agentTypeOther.trim()
          ? agentTypeOther.trim()
          : agentType;
      if (hasLicense)
        updateFields.hasLicense = hasLicense === "yes" ? true : false;
      if (licenseAuthority)
        updateFields.licenseAuthority = licenseAuthorityOther.trim()
          ? licenseAuthorityOther.trim()
          : licenseAuthority.trim();
      if (termsAgree)
        updateFields.termsAgree = termsAgree === "yes" ? true : false;
      if (accuracyConfirm)
        updateFields.accuracyConfirm = accuracyConfirm === "yes" ? true : false;
      if (communicationConsent)
        updateFields.communicationConsent =
          communicationConsent === "yes" ? true : false;

      if (employmentProofExpiry)
        updateFields.employmentProofExpiry = employmentProofExpiry.trim();

      if (emiratesIdExpiry)
        updateFields.emiratesIdExpiry = emiratesIdExpiry.trim();

      if (visaExpiry) updateFields.visaExpiry = visaExpiry.trim();

      if (passportExpiry) updateFields.passportExpiry = passportExpiry.trim();

      if (primaryIndustry) {
        if (Array.isArray(primaryIndustry)) {
          updateFields.industryMission = primaryIndustry.join(",");
        } else {
          updateFields.industryMission = primaryIndustry;
        }
      }

      if (primaryIndustryOther) {
        updateFields.industryMissionOther = primaryIndustryOther.trim();
      }

      if (agentType) {
        if (Array.isArray(agentType)) {
          updateFields.industrySubCategory = agentType.join(",");
        } else {
          updateFields.industrySubCategory = agentType;
        }
      }

      if (agentTypeOther) {
        updateFields.industrySubCategoryOther = agentTypeOther.trim();
      }




      let primaryIndustryIds = Array.isArray(primaryIndustry)  ? primaryIndustry : (primaryIndustry || "").split(",").filter((id: string) => id.trim() !== "");


      // For primary industries
      const primaryIndustryOther2 = primaryIndustryOther.split(",");
      if (
        primaryIndustryIds.includes("25") &&
        primaryIndustryOther2?.length > 0
      ) {
        for (const industry of primaryIndustryOther2) {
          const trimmedIndustry = industry.trim();
          if (trimmedIndustry !== "") {

            // Check if exists first
            const existing = await client.query(
              `SELECT * FROM list.services 
                WHERE name = $1`,
              [trimmedIndustry]
            );
            // console.log("existing", existing);

            if (existing.rows.length === 0) {
              const result = await client.query(
                `INSERT INTO list.services (name, description, "parentId", "typeId", "statusId", "createdBy")
                  VALUES ($1, $2, $3, $4, $5, $6)
                  RETURNING id`,
                [trimmedIndustry, trimmedIndustry, null, 2, 2, 1]
              );
              // console.log("result", result);

              // newPrimaryIndustryId = result.rows[0].id;
            } else {
              // newPrimaryIndustryId = existing.rows[0].id;
            }
          }
        }
      }





      let agentTypeIds = Array.isArray(agentType)
        ? agentType
        : (agentType || "").split(",");
      let newAgentTypes = [];

      // For agent types (with parentId = 25)
      if (agentTypeIds.includes("other") && agentTypeOther?.length > 0) {
        const parentId = 25;
        const agentTypeOtherArray  = agentTypeOther?.split(",")
        for (const agentType of agentTypeOtherArray) {
          const trimmedAgentType = agentType.trim();
          if (trimmedAgentType !== "") {
            // Check if service already exists
            const existingService = await client.query(
              `SELECT * FROM list.services 
              WHERE name = $1 `,
              [trimmedAgentType]
            );

            let serviceId: number;

            if (existingService.rows.length > 0) {
              // Use existing ID
              serviceId = existingService.rows[0].id;
            } else {
              // Insert new service
              const insertResult = await client.query(
                `INSERT INTO list.services (
                  name, 
                  description, 
                  "parentId", 
                  "typeId", 
                  "statusId", 
                  "createdBy"
                ) VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING id`,
                [trimmedAgentType, trimmedAgentType, parentId, 2, 2, 1]
              );
              serviceId = insertResult.rows[0].id;
            }

            newAgentTypes.push({
              name: trimmedAgentType,
              id: serviceId,
            });

            // Add to agentTypeIds for later processing
            agentTypeIds.push(serviceId.toString());
          }
        }
      }
      console.log("primaryIndustryOther is string" ,newAgentTypes)
      const keys = Object.keys(updateFields);
      if (keys.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "No valid fields to update");
      }

      if (primaryIndustryIds.length > 0) {
        for (const mission of primaryIndustryIds) {
          const missionId = Number(mission); // Convert to integer
          if (!isNaN(missionId)) {
            let newServiceId = missionId;
            if (newServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "statusId", "createdBy") VALUES ($1, $2, $3, $4)`,
                [profileId, newServiceId, 1, loginId.rows[0].id]
              );
            }
          }
        }
      }

      if (agentTypeIds.length > 0) {
        for (const subCategory of agentTypeIds) {
          const subCategoryId = Number(subCategory); // Convert to integer
          if (!isNaN(subCategoryId)) {
            let newSubServiceId = subCategoryId;
            if (newSubServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "statusId", "createdBy") VALUES ($1, $2, $3, $4)`,
                [profileId, newSubServiceId, 1, loginId.rows[0].id]
              );
            }
          }
        }
      }

      const licenseFieldsMap = {
        licenseNo: licenseNumber,
        licenseExpiredDate: licenseExpiryDate,
        profileId, // include this as it's usually required for inserts
      };

      if (hasLicense === "yes") {
        const licenseKeys = [];
        const licenseValues = [];
        const accountPlaceholders = [];

        let paramIndex = 1;

        for (const [key, value] of Object.entries(licenseFieldsMap)) {
          if (value !== undefined && value != "") {
            licenseKeys.push(`"${key}"`);
            licenseValues.push(value);
            accountPlaceholders.push(`$${paramIndex++}`);
          }
        }

        if (licenseKeys.length > 0) {
          const licenseQuery = `
            INSERT INTO ${TABLE.ACCOUNT} (${licenseKeys.join(", ")})
            VALUES (${accountPlaceholders.join(", ")})
            RETURNING *;
          `;
          await client.query(licenseQuery, licenseValues);
        }
      }

      const statusNames = ["Pending"];
      const pendingStatus = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const statusId = pendingStatus.rows[0].id;

      const query = `
        UPDATE prf.profile
        SET
          "firstName" = $1,
          "middleName" = $2,
          "lastName" = $3,
          "phone" = $4,
          "emiratesId" = $5,
          "statusId" =$6,
          "profileImage" = $7,
          "locationId"= $8
        WHERE id = $9
        RETURNING *;
      `;

      const profileValues = [
        first_name,
        middle_name,
        last_name,
        phone_number,
        emiratesId.length !== 0 ? emiratesId : null,
        statusId,
        profilePhotos,
        location,
        profileId,
      ];

      await client.query(query, profileValues);

      const agencyId = Number(company);
      if (!isNaN(agencyId) && agencyId > 0) {
        // console.log(agencyId, "===> ", profileId);
        const checkQuery = `
          SELECT COUNT(*) AS record_count
          FROM ${TABLE.AGENT}
          WHERE "agencyId" = $1 AND "profileId" = $2;
        `;

        const checkValues = [Number(agencyId), profileId];

        const checkResult = await client.query(checkQuery, checkValues);

        if (parseInt(checkResult.rows[0].record_count, 10) == 0) {
          // Insert into agents table
          const insertQuery = `
              INSERT INTO agents ("agencyId", "profileId", "statusId", "createdBy")
              VALUES ($1, $2, $3, $4)
              RETURNING *;
          `;
          const insertValues = [
            Number(agencyId),
            profileId,
            1,
            req.user?.loginId,
          ];

          const insertResult = await client.query(insertQuery, insertValues);
          // console.log("Inserted successfully:", insertResult.rows[0]);
        }
      }

      const values = keys.map((k) => updateFields[k]);
      const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

      // Try update first
      const result = await client.query(
        `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
          keys.length + 1
        } RETURNING *`,
        [...values, profileId]
      );

      if (!result.rows[0]) {
        // No row updated, insert new
        let columnNames = keys.map((k) => `"${k}"`).join(", ");
        let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
        columnNames += `, "profile_id"`;
        placeholders += `, $${keys.length + 1}`;
        values.push(profileId);
        const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
        await client.query(insertQuery, values);
      }

      await client.query(AUTH.UPDATE_PROFILE_STATUS_COMPLETED, [
        true,
        profileId,
      ]);

      const updatedData = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const updatedAgentData = updatedData.rows[0];

      if (referralId) {
        const referralCheck = await client.query(
          REFERRALS.SELECT_BY_REFERRAL_ID,
          [referralId]
        );
        if (referralCheck.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Invalid referral code.");
        }
        await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
          profileId,
          referralId,
        ]);
      }

      // Remove domain from URLs
      const mediaNames = allImages
        .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
        .filter(Boolean);

      if (mediaNames.length > 0) {
        const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");

        const query = `
          UPDATE prf.images
          SET "isFinal" = true
          WHERE "profileId" = $1
          AND "url" IN (${placeholders});
        `;

        const values = [profileId, ...mediaNames];

        await client.query(query, values);
      }

      if (form8Data?.length > 0) {
        for (const item of form8Data) {
          let {
            roleId,
            roletype,
            hasLicense,
            licenseNumber,
            licenseAuthority,
            licenseAuthorityOther,
            licenseexpiryDate,
            licenseFile,
          } = item;

          // Match roleId from newAgentTypes if roletype exists
          if (roletype && newAgentTypes.length > 0) {
            const matched = newAgentTypes.find(
              (agentType) =>
                agentType.name.toLowerCase() === roletype.toLowerCase().trim()
            );
            if (matched) {
              roleId = matched.id;
            }
          }
          // console.log(roleId)
          // console.log(newAgentTypes)

          if (!roleId || typeof roleId === "string") {
            roleId = 255;
          }

          const roleValues = [
            profileId,
            roleId,
            roletype,
            hasLicense === "yes" ? true : false,
            licenseNumber || null,
            licenseAuthority || null,
            licenseAuthorityOther || null,
            licenseexpiryDate || null,
            licenseFile.length > 0 ? licenseFile : null,
          ];

          await client.query(
            `
            INSERT INTO agentlicenses (
              "agentId", "roleId", "roletype", "hasLicense", "licenseNumber", 
              "licenseAuthority", "licenseAuthorityOther", "licenseexpiryDate", "licenseFile"
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `,
            roleValues
          );

          if (licenseFile && licenseFile.length > 0) {
            interface LicenseFilePlaceholders {
              placeholders: string;
            }

            const licenseFilePlaceholders: LicenseFilePlaceholders = {
              placeholders: licenseFile
                .map((_: string, index: number) => `$${index + 2}`)
                .join(", "),
            };

            const placeholders: string = licenseFilePlaceholders.placeholders;
            const values = [profileId, ...licenseFile];

            const updateQuery = `
              UPDATE prf.images
              SET "isFinal" = true
              WHERE "profileId" = $1
              AND "url" IN (${placeholders});
            `;

            await client.query(updateQuery, values);
          }
        }
      }




      await client.query("COMMIT");
      await sendApplicationSubmittedEmail(
        updatedAgentData.firstName + " " + updatedAgentData.lastName,
        updatedAgentData.email,
        res
      );
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error updating agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

export const completeAgentProfileV2 = asyncHandler(async (req: Request, res: Response) => {
  try {
    const response = await AgentService.processIndividualVerification(req.body, req.user.id);
    if (!response.success) {
      return errorCatchResponse(
        res,
        response.message
      );
    } else {
      const data = response.data;
      await sendApplicationSubmittedEmail(
        data.firstName + " " + data.lastName,
        data.email,
        res
      );
    }
  } catch (error) {
    return errorCatchResponse(
      res,
      "An error occurred while updating agent profile"
    );
  }
}
);

export const completeCompanyAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();

    await client.query("BEGIN");
    const toArray = (val: any) => (Array.isArray(val) ? val : val ? [val] : []);
    const profilePhotos = req.body?.profilePhoto;

    const profilePhoto = req.body?.profilePhoto ? [req.body.profilePhoto] : [];

    const tradeLicenseDocs = toArray(req.body?.tradeLicenseDoc);

    const personalIdDoc = toArray(req.body?.personalIdDoc);

    const passportDoc = toArray(req.body?.passportDoc);

    const visaDoc = toArray(req.body?.visaDoc);

    const supportingDocsDoc = toArray(req.body?.supportingDocsDoc);

    // EmiratesId
    const emiratesId = toArray(req.body?.emiratesId);

    const allImages = [
      ...supportingDocsDoc,
      ...visaDoc,
      ...passportDoc,
      ...emiratesId,
      ...personalIdDoc,
      ...tradeLicenseDocs,
      ...profilePhoto,
    ];

    try {
      const form8Data = Array.isArray(req.body.form8)
        ? req.body.form8.map((item: any) => ({
          roleId: item.id || null,
          roletype: item.roletype || null,
          hasLicense: item.hasLicense || null,
          licenseNumber: item.licenseNumber || null,
          licenseIssueDate: item.licenseIssueDate || null,
          licenseexpiryDate: item.licenseexpiryDate || null,
          licenseAuthority: item.licenseAuthority || null,
          licenseAuthorityOther: item.licenseAuthorityOther || null,
          licenseFile: toArray(item.licenseFile) || [],
        }))
        : [];

      const userId = req.user.id;
      const profileId = Number(userId);
      const loginId = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);
      // console.log(form8Data);

      // Destructure all form fields you want to support
      const {
        licenseNumber,
        licenseExpiryDate,
        licenseIssueDate,
        position,
        positionOther,
        first_name,
        middle_name,
        last_name,
        phone_number,
        comapnyName,
        companyEmail,
        companyPhone,
        issuingAuthority,
        inviteAgents,
        operationArea,
        industryMission,
        industryMissionOther,
        industrySubCategory,
        industrySubCategoryOther,
        issuingAuthorityOther,
        referralId,
        personalIdDocExpiry,
        passportDocExpiry,
        visaDocExpiry,
        supportingDocsDocExpiry,
        emiratesIdExpiry,
        form9,
        termsAgree,
        accuracyConfirm,
        location,
      } = req.body;

      // Fetch agent profile (confirm existence)
      const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const existingAgent = agentProfile.rows[0];

      if (!existingAgent) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile does not exist");
      }

      // Prepare update fields for agentdetails
      const updateFields: any = {};

      if (operationArea) updateFields.operationArea = operationArea.trim();
      if (tradeLicenseDocs.length)
        updateFields.tradeLicenseDocs = tradeLicenseDocs;
      if (position) updateFields.position = position.trim();
      if (req.body.location) updateFields.nationality = req.body?.location;
      if (positionOther) updateFields.positionOther = positionOther.trim();
      if (personalIdDoc.length) updateFields.personalIdDoc = personalIdDoc;
      if (emiratesId.length) updateFields.emiratesId = emiratesId;

      // Handle primaryIndustry (can be string or array)
      if (industryMission) {
        if (Array.isArray(industryMission)) {
          updateFields.industryMission = industryMission.join(",");
        } else {
          updateFields.industryMission = industryMission;
        }
      }
      // Handle primaryIndustryOther
      if (industryMissionOther) {
        updateFields.industryMissionOther = industryMissionOther.trim();
      }
      // Handle agentType (can be string or array)
      if (industrySubCategory) {
        if (Array.isArray(industrySubCategory)) {
          updateFields.industrySubCategory = industrySubCategory.join(",");
        } else {
          updateFields.industrySubCategory = industrySubCategory;
        }
      }
      // Handle agentTypeOther
      if (industrySubCategoryOther) {
        updateFields.industrySubCategoryOther = industrySubCategoryOther.trim();
      }

      if (emiratesIdExpiry) {
        updateFields.emiratesIdExpiry = emiratesIdExpiry.trim();
      }

      if (visaDocExpiry) {
        updateFields.visaDocExpiry = visaDocExpiry.trim();
      }

      if (passportDocExpiry) {
        updateFields.passportDocExpiry = passportDocExpiry.trim();
      }

      if (personalIdDocExpiry) {
        updateFields.personalIdDocExpiry = personalIdDocExpiry.trim();
      }

      if (supportingDocsDocExpiry) {
        updateFields.supportingDocsDocExpiry = supportingDocsDocExpiry.trim();
      }
      if (termsAgree)
        updateFields.termsAgree = termsAgree === "yes" ? true : false;
      if (accuracyConfirm)
        updateFields.accuracyConfirm = accuracyConfirm === "yes" ? true : false;
      // Generate update/insert SQL
      const keys = Object.keys(updateFields);
      if (keys.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "No valid fields to update");
      }

      const values = keys.map((k) => updateFields[k]);
      const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

      // Try update first
      const result = await client.query(
        `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
          keys.length + 1
        } RETURNING *`,
        [...values, profileId]
      );

      if (!result.rows[0]) {
        // No row updated, insert new
        let columnNames = keys.map((k) => `"${k}"`).join(", ");
        let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
        columnNames += `, "profile_id"`;
        placeholders += `, $${keys.length + 1}`;
        values.push(profileId);
        const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
        await client.query(insertQuery, values);
      }

      const licenseFieldsMap = {
        licenseNo: licenseNumber,
        licenseIssueDate,
        licenseExpiredDate: licenseExpiryDate,
        profileId, // Include this as it's likely required for inserts
      };

      const licenseKeys = [];
      const licenseValues = [];
      const accountPlaceholders = [];

      let paramIndex = 1;

      for (const [key, value] of Object.entries(licenseFieldsMap)) {
        if (value !== undefined && value != "") {
          licenseKeys.push(`"${key}"`);
          licenseValues.push(value);
          accountPlaceholders.push(`$${paramIndex++}`);
        }
      }

      if (licenseKeys.length > 0) {
        const licenseQuery = `
          INSERT INTO ${TABLE.ACCOUNT} (${licenseKeys.join(", ")})
          VALUES (${accountPlaceholders.join(", ")})
          RETURNING *;
        `;
        await client.query(licenseQuery, licenseValues);
      }

      const statusNames = ["Pending"];
      const pendingStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const statusId = pendingStatus.rows[0].id;

      const profileQuery = `
      UPDATE prf.profile
      SET
          "firstName" = $1,
          "middleName" = $2,
          "lastName" = $3,
          "phone" = $4,
          "contactEmail" = $5,
          "emiratesId" = $6,
          "statusId" = $7,
          "profileImage" = $8
      WHERE id = $9
      RETURNING *;
      `;
      const profileValues = [
        first_name,
        middle_name,
        last_name,
        phone_number,
        companyEmail,
        emiratesId.length !== 0 ? emiratesId : null,
        statusId,
        profilePhotos,
        profileId,
      ];
      await client.query(profileQuery, profileValues);

      const name = comapnyName;

      const agencyData = {
        name,
        companyEmail,
        companyPhone,
        issuingAuthority,
        issuingAuthorityOther: issuingAuthorityOther
          ? issuingAuthorityOther.trim()
          : null,
        inviteAgents,
        passportDoc,
        visaDoc,
        supportingDocsDoc,
        profileId,
        statusId: 2,
        createdBy: loginId.rows[0].id, // or req.user.id
      };

      // Clean out undefined fields
      const entries = Object.entries(agencyData).filter(
        ([_, v]) => v !== undefined
      );
      const columns = entries.map(([key]) => `"${key}"`);
      const companyValues = entries.map(([_, value]) => value);
      const placeholders = entries.map(([key], i) =>
        key === "issuingAuthorityOther" ? `$${i + 1}::text` : `$${i + 1}`
      );
      // Final raw query
      const insertQuery = `INSERT INTO ${TABLE.AGENCIES} (${columns.join(", ")}) VALUES (${placeholders.join(", ")}) RETURNING *`;

      const agencyResult = await client.query(insertQuery, companyValues);
      const savedAgencyData = agencyResult.rows[0];

      // --- Refactored logic for handling 'Other' in industryMission and industrySubCategory ---
      // Handle industryMission (Primary Industry)
      let industryMissionIds = Array.isArray(industryMission)
        ? industryMission
        : (industryMission || "").split(",");
      let newPrimaryIndustryId = [];

      let newPrimaryIndustryArray = [...industryMissionIds]; // ✅ Use the actual array of IDs
      if (
        industryMissionIds.includes("25") &&
        industryMissionOther &&
        industryMissionOther.trim() !== ""
      ) {
        const industryMissionArray = industryMissionOther.split(",");
        for (const industry of industryMissionArray) {
          const trimmedIndustry = industry.trim();
          if (trimmedIndustry !== "") {
            const existing = await client.query(
              `SELECT * FROM list.services 
                  WHERE name = $1`,
              [trimmedIndustry]
            );

            if (existing.rows.length === 0) {
              const result = await client.query(
                `INSERT INTO list.services (name, description, "parentId", "typeId", "statusId", "createdBy")
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING *`,
                [trimmedIndustry, trimmedIndustry, null, 2, 2, 1]
              );

              newPrimaryIndustryId.push(result.rows[0].id);
              newPrimaryIndustryArray.push(result.rows[0].id);
            } else {
              newPrimaryIndustryId.push(existing.rows[0].id);
              newPrimaryIndustryArray.push(existing.rows[0].id);
            }
          }
        }
      }

      // Handle industrySubCategory (Subcategory)
      let industrySubCategoryIds = Array.isArray(industrySubCategory)
        ? industrySubCategory
        : (industrySubCategory || "").split(",");

      let otherSubCatgoeryArrayTypes: any[] = [];

      // Now use industryMissionIds and industrySubCategoryIds for further processing (e.g., inserting into prf.services, etc.)
      if (industryMissionIds.length > 0) {
        const totalIndustries = [
          ...industryMissionIds,
          ...newPrimaryIndustryId,
        ];
        for (const mission of totalIndustries) {
          const missionId = Number(mission); // Convert to integer
          if (!isNaN(missionId)) {
            let newServiceId = missionId;
            if (newServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }

      if (industrySubCategoryIds.length > 0) {
        let totalSubCategoryIndustries = [];

        if (otherSubCatgoeryArrayTypes?.length > 0) {
          const othersSubId = otherSubCatgoeryArrayTypes.map((item) => item.id);
          totalSubCategoryIndustries = [
            ...industrySubCategoryIds,
            ...othersSubId,
          ];
        } else {
          totalSubCategoryIndustries = industrySubCategoryIds;
        }

        for (const subCategory of totalSubCategoryIndustries) {
          const subCategoryId = Number(subCategory);
          if (!isNaN(subCategoryId)) {
            const newSubServiceId = subCategoryId;
            if (newSubServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newSubServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }

      await client.query(
        `UPDATE "profile" SET "isProfileCompleted" = $1 WHERE id = $2`,
        [true, profileId]
      );

      if (referralId) {
        const referralCheck = await client.query(
          REFERRALS.SELECT_BY_REFERRAL_ID,
          [referralId]
        );
        if (referralCheck.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Invalid referral code.");
        }
        await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
          profileId,
          referralId,
        ]);
      }

      // Remove domain from URLs
      const mediaNames = allImages
        .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
        .filter(Boolean);

      if (mediaNames.length > 0) {
        const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");

        const query = `
          UPDATE prf.images
          SET "isFinal" = true
          WHERE "profileId" = $1
          AND "url" IN (${placeholders});
        `;

        const values = [profileId, ...mediaNames];

        await client.query(query, values);
      }
      if (form8Data?.length > 0) {
        for (const item of form8Data) {
          let {
            roleId,
            roletype,
            hasLicense,
            licenseNumber,
            licenseAuthority,
            licenseAuthorityOther,
            licenseexpiryDate,
            licenseFile,
            licenseIssueDate,
          } = item;

          // Fallback: if roleId is still null or undefined, assign default 255
          if (!roleId || typeof roleId === "string") {
            roleId = 255;
          }
          // npx knex migrate:make updateagentLicienseTable --knexfile knex/knexFile.ts
          const roleValues = [
            profileId,
            roleId,
            roletype,
            hasLicense === "yes" ? true : false,
            licenseNumber || null,
            licenseAuthority || null,
            licenseAuthorityOther || null,
            licenseexpiryDate || null,
            licenseFile.length > 0 ? licenseFile : null,
            licenseIssueDate,
          ];

          await client.query(
            `
            INSERT INTO agentlicenses (
              "agencyId", "roleId", "roletype", "hasLicense", "licenseNumber", 
              "licenseAuthority", "licenseAuthorityOther", "licenseexpiryDate", "licenseFile","licenseIssueDate"
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9,$10)
          `,
            roleValues
          );

          if (licenseFile && licenseFile.length > 0) {
            interface LicenseFilePlaceholders {
              placeholders: string;
            }

            const licenseFilePlaceholders: LicenseFilePlaceholders = {
              placeholders: licenseFile
                .map((_: string, index: number) => `$${index + 2}`)
                .join(", "),
            };

            const placeholders: string = licenseFilePlaceholders.placeholders;
            const values = [profileId, ...licenseFile];

            const updateQuery = `
              UPDATE prf.images
              SET "isFinal" = true
              WHERE "profileId" = $1
              AND "url" IN (${placeholders});
            `;

            await client.query(updateQuery, values);
          }
        }
      }

      if (form9?.length > 0) {
        const form9Data = JSON.parse(form9);
        for (const item of form9Data) {
          let { id, list, name } = item;

          // Skip if list is empty or not an array
          if (!Array.isArray(list) || list.length === 0) {
            continue;
          }

          const matchedIndustry = newPrimaryIndustryArray.find(
            (industry) => industry === id
          );

          if (matchedIndustry) {
            id = matchedIndustry.id;
          } else {
            id = 255;
          }

          const roleValues = [profileId, id, list, name];

          await client.query(
            `
            INSERT INTO companyrole (
              "companyId", "roleId", "rolesList", "roleName"
            ) VALUES ($1, $2, $3, $4)
          `,
            roleValues
          );
        }
      }

      await client.query("COMMIT"); // Commit transaction

      const updatedData = await db.query(AUTH.SELECT_BY_ID, [profileId]);

      const updatedAgentData = updatedData.rows[0];

      await sendAdminApplicationSubmittedEmail(
        updatedAgentData.firstName,
        updatedAgentData.lastName,
        updatedAgentData.email,
        updatedAgentData.phone,
        updatedAgentData.accountType,
        res
      );

      await sendApplicationSubmittedEmail(
        updatedAgentData.firstName + " " + updatedAgentData.lastName,
        updatedAgentData.email,
        res
      );
    } catch (error) {
      console.log("Error updating agent profile:", error);
      await client.query("ROLLBACK");
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

export const completeCompanyAgentProfileV2 = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const response = await AgentService.processCompanyProfileVerification(req.body, req.user.id);
      if (!response.success) {
        return errorCatchResponse(res, response.message);
      } else {
        const data = response.data;
        if (data) {
          await sendAdminApplicationSubmittedEmail(
            data.name,
            data.name,
            data.email,
            data.phone,
            data.accountType,
            res
          );

          await sendApplicationSubmittedEmail(
            data.name,
            data.email,
            res
          );
        }
      }
    } catch (error) {
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

export const createBasicAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const { workType, nationality, servicesOffered } = req.body;

    try {
      const userId = req.user.id;
      const profileId = Number(userId);
      if (!profileId) {
        return errorResponse(res, "Profile ID is required");
      }

      if (!workType || workType.trim() === "") {
        return errorResponse(res, "Work Type is required");
      }

      if (!nationality || nationality.trim() === "") {
        return errorResponse(res, "Nationality is required");
      }
      if (!servicesOffered || servicesOffered.length === 0) {
        return errorResponse(res, "Atleast one service is required");
      }

      const existingAgent = await db.query(AGENTS.CHECK_EXISTING_AGENT, [
        profileId,
      ]);

      if (existingAgent.rows.length > 0) {
        const existingAgent2 = await db.query(
          AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
          [profileId]
        );
        if (existingAgent2.rows.length > 0) {
          return response(
            res,
            203,
            "You have already created an account. Please update it."
          );
        }
      }

      const existingAccount = await db.query(AGENTS.CHECK_EXISTING_ACCOUNT, [
        profileId,
      ]);
      if (existingAccount.rows.length > 0) {
        return response(
          res,
          203,
          "You have already created an account. Please update it."
        );
      }

      const CreateQueryToStoreData = await db.query(AGENTS.CREATE_AGENT, [
        profileId,
        3,
        profileId,
      ]);

      if (!CreateQueryToStoreData.rows[0]) {
        return errorResponse(res, "Error creating agent profile");
      }
      const QueryToStoreInAccounts = await db.query(AGENTS.CREATE_ACCOUNT, [
        profileId,
        0,
        profileId,
      ]);

      if (!QueryToStoreInAccounts.rows[0]) {
        return errorResponse(res, "Error creating agent Profile account");
      }
      const query: any = {
        profileId,
        workType,
        servicesOffered,
        nationality,
      };

      const agentId = CreateQueryToStoreData.rows[0].id;

      const queryToInsertAgentDetails = await db.query(
        `INSERT INTO ${TABLE.AGENT_DETAILS} (
          agent_id, profile_id, "workType", "nationality", "servicesOffered"  
        ) VALUES (
          $1, $2, $3, $4, $5  
        ) RETURNING *`,
        [
          Number(agentId),
          profileId,
          query.workType,
          query.nationality,
          query.servicesOffered,
        ]
      );

      if (!queryToInsertAgentDetails.rows[0]) {
        return errorResponse(res, "Error creating agent profile details");
      }
      return response(res, 200, "Agent profile created successfully");
    } catch (error) {
      console.error("Error creating agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while creating agent profile"
      );
    }
  }
);

export const agentLogin = asyncHandler(async (req: Request, res: Response) => {
  try {
    let result;
    let resultUserName;
    const { email, password } = req.body;
    const { success, error } = loginSchema.safeParse({ email, password });
    if (!success) {
      return errorResponse(res, error?.issues[0].message);
    }
    result = await db.query(AUTH.SELECT_BY_EMAIL, [email]);

    if (result.rows.length === 0) {
      resultUserName = await db.query(AUTH.SELECT_BY_USERNAME_FROM_PROFILE, [
        email,
      ]);

      if (resultUserName.rows.length === 0) {
        return errorResponse(res, "Invalid username or email.");
      } else {
        result = await db.query(AUTH.SELECT_BY_ID, [
          resultUserName?.rows[0].profileId,
        ]);
      }
    }

    if (result.rows.length > 0) {
      const user = result.rows[0];

      const loginResult = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        user.id,
      ]);
      if (loginResult.rows.length > 0) {
        const login = loginResult.rows[0];

        const userRole = await db.query(AUTH.SELECT_LOGIN_ROLE_BY_ID, [
          login?.id,
        ]);

        if (userRole.rows.length > 0) {
          if (
            userRole.rows[0].name != "agent" &&
            userRole.rows[0].name != "agency"
          ) {
            errorResponse(
              res,
              "You are not authorized to access this resource."
            );
            return;
          }
          login.role = userRole.rows[0].name;
        } else {
          errorResponse(res, "You are not authorized to access this resource.");
          return;
        }

        const userStatus = await db.query(AUTH.SELECT_STATUS_BY_ID, [
          user.statusId,
        ]);

        if (userStatus.rows.length > 0) {
          const status = userStatus.rows[0].name;
          if (status == "Suspended" || status == "Deactivated") {
            return errorResponse(
              res,
              "Your account has been restricted. Please contact support for assistance."
            );
          }
        }

        if (login.passwordHash) {
          if (await bcrypt.compare(password, login.passwordHash)) {
            if (login.isActivated) {
              const token = getSignedJwt(login.id, email);
              res.cookie("authToken", token, {
                httpOnly: true,
                secure: process.env.NODE_ENV === "production",
                sameSite: "strict",
                maxAge:
                  (Number(process.env.COOKIE_EXPIRY) || 7) *
                  24 *
                  60 *
                  60 *
                  1000,
              });
              await upsertToken(login.id, token);

              const result = await db.query(AUTH.UPDATE_INTO_LOGIN_LAST_LOGIN, [login.id]);
              if (result.rows.length > 0) {
                console.log("lastLogin updated successfully");
              } else {
                console.log("Failed to update lastLogin");
              }

              delete login.passwordHash;
              user.user_id = user.id;
              delete user.id;
              login.login_id = login.id;
              delete login.id;

              if (user.profileImage) {
                user.profileImage = baseUrl + user.profileImage;
              }

              const existingAgent2 = await db.query(
                AGENTS.CHECK_EXISTING_ACCOUNT_DETAILS,
                [user.user_id]
              );
              let agentProfile = "user";
              if (existingAgent2.rows.length > 0) {
                agentProfile = "agent";
              }
              const data = { ...user, ...login, agentProfile };

              return responseData(res, 200, "Login successful", data);
            } else {
              return errorResponse(
                res,
                "Your account is not activated. Please check your email for the activation link."
              );
            }
          } else {
            return errorResponse(res, "Invalid email or password");
          }
        } else {
          return errorResponse(res, "You have not set password yet.");
        }
      } else {
        return errorResponse(res, "Invalid credentials. Please try again.");
      }
    } else {
      return errorResponse(res, "Invalid credentials. Please try again.");
    }
  } catch (error) {
    console.error("Error logging in user:", error);
    return response(res, 500, "Failed to login user");
  }
});

export const updateProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const profileId = req.user.id;
    const {
      firstName,
      phone,
      location,
      address,
      shortDescription,
      description,
      experience,
      languages,
      industry,
      typeId,
      association,
      associated_company_name,
      contactEmail,
      contactNumber,
      whatsappContact,
    } = req.body;

    try {
      const query = `
      UPDATE prf.profile
      SET
        "firstName" = $1,
        "phone" = $2,
        "locationId" = $3,
        address = $4,
        "shortDescription" = $5,
        description = $6,
        experience = $7,
        languages = $8,
        industry = $9,
        "typeId" = $10,
        association = $11,
        "cardHolderName" = $12,
        "contactEmail" = $13,
        "whatsappContact" = $14,
        "contactNumber" = $15,
        "modifiedOn" = CURRENT_TIMESTAMP
      WHERE id = $16
      RETURNING *;
    `;

      const values = [
        firstName,
        phone,
        location,
        address,
        shortDescription,
        description,
        experience,
        languages,
        industry,
        typeId,
        association === "Yes" || association === true,
        associated_company_name,
        contactEmail,
        whatsappContact,
        contactNumber,
        profileId,
      ];

      const { rows } = await db.query(query, values);

      if (!rows.length) {
        return errorResponse(res, "Profile not found");
      }

      return response(res, 200, "Profile updated successfully");
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  const profileId = req.user.id;

  try {
    const { rows } = await db.query(AUTH.SELECT_BY_ID, [profileId]);

    if (!rows.length) {
      return errorResponse(res, "Profile not found");
    }

    return responseData(res, 200, "Profile retrieved successfully", rows[0]);
  } catch (err) {
    console.error("Error updating profile:", err);

    return errorCatchResponse(res, "Something went wrong");
  }
});

export const getCompanyApplicationByProfileId = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const profileId = req.user.id;
      const companyProfileApplication : CompanyProfileDto = await AgentService.getCompanyApplicationByProfileId(profileId);
      return responseData(
          res,
          200,
          "Data retrieved successfully",
          companyProfileApplication
        );
    } catch (error) {
      console.error("Error occured while getting company application:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getApplicationProfileByProfileId = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const { rows } = await db.query(AUTH.SELECT_BY_ID, [userId]);

      if (!rows.length) {
        return errorResponse(res, "Profile data not found");
      }

      if (rows[0].accountType === "Individual") {
        const individualAgentData = await db.query(
          AGENTS.GET_INDIVIDUAL_AGENYT_DETAILS_BY_ID,
          [userId]
        );

        if (!individualAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const individualAgent = individualAgentData.rows[0];
        const agentlicenses = await db.query(
          `SELECT * FROM agentlicenses WHERE "agentId" = $1 OR "agencyId" = $1`,
          [userId]
        );
        const companyrole = await db.query(
          `SELECT * FROM companyrole  WHERE "companyId" = $1`,
          [userId]
        );


        delete individualAgent.passwordHash;

        return responseData(
          res,
          200,
          "Data retrieved successfully",
          {
            ...individualAgent,
            agentlicenses: agentlicenses.rows,
            companyrole: companyrole.rows,
          }
        );
      } else if (rows[0].accountType === "Company/Agency/PropertyDeveloper") {
        const companyAgentData = await db.query(
          AGENTS.GET_COMPANY_AGENT_DETAILS_BY_ID,
          [userId]
        );

        if (!companyAgentData.rows.length) {
          return errorResponse(res, "No record found");
        }

        const companyAgent = companyAgentData.rows[0];


        delete companyAgent.passwordHash;
        const agentlicenses = await db.query(
          `SELECT * FROM agentlicenses WHERE "agentId" = $1 OR "agencyId" = $1`,
          [userId]
        );
        const companyrole = await db.query(
          `SELECT * FROM companyrole  WHERE "companyId" = $1`,
          [userId]
        );

        return responseData(
          res,
          200,
          "Data retrieved successfully",
          {
            ...companyAgent,
            agentlicenses: agentlicenses.rows,
            companyrole: companyrole.rows,
          }
        );
      }
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const getIndividualApplicationByProfileId = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const profileId = req.user.id;
      const individualApplication : IndividualCompleteProfileDto = await AgentService.getIndividualApplicationByProfileId(profileId);
      return responseData(
          res,
          200,
          "Data retrieved successfully",
          individualApplication
        );
    } catch (error) {
      console.error("Error occured while getting individual application:", error);
      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const updateApplicationTempData = asyncHandler(
  async (req: Request, res: Response) => {
    try {
      const userId = req.user.id;
      const { stepData } = req.body;

      const { rows } = await db.query(AUTH.SELECT_BY_ID, [userId]);

      if (!rows.length) {
        return errorResponse(res, "Profile data not found");
      }

      const profielData = await db.query(AUTH.UPDATE_TEMP_DATA, [
        stepData,
        userId,
      ]);
      const updatedProfile = profielData.rows[0];
      return responseData(
        res,
        200,
        "Temp data updated successfully",
        updatedProfile
      );
    } catch (err) {
      console.error("Error updating profile:", err);

      return errorCatchResponse(res, "Something went wrong");
    }
  }
);

export const updateAgentApplicationProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();
    await client.query("BEGIN");

    const toArray = (val: any) => (Array.isArray(val) ? val : val ? [val] : []);

    const employmentProof = toArray(req.body?.employmentProof) || [];
    const licenseDocs = toArray(req.body?.licenseDoc);
    const freelancePermitDocs = toArray(req.body?.freelancePermitDoc);
    const emiratesId = toArray(req.body?.emiratesId);
    const visa = toArray(req.body?.visa);
    const passport = toArray(req.body?.passport);

    const profilePhotos = req.body?.profilePhoto || null;
    const profilePhoto = req.body?.profilePhoto ? [req.body.profilePhoto] : [];

    const allImages = [
      ...employmentProof,
      ...licenseDocs,
      ...freelancePermitDocs,
      ...emiratesId,
      ...visa,
      ...passport,
      ...profilePhoto,
    ];

    const form8Data = Array.isArray(req.body.form8)
      ? req.body.form8.map((item: any) => ({
        roleId: item.id || null,
        roletype: item.roletype || null,
        hasLicense: item.hasLicense || null,
        licenseNumber: item.licenseNumber || null,
        licenseAuthority: item.licenseAuthority || null,
        licenseAuthorityOther: item.licenseAuthorityOther || null,
        licenseexpiryDate: item.licenseexpiryDate || null,
        licenseFile: toArray(item.licenseFile) || [],
      }))
      : [];

    console.log(form8Data)

    try {
      const userId = req.user.id;
      const profileId = Number(userId);
      const loginId = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);

      const {
        first_name,
        middle_name,
        last_name,
        nationality,
        gender,
        phone_number,
        workType,
        company,
        employerName,
        licenseNumber,
        licenseExpiryDate,
        companyEmail,
        companyPhone,
        hasFreelancerPermit,
        freelancerLicenseNumber,
        freelancerLicenseNumberExpiryDate,
        freelancerLicenseAuthority,
        freelancerLicenseAuthorityOther,
        primaryIndustry,
        primaryIndustryOther,
        agentType,
        agentTypeOther,
        hasLicense,
        licenseAuthority,
        licenseAuthorityOther,
        termsAgree,
        accuracyConfirm,
        communicationConsent,
        referralId,
        employmentProofExpiry,
        emiratesIdExpiry,
        visaExpiry,
        passportExpiry,
        location,
      } = req.body;

      const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const existingAgent = agentProfile.rows[0];

      if (!existingAgent) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile does not exist");
      }

      const updateFields: any = {};

      if (workType) updateFields.workType = workType.trim();
      if (nationality) updateFields.nationality = nationality.trim();
      if (gender) updateFields.gender = gender.trim();
      if (licenseDocs.length != 0) updateFields.licenseDocs = licenseDocs;
      if (freelancePermitDocs.length != 0)
        updateFields.freelancePermitDocs = freelancePermitDocs;
      if (visa.length != 0) updateFields.visa = visa;
      if (passport.length != 0) updateFields.passport = passport;
      if (employerName) updateFields.employerName = employerName.trim();
      if (employmentProof.length != 0)
        updateFields.employmentProof = employmentProof;
      if (companyEmail) updateFields.companyEmail = companyEmail.trim();
      if (companyPhone) updateFields.companyPhone = companyPhone.trim();
      if (hasFreelancerPermit)
        updateFields.hasFreelancerPermit =
          hasFreelancerPermit === "yes" ? true : false;
      if (freelancerLicenseNumber)
        updateFields.freelancerLicenseNumber = freelancerLicenseNumber.trim();
      if (freelancerLicenseNumberExpiryDate)
        updateFields.freelancerLicenseNumberExpiryDate =
          freelancerLicenseNumberExpiryDate.trim();
      if (freelancerLicenseAuthority)
        updateFields.freelancerLicenseAuthority =
          freelancerLicenseAuthority.trim();
      if (emiratesId.length) updateFields.emiratesId = emiratesId;
      if (freelancerLicenseAuthorityOther)
        updateFields.freelancerLicenseAuthorityOther =
          freelancerLicenseAuthorityOther.trim();
      if (agentType)
        updateFields.agentRole = agentTypeOther.trim()
          ? agentTypeOther.trim()
          : agentType;
      if (hasLicense)
        updateFields.hasLicense = hasLicense === "yes" ? true : false;
      if (licenseAuthority)
        updateFields.licenseAuthority = licenseAuthorityOther.trim()
          ? licenseAuthorityOther.trim()
          : licenseAuthority.trim();
      if (termsAgree)
        updateFields.termsAgree = termsAgree === "yes" ? true : false;
      if (accuracyConfirm)
        updateFields.accuracyConfirm = accuracyConfirm === "yes" ? true : false;
      if (communicationConsent)
        updateFields.communicationConsent =
          communicationConsent === "yes" ? true : false;

      if (employmentProofExpiry)
        updateFields.employmentProofExpiry = employmentProofExpiry.trim();

      if (emiratesIdExpiry)
        updateFields.emiratesIdExpiry = emiratesIdExpiry.trim();

      if (visaExpiry) updateFields.visaExpiry = visaExpiry.trim();

      if (passportExpiry) updateFields.passportExpiry = passportExpiry.trim();

      if (primaryIndustry) {
        if (Array.isArray(primaryIndustry)) {
          updateFields.industryMission = primaryIndustry.join(",");
        } else {
          updateFields.industryMission = primaryIndustry;
        }
      }

      if (primaryIndustryOther) {
        updateFields.industryMissionOther = primaryIndustryOther.trim();
      }

      if (agentType) {
        if (Array.isArray(agentType)) {
          updateFields.industrySubCategory = agentType.join(",");
        } else {
          updateFields.industrySubCategory = agentType;
        }
      }

      if (agentTypeOther) {
        updateFields.industrySubCategoryOther = agentTypeOther.trim();
      }

      let primaryIndustryIds = Array.isArray(primaryIndustry)  ? primaryIndustry  : (primaryIndustry || "").split(",").filter((id: string) => id.trim() !== "");
      let newPrimaryIndustryId: number | null = null;

      // For primary industries
      const primaryIndustryOther2 = primaryIndustryOther?.split(",") || [];



      if (  primaryIndustryIds.includes("25") &&  primaryIndustryOther2?.length > 0  ) {
        for (const industry of primaryIndustryOther2) {
          const trimmedIndustry = industry.trim();
          if (trimmedIndustry !== "") {
            // Check if exists first
            const existing = await client.query(
              `SELECT * FROM list.services 
                WHERE name = $1`,
              [trimmedIndustry]
            );

            if (existing.rows.length === 0) {
              const result = await client.query(
                `INSERT INTO list.services (name, description, "parentId", "typeId", "statusId", "createdBy")
                  VALUES ($1, $2, $3, $4, $5, $6)
                  RETURNING id`,
                [trimmedIndustry, trimmedIndustry, null, 2, 2, 1]
              );
            }
          }
        }
      }

      let agentTypeIds = Array.isArray(agentType)
        ? agentType
        : (agentType || "").split(",");
      let newAgentTypes = [];

      // For agent types (with parentId = 25)
      if (agentTypeIds.includes("other") && agentTypeOther?.length > 0) {
        const parentId = 25;
        const agentTypeOtherArray  = agentTypeOther?.split(",")
        for (const agentType of agentTypeOtherArray) {
          const trimmedAgentType = agentType.trim();
          if (trimmedAgentType !== "") {
            // Check if service already exists
            const existingService = await client.query(
              `SELECT * FROM list.services 
              WHERE name = $1 `,
              [trimmedAgentType]
            );

            let serviceId: number;

            if (existingService.rows.length > 0) {
              // Use existing ID
              serviceId = existingService.rows[0].id;
            } else {
              // Insert new service
              const insertResult = await client.query(
                `INSERT INTO list.services (
                  name, 
                  description, 
                  "parentId", 
                  "typeId", 
                  "statusId", 
                  "createdBy"
                ) VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING id`,
                [trimmedAgentType, trimmedAgentType, parentId, 2, 2, 1]
              );
              serviceId = insertResult.rows[0].id;
            }

            newAgentTypes.push({
              name: trimmedAgentType,
              id: serviceId,
            });

            // Add to agentTypeIds for later processing
            agentTypeIds.push(serviceId.toString());
          }
        }
      }

      const keys = Object.keys(updateFields);
      if (keys.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "No valid fields to update");
      }

      // Delete existing services for this profile
      await client.query(`DELETE FROM prf.services WHERE "profileId" = $1`, [
        profileId,
      ]);

      if (primaryIndustryIds.length > 0) {
        for (const mission of primaryIndustryIds) {
          const missionId = Number(mission); // Convert to integer
          if (!isNaN(missionId)) {
            let newServiceId = missionId;
            if (newServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "statusId", "createdBy") VALUES ($1, $2, $3, $4)`,
                [profileId, newServiceId, 1, loginId.rows[0].id]
              );
            }
          }
        }
      }

      if (agentTypeIds.length > 0) {
        for (const subCategory of agentTypeIds) {
          const subCategoryId = Number(subCategory); // Convert to integer
          if (!isNaN(subCategoryId)) {
            let newSubServiceId = subCategoryId;
            if (newSubServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "statusId", "createdBy") VALUES ($1, $2, $3, $4)`,
                [profileId, newSubServiceId, 1, loginId.rows[0].id]
              );
            }
          }
        }
      }

      // Delete existing account records
      await client.query(
        `DELETE FROM ${TABLE.ACCOUNT} WHERE "profileId" = $1`,
        [profileId]
      );

      const licenseFieldsMap = {
        licenseNo: licenseNumber,
        licenseExpiredDate: licenseExpiryDate,
        profileId,
      };

      if (hasLicense === "yes") {
        const licenseKeys = [];
        const licenseValues = [];
        const accountPlaceholders = [];

        let paramIndex = 1;

        for (const [key, value] of Object.entries(licenseFieldsMap)) {
          if (value !== undefined && value != "") {
            licenseKeys.push(`"${key}"`);
            licenseValues.push(value);
            accountPlaceholders.push(`$${paramIndex++}`);
          }
        }

        if (licenseKeys.length > 0) {
          const licenseQuery = `
            INSERT INTO ${TABLE.ACCOUNT} (${licenseKeys.join(", ")})
            VALUES (${accountPlaceholders.join(", ")})
            RETURNING *;
          `;
          await client.query(licenseQuery, licenseValues);
        }
      }

      const statusNames = ["Pending"];
      const pendingStatus = await client.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const statusId = pendingStatus.rows[0].id;

      const query = `
        UPDATE prf.profile
        SET
          "firstName" = $1,
          "middleName" = $2,
          "lastName" = $3,
          "phone" = $4,
          "emiratesId" = $5,
          "statusId" = $6,
          "profileImage" = $7,
          "locationId" = $8
        WHERE id = $9
        RETURNING *;
      `;

      const profileValues = [
        first_name,
        middle_name,
        last_name,
        phone_number,
        emiratesId.length !== 0 ? emiratesId : null,
        statusId,
        profilePhotos,
        location,
        profileId,
      ];

      await client.query(query, profileValues);

      const agencyId = Number(company);
      if (!isNaN(agencyId) && agencyId > 0) {
        const checkQuery = `
          SELECT COUNT(*) AS record_count
          FROM ${TABLE.AGENT}
          WHERE "agencyId" = $1 AND "profileId" = $2;
        `;

        const checkValues = [Number(agencyId), profileId];

        const checkResult = await client.query(checkQuery, checkValues);

        if (parseInt(checkResult.rows[0].record_count, 10) == 0) {
          // Insert into agents table
          const insertQuery = `
              INSERT INTO agents ("agencyId", "profileId", "statusId", "createdBy")
              VALUES ($1, $2, $3, $4)
              RETURNING *;
          `;
          const insertValues = [
            Number(agencyId),
            profileId,
            1,
            req.user?.loginId,
          ];

          const insertResult = await client.query(insertQuery, insertValues);
        }
      }

      const values = keys.map((k) => updateFields[k]);
      const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

      // Try update first
      const result = await client.query(
        `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
          keys.length + 1
        } RETURNING *`,
        [...values, profileId]
      );

      if (!result.rows[0]) {
        // No row updated, insert new
        let columnNames = keys.map((k) => `"${k}"`).join(", ");
        let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
        columnNames += `, "profile_id"`;
        placeholders += `, $${keys.length + 1}`;
        values.push(profileId);
        const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
        await client.query(insertQuery, values);
      }

      await client.query(AUTH.UPDATE_PROFILE_STATUS_COMPLETED, [
        true,
        profileId,
      ]);

      const updatedData = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const updatedAgentData = updatedData.rows[0];

      if (referralId) {
        const referralCheck = await client.query(
          REFERRALS.SELECT_BY_REFERRAL_ID,
          [referralId]
        );
        if (referralCheck.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Invalid referral code.");
        }
        await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
          profileId,
          referralId,
        ]);
      }

      // Remove domain from URLs
      const mediaNames = allImages
        .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
        .filter(Boolean);

      if (mediaNames.length > 0) {
        const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");

        const query = `
          UPDATE prf.images
          SET "isFinal" = true
          WHERE "profileId" = $1
          AND "url" IN (${placeholders});
        `;

        const values = [profileId, ...mediaNames];

        await client.query(query, values);
      }

      // Delete existing agentlicenses for this profile
      await client.query(
        `DELETE FROM agentlicenses WHERE "agentId" = $1`,
        [profileId]
      );

      if (form8Data?.length > 0) {
        for (const item of form8Data) {
          let {
            roleId,
            roletype,
            hasLicense,
            licenseNumber,
            licenseAuthority,
            licenseAuthorityOther,
            licenseexpiryDate,
            licenseFile,
          } = item;

          // Match roleId from newAgentTypes if roletype exists
          if (roletype && newAgentTypes.length > 0) {
            const matched = newAgentTypes.find(
              (agentType) =>
                agentType.name.toLowerCase() === roletype.toLowerCase().trim()
            );
            if (matched) {
              roleId = matched.id;
            }
          }

          if (!roleId || typeof roleId === "string") {
            roleId = 255;
          }

          const roleValues = [
            profileId,
            roleId,
            roletype,
            hasLicense === "yes" ? true : false,
            licenseNumber || null,
            licenseAuthority || null,
            licenseAuthorityOther || null,
            licenseexpiryDate || null,
            licenseFile.length > 0 ? licenseFile : null,
          ];

          await client.query(
            `
            INSERT INTO agentlicenses (
              "agentId", "roleId", "roletype", "hasLicense", "licenseNumber", 
              "licenseAuthority", "licenseAuthorityOther", "licenseexpiryDate", "licenseFile"
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          `,
            roleValues
          );

          if (licenseFile && licenseFile.length > 0) {
            interface LicenseFilePlaceholders {
              placeholders: string;
            }

            const licenseFilePlaceholders: LicenseFilePlaceholders = {
              placeholders: licenseFile
                .map((_: string, index: number) => `$${index + 2}`)
                .join(", "),
            };

            const placeholders: string = licenseFilePlaceholders.placeholders;
            const values = [profileId, ...licenseFile];

            const updateQuery = `
              UPDATE prf.images
              SET "isFinal" = true
              WHERE "profileId" = $1
              AND "url" IN (${placeholders});
            `;

            await client.query(updateQuery, values);
          }
        }
      }

      await client.query("COMMIT");
      await sendApplicationSubmittedEmail(
        updatedAgentData.firstName + " " + updatedAgentData.lastName,
        updatedAgentData.email,
        res
      );

      return response(
        res,
        200,
        "Your application has been successfully updated and re-submitted for verification."
      );
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error updating agent profile:", error);
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

export const updateCompanyAgent  = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();
    await client.query("BEGIN");
    const toArray = (val: any) => (Array.isArray(val) ? val : val ? [val] : []);
    const profilePhotos = req.body?.profilePhoto;
    const profilePhoto = req.body?.profilePhoto ? [req.body.profilePhoto] : [];
    const tradeLicenseDocs = toArray(req.body?.tradeLicenseDoc);
    const personalIdDoc = toArray(req.body?.personalIdDoc);
    const passportDoc = toArray(req.body?.passportDoc);
    const visaDoc = toArray(req.body?.visaDoc);
    const supportingDocsDoc = toArray(req.body?.supportingDocsDoc);
    const emiratesId = toArray(req.body?.emiratesId);
    const allImages = [
      ...supportingDocsDoc,
      ...visaDoc,
      ...passportDoc,
      ...emiratesId,
      ...personalIdDoc,
      ...tradeLicenseDocs,
      ...profilePhoto,
    ];
    console.log(allImages)
    try {
      const form8Data = Array.isArray(req.body.form8)
        ? req.body.form8.map((item: any) => ({
          roleId: item.id || null,
          roletype: item.roletype || null,
          hasLicense: item.hasLicense || null,
          licenseNumber: item.licenseNumber || null,
          licenseIssueDate: item.licenseIssueDate || null,
          licenseexpiryDate: item.licenseexpiryDate || null,
          licenseAuthority: item.licenseAuthority || null,
          licenseAuthorityOther: item.licenseAuthorityOther || null,
          licenseFile: toArray(item.licenseFile) || [],
        }))
        : [];
      const form9 = req.body.form9;
      const userId = req.user.id;
      const profileId = Number(userId);
      const loginId = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);
      console.log(form8Data)
      console.log(form9)
      // Destructure all form fields you want to support
      const {
        licenseNumber,
        licenseExpiryDate,
        licenseIssueDate,
        position,
        positionOther,
        first_name,
        middle_name,
        last_name,
        phone_number,
        comapnyName,
        companyEmail,
        companyPhone,
        issuingAuthority,
        inviteAgents,
        operationArea,
        industryMission,
        industryMissionOther,
        industrySubCategory,
        industrySubCategoryOther,
        issuingAuthorityOther,
        referralId,
        personalIdDocExpiry,
        passportDocExpiry,
        visaDocExpiry,
        supportingDocsDocExpiry,
        emiratesIdExpiry,
        termsAgree,
        accuracyConfirm,
        location,
      } = req.body;

      console.log( licenseNumber,
        licenseExpiryDate,
        licenseIssueDate,
        position,
        positionOther,
        first_name,
        middle_name,
        last_name,
        phone_number,
        comapnyName,
        companyEmail,
        companyPhone,
        issuingAuthority,
        inviteAgents,
        operationArea,
        industryMission,
        industryMissionOther,
        industrySubCategory,
        industrySubCategoryOther,
        issuingAuthorityOther,
        referralId,
        personalIdDocExpiry,
        passportDocExpiry,
        visaDocExpiry,
        supportingDocsDocExpiry,
        emiratesIdExpiry,
        termsAgree,
        accuracyConfirm,
        location,)

      // Fetch agent profile (confirm existence)
      const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);
      const existingAgent = agentProfile.rows[0];
      if (!existingAgent) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile does not exist");
      }
      // Prepare update fields for agentdetails
      const updateFields: any = {};
      if (operationArea) updateFields.operationArea = operationArea.trim();
      if (tradeLicenseDocs.length) updateFields.tradeLicenseDocs = tradeLicenseDocs;
      if (position) updateFields.position = position.trim();
      if (req.body.location) updateFields.nationality = req.body?.location;
      if (positionOther) updateFields.positionOther = positionOther.trim();
      if (personalIdDoc.length) updateFields.personalIdDoc = personalIdDoc;
      if (emiratesId.length) updateFields.emiratesId = emiratesId;
      // Handle primaryIndustry (can be string or array)
      if (industryMission) {
        if (Array.isArray(industryMission)) {
          updateFields.industryMission = industryMission.join(",");
        } else {
          updateFields.industryMission = industryMission;
        }
      }
      // Handle primaryIndustryOther
      if (industryMissionOther) {
        updateFields.industryMissionOther = industryMissionOther.trim();
      }
      // Handle agentType (can be string or array)
      if (industrySubCategory) {
        if (Array.isArray(industrySubCategory)) {
          updateFields.industrySubCategory = industrySubCategory.join(",");
        } else {
          updateFields.industrySubCategory = industrySubCategory;
        }
      }
      // Handle agentTypeOther
      if (industrySubCategoryOther) {
        updateFields.industrySubCategoryOther = industrySubCategoryOther.trim();
      }
      if (emiratesIdExpiry) {
        updateFields.emiratesIdExpiry = emiratesIdExpiry.trim();
      }
      if (visaDocExpiry) {
        updateFields.visaDocExpiry = visaDocExpiry.trim();
      }
      if (passportDocExpiry) {
        updateFields.passportDocExpiry = passportDocExpiry.trim();
      }
      if (personalIdDocExpiry) {
        updateFields.personalIdDocExpiry = personalIdDocExpiry.trim();
      }
      if (supportingDocsDocExpiry) {
        updateFields.supportingDocsDocExpiry = supportingDocsDocExpiry.trim();
      }
      if (termsAgree)
        updateFields.termsAgree = termsAgree === "yes" ? true : false;
      if (accuracyConfirm)
        updateFields.accuracyConfirm = accuracyConfirm === "yes" ? true : false;
      // Generate update/insert SQL
      const keys = Object.keys(updateFields);
      if (keys.length > 0) {
        const values = keys.map((k) => updateFields[k]);
        const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");
        // Try update first
        const result = await client.query(
          `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
            keys.length + 1
          } RETURNING *`,
          [...values, profileId]
        );
        if (!result.rows[0]) {
          // No row updated, insert new
          let columnNames = keys.map((k) => `"${k}"`).join(", ");
          let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
          columnNames += `, "profile_id"`;
          placeholders += `, $${keys.length + 1}`;
          values.push(profileId);
          const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
          await client.query(insertQuery, values);
        }
      }
      // --- ACCOUNT update/insert ---
      const licenseFieldsMap = {
        licenseNo: licenseNumber,
        licenseIssueDate,
        licenseExpiredDate: licenseExpiryDate,
        profileId, // Include this as it's likely required for inserts
      };
      const licenseKeys = [];
      const licenseValues = [];
      const accountPlaceholders = [];
      let paramIndex = 1;
      for (const [key, value] of Object.entries(licenseFieldsMap)) {
        if (value !== undefined && value != "") {
          licenseKeys.push(`"${key}"`);
          licenseValues.push(value);
          accountPlaceholders.push(`$${paramIndex++}`);
        }
      }
      // Delete existing account (if any)
      await client.query(
        `DELETE FROM ${TABLE.ACCOUNT} WHERE "profileId" = $1`,
        [profileId]
      );
      if (licenseKeys.length > 0) {
        const licenseQuery = `
          INSERT INTO ${TABLE.ACCOUNT} (${licenseKeys.join(", ")})
          VALUES (${accountPlaceholders.join(", ")})
          RETURNING *;
        `;
        await client.query(licenseQuery, licenseValues);
      }
      // --- prf.profile update ---
      const statusNames = ["Pending"];
      const pendingStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );
      const statusId = pendingStatus.rows[0].id;
      const profileQuery = `
      UPDATE prf.profile
      SET
          "firstName" = $1,
          "middleName" = $2,
          "lastName" = $3,
          "phone" = $4,
          "contactEmail" = $5,
          "emiratesId" = $6,
          "statusId" = $7,
          "profileImage" = $8
      WHERE id = $9
      RETURNING *;
      `;
      const profileValues = [
        first_name,
        middle_name,
        last_name,
        phone_number,
        companyEmail,
        emiratesId.length !== 0 ? emiratesId : null,
        statusId,
        profilePhotos,
        profileId,
      ];
      await client.query(profileQuery, profileValues);
      // --- AGENCIES update/insert ---
      const name = comapnyName;
      // 1. Check if agency already exists for the profile
      const existingAgencyResult = await client.query(
        `SELECT id FROM ${TABLE.AGENCIES} WHERE "profileId" = $1`,
        [profileId]
      );
      const existingAgency = existingAgencyResult.rows[0];
      let agencyResult = null;
      let agencyId = null;
      let agencyData = {
        name,
        companyEmail,
        companyPhone,
        issuingAuthority,
        issuingAuthorityOther: issuingAuthorityOther
          ? issuingAuthorityOther.trim()
          : null,
        inviteAgents,
        passportDoc,
        visaDoc,
        supportingDocsDoc,
        profileId,
        statusId: 2,
        createdBy: loginId.rows[0].id,
      };
      // Clean out undefined fields
      const entries = Object.entries(agencyData).filter(
        ([_, v]) => v !== undefined
      );
      const columns = entries.map(([key]) => `"${key}"`);
      const companyValues = entries.map(([_, value]) => value);
      const placeholders = entries.map(([key], i) => {
        if (key === "issuingAuthorityOther") return `$${i + 1}::text`;
        if (key === "passportDoc") return `$${i + 1}::text[]`;
        if (key === "visaDoc") return `$${i + 1}::text[]`;
        if (key === "supportingDocsDoc") return `$${i + 1}::text[]`;
        if (key === "companyPhone") return `$${i + 1}::text`;
        if (key === "inviteAgents") return `$${i + 1}::text`;
        return `$${i + 1}`;
      });
      // Final raw query
      const insertQuery = `INSERT INTO ${TABLE.AGENCIES} (${columns.join(", ")}) VALUES (${placeholders.join(", ")}) RETURNING *`;

      if (existingAgency) {
        // UPDATE
        const setClause = columns
          .filter((col) => col !== `"profileId"`) // don't update profileId
          .map((col, i) =>
            col === '"issuingAuthorityOther"'
              ? `${col} = $${i + 1}::text`
              : `${col} = $${i + 1}`
          )
          .join(", ");
        agencyResult = await client.query(
          `UPDATE ${TABLE.AGENCIES} SET ${setClause} WHERE "profileId" = $${
            companyValues.length + 1
          } RETURNING *`,
          [...companyValues, profileId]
        );
      } else {
        // INSERT
        agencyResult = await client.query(
          insertQuery,
          companyValues
        );
      }
      const savedAgencyData = agencyResult.rows[0];
      agencyId = savedAgencyData.id;
      // --- Services/Industries ---
      // Handle 'Other' for industryMission
      let industryMissionIds = Array.isArray(industryMission)
        ? industryMission
        : (industryMission || "").split(",");
      let newPrimaryIndustryId = [];
      let newPrimaryIndustryArray = [...industryMissionIds];
      if (
        industryMissionIds.includes("25") &&
        industryMissionOther &&
        industryMissionOther.trim() !== ""
      ) {
        const industryMissionArray = industryMissionOther.split(",");
        for (const industry of industryMissionArray) {
          const trimmedIndustry = industry.trim();
          if (trimmedIndustry !== "") {
            const existing = await client.query(
              `SELECT * FROM list.services 
                  WHERE name = $1`,
              [trimmedIndustry]
            );

            if (existing.rows.length === 0) {
              const result = await client.query(
                `INSERT INTO list.services (name, description, "parentId", "typeId", "statusId", "createdBy")
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING *`,
                [trimmedIndustry, trimmedIndustry, null, 2, 2, 1]
              );

              newPrimaryIndustryId.push(result.rows[0].id);
              newPrimaryIndustryArray.push(result.rows[0].id);
            } else {
              newPrimaryIndustryId.push(existing.rows[0].id);
              newPrimaryIndustryArray.push(existing.rows[0].id);
            }
          }
        }
      }
      // Handle industrySubCategory (Subcategory)
      let industrySubCategoryIds = Array.isArray(industrySubCategory)
        ? industrySubCategory
        : (industrySubCategory || "").split(",");
      let otherSubCatgoeryArrayTypes: any[] = [];
      // Now use industryMissionIds and industrySubCategoryIds for further processing (e.g., inserting into prf.services, etc.)
      if (industryMissionIds.length > 0) {
        const totalIndustries = [
          ...industryMissionIds,
          ...newPrimaryIndustryId,
        ];
        for (const mission of totalIndustries) {
          const missionId = Number(mission); // Convert to integer
          if (!isNaN(missionId)) {
            let newServiceId = missionId;
            if (newServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }
      if (industrySubCategoryIds.length > 0) {
        let totalSubCategoryIndustries = [];
        if (otherSubCatgoeryArrayTypes?.length > 0) {
          const othersSubId = otherSubCatgoeryArrayTypes.map((item) => item.id);
          totalSubCategoryIndustries = [
            ...industrySubCategoryIds,
            ...othersSubId,
          ];
        } else {
          totalSubCategoryIndustries = industrySubCategoryIds;
        }
        for (const subCategory of totalSubCategoryIndustries) {
          const subCategoryId = Number(subCategory);
          if (!isNaN(subCategoryId)) {
            const newSubServiceId = subCategoryId;
            if (newSubServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newSubServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }
      // --- Images ---
      const mediaNames = allImages
        .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
        .filter(Boolean);
      if (mediaNames.length > 0) {
        const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");
        const query = `
          UPDATE prf.images
          SET "isFinal" = true
          WHERE "profileId" = $1
          AND "url" IN (${placeholders});
        `;
        const values = [profileId, ...mediaNames];
        await client.query(query, values);
      }
      // --- agentlicenses ---
      await client.query(
        `DELETE FROM agentlicenses WHERE "agencyId" = $1`,
        [profileId]
      );
      if (form8Data?.length > 0) {
        for (const item of form8Data) {
          let {
            roleId,
            roletype,
            hasLicense,
            licenseNumber,
            licenseAuthority,
            licenseAuthorityOther,
            licenseexpiryDate,
            licenseFile,
            licenseIssueDate,
          } = item;
          if (!roleId || typeof roleId === "string") {
            roleId = 255;
          }
          const roleValues = [
            profileId,
            roleId,
            roletype,
            hasLicense === "yes" ? true : false,
            licenseNumber || null,
            licenseAuthority || null,
            licenseAuthorityOther || null,
            licenseexpiryDate || null,
            licenseFile.length > 0 ? licenseFile : null,
            licenseIssueDate,
          ];
          await client.query(
            `
            INSERT INTO agentlicenses (
              "agencyId", "roleId", "roletype", "hasLicense", "licenseNumber", 
              "licenseAuthority", "licenseAuthorityOther", "licenseexpiryDate", "licenseFile","licenseIssueDate"
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9,$10)
          `,
            roleValues
          );
        }
      }
      // --- companyrole (form9) ---
      await client.query(
        `DELETE FROM companyrole WHERE "companyId" = $1`,
        [profileId]
      );
      if (form9?.length > 0) {
        const form9Data = typeof form9 === "string" ? JSON.parse(form9) : form9;
        for (const item of form9Data) {
          let { id, list, name } = item;
          if (!Array.isArray(list) || list.length === 0) {
            continue;
          }
          const matchedIndustry = newPrimaryIndustryArray.find(
            (industry) => industry === id
          );
          if (matchedIndustry) {
            id = matchedIndustry.id;
          } else {
            id = 255;
          }
          const roleValues = [profileId, id, list, name];
          await client.query(
            `
            INSERT INTO companyrole (
              "companyId", "roleId", "rolesList", "roleName"
            ) VALUES ($1, $2, $3, $4)
          `,
            roleValues
          );
        }
      }
      // --- Profile completed ---
      await client.query(
        `UPDATE "profile" SET "isProfileCompleted" = $1 WHERE id = $2`,
        [true, profileId]
      );
      // --- Referral ---
      if (referralId) {
        const referralCheck = await client.query(
          REFERRALS.SELECT_BY_REFERRAL_ID,
          [referralId]
        );
        if (referralCheck.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Invalid referral code.");
        }
        await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
          profileId,
          referralId,
        ]);
      }
      // --- Send emails ---
      const updatedData = await db.query(AUTH.SELECT_BY_ID, [profileId]);
      const updatedAgentData = updatedData.rows[0];
      await sendAdminApplicationSubmittedEmail(
        updatedAgentData.firstName,
        updatedAgentData.lastName,
        updatedAgentData.email,
        updatedAgentData.phone,
        updatedAgentData.accountType,
        res
      );
      await sendApplicationSubmittedEmail(
        updatedAgentData.firstName + " " + updatedAgentData.lastName,
        updatedAgentData.email,
        res
      );
      await client.query("COMMIT");
      return response(
        res,
        200,
        "Company agent profile updated successfully and re-submitted for verification."
      );
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Update Error:", error);
      return errorCatchResponse(
        res,
        "Error while updating company agent profile"
      );
    }
  }
);

export const inviteTeamMembers = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();
    await client.query("BEGIN");

    try {
      const { inviteedEmail } = req.body;

      console.log(inviteedEmail)
      const profileId = Number(req.user.id);

      if (!inviteedEmail    || inviteedEmail.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Please provide valid email addresses to invite");
      }

      // Clean and validate emails
      const invitedEmails = inviteedEmail
        .split(",") // Split by comma
        .map((email: string) => email.trim())
        .filter((email: string) => email && email.includes("@"));

      if (invitedEmails.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Please provide valid email addresses");
      }

      // Get user details for the invitation
      const userResult = await client.query(AUTH.SELECT_BY_ID, [profileId]);
      if (!userResult.rows[0]) {
        await client.query("ROLLBACK");
        return errorResponse(res, "User not found");
      }

      const user = userResult.rows[0];
      const inviterEmail = user.email;
      console.log(inviterEmail)
      console.log(invitedEmails)

      // Send invitation emails
      for (const email of invitedEmails) {
        try {
          await sendUserInvitationEmail(
            inviterEmail,
            email,
            "agency", // userType - assuming inviting as agent
            `${baseUrl}/register` // registrationLink
          );
        } catch (emailError) {
          console.error(`Error sending invitation email to ${email}:`, emailError);
          // Continue with other emails even if one fails
        }
      }

      await client.query("COMMIT");
      return response(
        res,
        200,
        `Invitation emails sent successfully to ${invitedEmails.length} team members`
      );

    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error inviting team members:", error);
      return errorCatchResponse(
        res,
        "An error occurred while sending invitation emails"
      );
    }
  }
);

export const updateCompanyAgentProfile = asyncHandler(
  async (req: Request, res: Response) => {
    const client = await db.connect();

    await client.query("BEGIN");
    const toArray = (val: any) => (Array.isArray(val) ? val : val ? [val] : []);
    const profilePhotos = req.body?.profilePhoto;

    const profilePhoto = req.body?.profilePhoto ? [req.body.profilePhoto] : [];

    const tradeLicenseDocs = toArray(req.body?.tradeLicenseDoc);

    const personalIdDoc = toArray(req.body?.personalIdDoc);

    const passportDoc = toArray(req.body?.passportDoc);

    const visaDoc = toArray(req.body?.visaDoc);

    const supportingDocsDoc = toArray(req.body?.supportingDocsDoc);

    // EmiratesId
    const emiratesId = toArray(req.body?.emiratesId);

    const allImages = [
      ...supportingDocsDoc,
      ...visaDoc,
      ...passportDoc,
      ...emiratesId,
      ...personalIdDoc,
      ...tradeLicenseDocs,
      ...profilePhoto,
    ];

    try {
      const form8Data = Array.isArray(req.body.form8)
        ? req.body.form8.map((item: any) => ({
          roleId: item.id || null,
          roletype: item.roletype || null,
          hasLicense: item.hasLicense || null,
          licenseNumber: item.licenseNumber || null,
          licenseIssueDate: item.licenseIssueDate || null,
          licenseexpiryDate: item.licenseexpiryDate || null,
          licenseAuthority: item.licenseAuthority || null,
          licenseAuthorityOther: item.licenseAuthorityOther || null,
          licenseFile: toArray(item.licenseFile) || [],
        }))
        : [];

      const userId = req.user.id;
      const profileId = Number(userId);
      const loginId = await client.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);
      // console.log(form8Data);

      // Destructure all form fields you want to support
      const {
        licenseNumber,
        licenseExpiryDate,
        licenseIssueDate,
        position,
        positionOther,
        first_name,
        middle_name,
        last_name,
        phone_number,
        comapnyName,
        companyEmail,
        companyPhone,
        issuingAuthority,
        inviteAgents,
        operationArea,
        industryMission,
        industryMissionOther,
        industrySubCategory,
        industrySubCategoryOther,
        issuingAuthorityOther,
        referralId,
        personalIdDocExpiry,
        passportDocExpiry,
        visaDocExpiry,
        supportingDocsDocExpiry,
        emiratesIdExpiry,
        form9,
        termsAgree,
        accuracyConfirm,
        location,
      } = req.body;

      // Fetch agent profile (confirm existence)
      const agentProfile = await client.query(AUTH.SELECT_BY_ID, [profileId]);

      const existingAgent = agentProfile.rows[0];

      if (!existingAgent) {
        await client.query("ROLLBACK");
        return errorResponse(res, "Agent profile does not exist");
      }

      // Prepare update fields for agentdetails
      const updateFields: any = {};

      if (operationArea) updateFields.operationArea = operationArea.trim();
      if (tradeLicenseDocs.length)
        updateFields.tradeLicenseDocs = tradeLicenseDocs;
      if (position) updateFields.position = position.trim();
      if (req.body.location) updateFields.nationality = req.body?.location;
      if (positionOther) updateFields.positionOther = positionOther.trim();
      if (personalIdDoc.length) updateFields.personalIdDoc = personalIdDoc;
      if (emiratesId.length) updateFields.emiratesId = emiratesId;

      // Handle primaryIndustry (can be string or array)
      if (industryMission) {
        if (Array.isArray(industryMission)) {
          updateFields.industryMission = industryMission.join(",");
        } else {
          updateFields.industryMission = industryMission;
        }
      }
      // Handle primaryIndustryOther
      if (industryMissionOther) {
        updateFields.industryMissionOther = industryMissionOther.trim();
      }
      // Handle agentType (can be string or array)
      if (industrySubCategory) {
        if (Array.isArray(industrySubCategory)) {
          updateFields.industrySubCategory = industrySubCategory.join(",");
        } else {
          updateFields.industrySubCategory = industrySubCategory;
        }
      }
      // Handle agentTypeOther
      if (industrySubCategoryOther) {
        updateFields.industrySubCategoryOther = industrySubCategoryOther.trim();
      }

      if (emiratesIdExpiry) {
        updateFields.emiratesIdExpiry = emiratesIdExpiry.trim();
      }

      if (visaDocExpiry) {
        updateFields.visaDocExpiry = visaDocExpiry.trim();
      }

      if (passportDocExpiry) {
        updateFields.passportDocExpiry = passportDocExpiry.trim();
      }

      if (personalIdDocExpiry) {
        updateFields.personalIdDocExpiry = personalIdDocExpiry.trim();
      }

      if (supportingDocsDocExpiry) {
        updateFields.supportingDocsDocExpiry = supportingDocsDocExpiry.trim();
      }
      if (termsAgree)
        updateFields.termsAgree = termsAgree === "yes" ? true : false;
      if (accuracyConfirm)
        updateFields.accuracyConfirm = accuracyConfirm === "yes" ? true : false;
      // Generate update/insert SQL
      const keys = Object.keys(updateFields);
      if (keys.length === 0) {
        await client.query("ROLLBACK");
        return errorResponse(res, "No valid fields to update");
      }

      const values = keys.map((k) => updateFields[k]);
      const setClause = keys.map((k, i) => `"${k}" = $${i + 1}`).join(", ");

      // Try update first
      const result = await client.query(
        `UPDATE ${TABLE.AGENT_DETAILS} SET ${setClause} WHERE profile_id = $${
          keys.length + 1
        } RETURNING *`,
        [...values, profileId]
      );

      if (!result.rows[0]) {
        // No row updated, insert new
        let columnNames = keys.map((k) => `"${k}"`).join(", ");
        let placeholders = keys.map((_, i) => `$${i + 1}`).join(", ");
        columnNames += `, "profile_id"`;
        placeholders += `, $${keys.length + 1}`;
        values.push(profileId);
        const insertQuery = `INSERT INTO ${TABLE.AGENT_DETAILS} (${columnNames}) VALUES (${placeholders}) RETURNING *`;
        await client.query(insertQuery, values);
      }

      const licenseFieldsMap = {
        licenseNo: licenseNumber,
        licenseIssueDate,
        licenseExpiredDate: licenseExpiryDate,
        profileId, // Include this as it's likely required for inserts
      };

      const licenseKeys = [];
      const licenseValues = [];
      const accountPlaceholders:any = [];

      let paramIndex = 1;

      for (const [key, value] of Object.entries(licenseFieldsMap)) {
        if (value !== undefined && value != "") {
          licenseKeys.push(`"${key}"`);
          licenseValues.push(value);
          accountPlaceholders.push(`$${paramIndex++}`);
        }
      }
      if (licenseKeys.length > 0) {
        const setClause2 = licenseKeys.map((key, i) => `${key} = ${accountPlaceholders[i]}`).join(", ");

        if (setClause2.trim()) {
          const licenseQuery = `
            UPDATE ${TABLE.ACCOUNT}
            SET ${setClause2}
            WHERE "profileId" = $${licenseValues.length + 1}
            RETURNING *;
          `;

          await client.query(licenseQuery, [...licenseValues, profileId]);
        }
      }


      console.log("licenseQuery")

      const statusNames = ["Pending"];
      const pendingStatus :any= await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );

      const statusId = pendingStatus.rows[0].id;

      const profileQuery = `
      UPDATE prf.profile
      SET
          "firstName" = $1,
          "middleName" = $2,
          "lastName" = $3,
          "phone" = $4,
          "contactEmail" = $5,
          "emiratesId" = $6,
          "statusId" = $7,
          "profileImage" = $8
      WHERE id = $9
      RETURNING *;
      `;
      const profileValues = [
        first_name,
        middle_name,
        last_name,
        phone_number,
        companyEmail,
        emiratesId.length !== 0 ? emiratesId : null,
        statusId,
        profilePhotos,
        profileId,
      ];
      await client.query(profileQuery, profileValues);

      const name = comapnyName;

      const agencyData = {
        name,
        companyEmail,
        companyPhone,
        issuingAuthority,
        issuingAuthorityOther: issuingAuthorityOther
          ? issuingAuthorityOther.trim()
          : null,
        inviteAgents,
        passportDoc,
        visaDoc,
        supportingDocsDoc,
        profileId,
        statusId: 2,
        createdBy: loginId.rows[0].id, // or req.user.id
      };

      // Clean out undefined fields
      const entries = Object.entries(agencyData).filter(
        ([_, v]) => v !== undefined
      );
      const columns = entries.map(([key]) => `"${key}"`);
      const companyValues = entries.map(([_, value]) => value);
      const placeholders = entries.map(([key], i) =>
        key === "issuingAuthorityOther" ? `$${i + 1}::text` : `$${i + 1}`
      );
      // Final raw query


      const setClause3 = columns.map((col, index) => `${col} = ${placeholders[index]}`).join(", ");

      const updateQuery = `
        UPDATE ${TABLE.AGENCIES}
        SET ${setClause3}
        WHERE "profileId" = $${companyValues.length + 1}
        RETURNING *;
      `;

      console.log("updateQuery", updateQuery);
      console.log("companyValues", [...companyValues, profileId]);

      const agencyResult = await client.query(updateQuery, [...companyValues, profileId]);




      const savedAgencyData = agencyResult.rows[0];

      // --- Refactored logic for handling 'Other' in industryMission and industrySubCategory ---
      // Handle industryMission (Primary Industry)
      let industryMissionIds = Array.isArray(industryMission)
        ? industryMission
        : (industryMission || "").split(",");
      let newPrimaryIndustryId = [];

      let newPrimaryIndustryArray = [...industryMissionIds]; // ✅ Use the actual array of IDs
      if (
        industryMissionIds.includes("25") &&
        industryMissionOther &&
        industryMissionOther.trim() !== ""
      ) {
        const industryMissionArray = industryMissionOther.split(",");
        for (const industry of industryMissionArray) {
          const trimmedIndustry = industry.trim();
          if (trimmedIndustry !== "") {
            const existing = await client.query(
              `SELECT * FROM list.services 
                  WHERE name = $1`,
              [trimmedIndustry]
            );

            if (existing.rows.length === 0) {
              const result = await client.query(
                `INSERT INTO list.services (name, description, "parentId", "typeId", "statusId", "createdBy")
                    VALUES ($1, $2, $3, $4, $5, $6)
                    RETURNING *`,
                [trimmedIndustry, trimmedIndustry, null, 2, 2, 1]
              );

              newPrimaryIndustryId.push(result.rows[0].id);
              newPrimaryIndustryArray.push(result.rows[0].id);
            } else {
              newPrimaryIndustryId.push(existing.rows[0].id);
              newPrimaryIndustryArray.push(existing.rows[0].id);
            }
          }
        }
      }
      console.log("newPrimaryIndustryArray")
      // Handle industrySubCategory (Subcategory)
      let industrySubCategoryIds = Array.isArray(industrySubCategory)
        ? industrySubCategory
        : (industrySubCategory || "").split(",");

      let otherSubCatgoeryArrayTypes: any[] = [];

      // Now use industryMissionIds and industrySubCategoryIds for further processing (e.g., inserting into prf.services, etc.)
      if (industryMissionIds.length > 0) {
        const totalIndustries = [
          ...industryMissionIds,
          ...newPrimaryIndustryId,
        ];
        for (const mission of totalIndustries) {
          const missionId = Number(mission); // Convert to integer
          if (!isNaN(missionId)) {
            let newServiceId = missionId;
            if (newServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }
      console.log("loginId")
      if (industrySubCategoryIds.length > 0) {
        let totalSubCategoryIndustries = [];

        if (otherSubCatgoeryArrayTypes?.length > 0) {
          const othersSubId = otherSubCatgoeryArrayTypes.map((item) => item.id);
          totalSubCategoryIndustries = [
            ...industrySubCategoryIds,
            ...othersSubId,
          ];
        } else {
          totalSubCategoryIndustries = industrySubCategoryIds;
        }

        for (const subCategory of totalSubCategoryIndustries) {
          const subCategoryId = Number(subCategory);
          if (!isNaN(subCategoryId)) {
            const newSubServiceId = subCategoryId;
            if (newSubServiceId) {
              await client.query(
                `INSERT INTO prf.services ("profileId", "serviceId", "agencyId", "statusId", "createdBy") VALUES ($1, $2, $3, $4, $5)`,
                [
                  profileId,
                  newSubServiceId,
                  savedAgencyData.id,
                  2,
                  loginId.rows[0].id,
                ]
              );
            }
          }
        }
      }

      console.log("isProfileCompleted")
      await client.query(
        `UPDATE "profile" SET "isProfileCompleted" = $1 WHERE id = $2`,
        [true, profileId]
      );

      if (referralId) {
        const referralCheck = await client.query(
          REFERRALS.SELECT_BY_REFERRAL_ID,
          [referralId]
        );
        if (referralCheck.rows.length === 0) {
          await client.query("ROLLBACK");
          return errorResponse(res, "Invalid referral code.");
        }
        await client.query(REFERRALS.INSERT_INTO_USER_REFERRALS, [
          profileId,
          referralId,
        ]);
      }

      // Remove domain from URLs
      const mediaNames = allImages
        .map((url) => url?.replace(/^https?:\/\/[^/]+\//, ""))
        .filter(Boolean);

      if (mediaNames.length > 0) {
        const placeholders = mediaNames.map((_, i) => `$${i + 2}`).join(", ");

        const query = `
          UPDATE prf.images
          SET "isFinal" = true
          WHERE "profileId" = $1
          AND "url" IN (${placeholders});
        `;

        const values = [profileId, ...mediaNames];

        await client.query(query, values);
      }

      await client.query(
        `DELETE FROM agentlicenses WHERE "agencyId" = $1`,
        [profileId]
      );
      if (form8Data?.length > 0) {
        for (const item of form8Data) {
          let {
            roleId,
            roletype,
            hasLicense,
            licenseNumber,
            licenseAuthority,
            licenseAuthorityOther,
            licenseexpiryDate,
            licenseFile,
            licenseIssueDate,
          } = item;

          // Fallback: if roleId is still null or undefined, assign default 255
          if (!roleId || typeof roleId === "string") {
            roleId = 255;
          }
          // npx knex migrate:make updateagentLicienseTable --knexfile knex/knexFile.ts
          const roleValues = [
            profileId,
            roleId,
            roletype,
            hasLicense === "yes" ? true : false,
            licenseNumber || null,
            licenseAuthority || null,
            licenseAuthorityOther || null,
            licenseexpiryDate || null,
            licenseFile.length > 0 ? licenseFile : null,
            licenseIssueDate,
          ];

          await client.query(
            `
            INSERT INTO agentlicenses (
              "agencyId", "roleId", "roletype", "hasLicense", "licenseNumber", 
              "licenseAuthority", "licenseAuthorityOther", "licenseexpiryDate", "licenseFile","licenseIssueDate"
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9,$10)
          `,
            roleValues
          );

          if (licenseFile && licenseFile.length > 0) {
            interface LicenseFilePlaceholders {
              placeholders: string;
            }

            const licenseFilePlaceholders: LicenseFilePlaceholders = {
              placeholders: licenseFile
                .map((_: string, index: number) => `$${index + 2}`)
                .join(", "),
            };

            const placeholders: string = licenseFilePlaceholders.placeholders;
            const values = [profileId, ...licenseFile];

            const updateQuery = `
              UPDATE prf.images
              SET "isFinal" = true
              WHERE "profileId" = $1
              AND "url" IN (${placeholders});
            `;

            await client.query(updateQuery, values);
          }
        }
      }
      await client.query(
        `DELETE FROM companyrole WHERE "companyId" = $1`,
        [profileId]
      );
      if (form9?.length > 0) {
        const form9Data = JSON.parse(form9);
        for (const item of form9Data) {
          let { id, list, name } = item;

          // Skip if list is empty or not an array
          if (!Array.isArray(list) || list.length === 0) {
            continue;
          }

          const matchedIndustry = newPrimaryIndustryArray.find(
            (industry) => industry === id
          );

          if (matchedIndustry) {
            id = matchedIndustry.id;
          } else {
            id = 255;
          }

          const roleValues = [profileId, id, list, name];

          await client.query(
            `
            INSERT INTO companyrole (
              "companyId", "roleId", "rolesList", "roleName"
            ) VALUES ($1, $2, $3, $4)
          `,
            roleValues
          );
        }
      }

      await client.query("COMMIT"); // Commit transaction

      const updatedData = await db.query(AUTH.SELECT_BY_ID, [profileId]);

      const updatedAgentData = updatedData.rows[0];

      await sendAdminApplicationSubmittedEmail(
        updatedAgentData.firstName,
        updatedAgentData.lastName,
        updatedAgentData.email,
        updatedAgentData.phone,
        updatedAgentData.accountType,
        res
      );

      await sendApplicationSubmittedEmail(
        updatedAgentData.firstName + " " + updatedAgentData.lastName,
        updatedAgentData.email,
        res
      );
    } catch (error) {
      console.log("Error updating agent profile:", error);
      await client.query("ROLLBACK");
      return errorCatchResponse(
        res,
        "An error occurred while updating agent profile"
      );
    }
  }
);

// GET /api/v1/admin/agents/search?email={email}
export const searchAgentByEmail = asyncHandler(async (req: Request, res: Response) => {
  const { email } = req.query;

  if (!email) {
    return errorResponse(res, "Email parameter is required");
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email as string)) {
    return errorResponse(res, "Invalid email format");
  }

  // Search for agent by email with role in ('agent', 'agency', 'agencyAdmin')
  const result = await db.query(
    `SELECT p.id, CONCAT(p."firstName", ' ', COALESCE(p."middleName", ''), ' ', p."lastName") AS name, p.email 
     FROM prf.profile p
     LEFT JOIN sec.login l ON p.id = l."profileId"
     LEFT JOIN sec.loginrole lr ON l.id = lr."loginId"
     LEFT JOIN sec.roles r ON lr."roleId" = r.id
     WHERE LOWER(p.email) = LOWER($1) AND r.name IN ('agent', 'agency', 'agencyAdmin') LIMIT 1`,
    [email]
  );
 
  if (result.rows.length === 0) {
    return responseData(res, 404, "No registered agent found with this email. You can still create a ticket for an unregistered user.", {
      found: false,
      message: "No registered agent found with this email. You can still create a ticket for an unregistered user."
    });
  }
  return responseData(res, 200, "Agent found", {
    found: true,
    agent: result.rows[0]
  });
});

export const sendInvitationTeamMember = asyncHandler(
  async (req: Request, res: Response) => {

    const { name, email, agencyProfileId } = req.body;
    if (!name || !email || !agencyProfileId) {
      return errorResponse(res, "Missing required fields (name, email, userId).");
    }

    try {
      // 1. Check if userId belongs to an agency user
      const agencyUser = await db.query(
        `SELECT 
           p.id, 
           TRIM(p.email) AS email, 
           TRIM(p."accountType") AS "accountType", 
           p."isProfileCompleted", 
           s.id AS statusId, 
           TRIM(s.name) AS status, 
           TRIM(a.name) AS agency_name
         FROM profile p 
         LEFT JOIN status s ON s.id = p."statusId"
         LEFT JOIN agencies a ON a."profileId" = p.id 
         WHERE p.id = $1 AND p."accountType" = 'Company/Agency/PropertyDeveloper'`,
        [agencyProfileId]
      );

      if (!agencyUser.rows.length) {
        return errorResponse(res, "Inviter is not a valid agency user.");
      }

      const agencyRow = agencyUser.rows[0];

      // if (agencyRow.status !== "Activated") {
      //   return errorResponse(res, "Please ensure your account is verified before inviting team members. If you have already submitted your application, kindly wait for admin approval before proceeding.");
      // }

      const invalidStatuses = ["Pending", "Incomplete", "Rejected"];

      if (invalidStatuses.includes(agencyRow.status)) {
        return errorResponse(
          res,
          "Please ensure your account is verified before inviting team members. If you have already submitted your application, kindly wait for admin approval before proceeding."
        );
      }

      const agencyName = agencyRow.agency_name || "the agency";


      // 2. Check if the email is already invited or exists as a team member for this agency
      const invitedAgentUser = await db.query(
        `SELECT id, email, "accountType"
        FROM profile
        WHERE TRIM(LOWER(email)) = TRIM(LOWER($1))`,
        [email]
      );
      const invitedAgentProfileId =
        invitedAgentUser.rows.length > 0 ? invitedAgentUser.rows[0].id : null;

      // Check if the invited email exists and has the agent role
      const agentCheckResult = await db.query(
        `SELECT p.id as profile_id
               FROM profile p
               JOIN login l ON l."profileId" = p.id
               JOIN loginrole lr ON lr."loginId" = l.id
               JOIN roles r ON lr."roleId" = r.id
               WHERE TRIM(LOWER(p.email)) = TRIM(LOWER($1))
                 AND r.name = 'agent'
               LIMIT 1`,
        [email]
      );

      // If the email exists in profile but does not have the agent role, block the invitation
      if (invitedAgentUser.rows.length > 0 && agentCheckResult.rows.length === 0) {
        return errorResponse(res, "The invited user is not an agent, thus you cannot invite them as a team member to this agency.");
      }

      const existingInvite = await db.query(
        `SELECT * 
        FROM agency_team_invites 
        WHERE TRIM(LOWER(email)) = TRIM(LOWER($1))
        AND agency_id = $2 
        AND status = 'invited'`,
        [email, agencyProfileId]
      );
      if (existingInvite.rows.length) {
        return errorResponse(res, "This user has already been invited.");
      }

      // Check if already a team member
      const alreadyMember = await db.query(
        `SELECT * 
        FROM agent_agency_mapping 
        WHERE agent_id = $1 AND agency_id = $2`,
        [invitedAgentProfileId, agencyProfileId]
      );
      if (alreadyMember.rows.length) {
        return errorResponse(
          res,
          "This email is already a team member's email of the agency."
        );
      }

      // 3. Create invitation record
      const inviteToken = uuidv4();
      await db.query(
        `INSERT INTO agency_team_invites 
        (agency_id, email, agent_profile_id, invite_token, expires_at) 
        VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP + INTERVAL '24 hours')`,
        [agencyProfileId, email, invitedAgentProfileId ?? null, inviteToken]
      );

      // 4. Send invitation email
      const isRegistered = invitedAgentProfileId ? true : false;

      // const baseUrl = isRegistered
      // ? process.env.NEXT_PUBLIC_ACCEPT_INVITE_URL || "https://dashboard-faa.findanyagent.ae/accept-invite"
      // : process.env.NEXT_PUBLIC_DASHBOARD_REGISTRATION_URL || "https://dashboard-faa.findanyagent.ae/registered";

      const baseUrl = isRegistered
        ? "http://localhost:3000/accept-invite"
        : "http://localhost:3000/registered";

      const registrationLink = `${baseUrl}?invite=${inviteToken}`;

      await sendInvitationToTeamMember(
        name,
        email,
        agencyName,
        registrationLink,
        isRegistered
      );

      // 5. Respond
      return response(res, 200, "Invitation sent successfully.");
    } catch (err) {
      console.error("Error sending invitation:", err);
      return errorCatchResponse(res, "An error occurred while sending invitation.");
    }
  }
);

export const acceptInvite = asyncHandler(async (req: Request, res: Response) => {
  const token = req.query.token as string;
  if (!token) {
    return errorResponse(res, "Invitation token is missing from the request.");
  }

  // Look up the invitation by token
  const inviteResult = await db.query(
    `SELECT * FROM agency_team_invites WHERE invite_token = $1 LIMIT 1`,
    [token]
  );
  if (!inviteResult.rows.length) {
    return errorResponse(res, "This invitation link is invalid or does not exist.");
  }
  const invite = inviteResult.rows[0];

  // Check if already accepted or expired
  if (invite.status !== "invited" || (invite.expires_at && new Date(invite.expires_at) < new Date())) {
    return errorResponse(res, "This invitation link is either expired or already accepted.");
  }

  // Mark as accepted
  await db.query(
    `UPDATE agency_team_invites SET status = $1, responded_at = NOW() WHERE id = $2`,
    ["accepted", invite.id]
  );

  // Add to agent_agency_mapping if agent_profile_id exists and not already mapped
  if (invite.agent_profile_id) {
    const mappingResult = await db.query(
      `SELECT * FROM agent_agency_mapping WHERE agency_id = $1 AND agent_id = $2`,
      [invite.agency_id, invite.agent_profile_id]
    );
    if (!mappingResult.rows.length) {
      await db.query(
        `INSERT INTO agent_agency_mapping (agency_id, agent_id) VALUES ($1, $2)`,
        [invite.agency_id, invite.agent_profile_id]
      );
    }
  }

  return response(res, 200, "You have successfully accepted the invitation and joined the agency's team.");
});

export const getInvitedAgentEmail = asyncHandler(async (req: Request, res: Response) => {
  const token = req.query.token as string;
  if (!token) {
    return errorResponse(res, "Invitation token parameter is missing from the request.");
  }

  try {
    // Look up the invitation by token and check if it's valid and not expired
    const inviteResult = await db.query(
      `SELECT email FROM agency_team_invites 
       WHERE invite_token = $1 
       AND status = 'invited' 
       AND expires_at > NOW()
       LIMIT 1`,
      [token]
    );

    if (inviteResult.rows.length === 0) {
      return errorResponse(res, "Invalid or expired invitation token.");
    }

    const email = inviteResult.rows[0].email;

    return responseData(res, 200, "You have successfully accepted the invitation and joined the agency's team.", {email});

  } catch (error) {
    console.error("Error validating invitation token:", error);
    return errorCatchResponse(res, "An error occurred while validating the invitation token");
  }
});

export const getTeamMembers = asyncHandler(async (req: Request, res: Response) => {
  const agencyId = req.query.agency_id;
  if (!agencyId) {
    return errorResponse(res, "agency_id query parameter is required");
  }

  try {
    // Fetch all invites for the agency, join profile for name and status if agent_profile_id is present, and agentdetails for role info
    const invites = await db.query(
      `SELECT ati.id, 
              CASE 
                WHEN p."firstName" IS NULL OR p."middleName" IS NULL OR p."lastName" IS NULL THEN NULL
                ELSE TRIM(CONCAT(p."firstName", ' ', p."middleName", ' ', p."lastName"))
              END AS name, 
              ati.email, 
              ati.status, 
              ati.agent_profile_id, 
              ati.agency_id, 
              ati.invited_at, 
              ati.expires_at, 
              ati.responded_at,
              ad."industrySubCategory",
              ad."industrySubCategoryOther",
              s.name AS verificationStatus
       FROM agency_team_invites ati
       LEFT JOIN profile p ON ati.agent_profile_id = p.id
       LEFT JOIN agentdetails ad ON ad.profile_id = ati.agent_profile_id
       LEFT JOIN look.status s ON p."statusId" = s.id
       WHERE ati.agency_id = $1
       ORDER BY ati.invited_at DESC`,
      [agencyId]
    );

    // Gather all unique service ids to fetch names in one go
    const allServiceIds = new Set();
    invites.rows.forEach(row => {
      if (row.industrySubCategory) {
        row.industrySubCategory.split(',').map((v:string) => v.trim()).forEach((val: string) => {
          if (val && val !== 'other' && !isNaN(Number(val))) {
            allServiceIds.add(Number(val));
          }
        });
      }
    });

    // let serviceNamesMap = {};
    let serviceNamesMap: Record<string, string> = {};

    if (allServiceIds.size > 0) {
      const idsArr = Array.from(allServiceIds);
      const servicesResult = await db.query(
        `SELECT id, name FROM list.services WHERE id = ANY($1)`,
        [idsArr]
      );
      serviceNamesMap = Object.fromEntries(servicesResult.rows.map(r => [String(r.id), r.name]));
    }


    // Build the response
    const teamMembers = invites.rows.map(row => {
      let roles = [];
      if (row.industrySubCategory) {
        const subCats = row.industrySubCategory.split(',').map((v:string) => v.trim()).filter((v:string) => v.length > 0);
        let otherNames = [];
        if (row.industrySubCategoryOther) {
          otherNames = row.industrySubCategoryOther.split(',').map((v:string) => v.trim()).filter((v:string) => v.length > 0);
        }
        let otherIdx = 0;
        for (let i = 0; i < subCats.length && roles.length < 3; i++) {
          const val = subCats[i];
          if (val === 'other') {
            if (otherNames[otherIdx]) {
              roles.push({ id: 'other', name: otherNames[otherIdx] });
              otherIdx++;
            }
          } else if (!isNaN(Number(val)) && serviceNamesMap[val]) {
            roles.push({ id: Number(val), name: serviceNamesMap[val] });
          }
        }
      }
      return {
        id: row.id,
        name: row.name,
        email: row.email,
        role: roles.length > 0 ? roles : null,
        status: row.status,
        subscription: null,
        agent_profile_id: row.agent_profile_id,
        agency_id: row.agency_id,
        invited_at: row.invited_at,
        expires_at: row.expires_at,
        responded_at: row.responded_at,
        verificationStatus: row.verificationstatus
      };
    });

    return responseData(res, 200, "Team members fetched successfully", teamMembers);
  } catch (error) {
    console.error("Error fetching team members:", error);
    return errorCatchResponse(res, "An error occurred while fetching team members");
  }
});

/**
 * DELETE /agency/team/:agency_id/:agent_id
 * Remove a user from an agency team and handle subscription logic.
 */
export const suspendOrUnlinkTeamMember = asyncHandler(async (req: Request, res: Response) => {
  const agencyId = Number(req.params.agency_id);
  const agentId = Number(req.params.agent_id);
  if (!agencyId || !agentId) {
    return errorResponse(res, "agency_id and agent_id are required");
  }
  const client = await db.connect();
  try {
    await client.query("BEGIN");
    // 1. Remove mapping
    await client.query(
      `DELETE FROM agent_agency_mapping WHERE agency_id = $1 AND agent_id = $2`,
      [agencyId, agentId]
    );
    // 2. Set status to 'suspended' in agency_team_invites for this agency/user (if accepted)
    await client.query(
      `UPDATE agency_team_invites SET status = 'suspended' WHERE agency_id = $1 AND agent_profile_id = $2 AND status = 'accepted'`,
      [agencyId, agentId]
    );
    // 3. Find all active subscriptions for this agent
    const { rows: subs } = await client.query(
      `SELECT s.id, s."packageTypeId" FROM list.subscription s WHERE s."profileId" = $1 AND s."statusId" = 1`,
      [agentId]
    );
    for (const sub of subs) {
      // Get package type userType
      const pkgTypeRes = await client.query(
        `SELECT "userType" FROM look.packagetype WHERE id = $1`,
        [sub.packageTypeId]
      );
      const userType = pkgTypeRes.rows[0]?.userType;
      if (userType === 'agency') {
        // Cancel/delete the subscription (using stored procedure)
        await client.query(
          `SELECT * FROM look.sp_subscription($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`,
          [3, sub.id, null, null, null, null, null, null, null, null, null, null, null, null]
        );
      }
      // If userType === 'agent', do nothing (user paid for it)
    }
    await client.query("COMMIT");
    return response(res, 200, "User has been unlinked from the agency, status set to suspended, and relevant subscriptions removed if agency-paid.");
  } catch (err) {
    await client.query("ROLLBACK");
    console.error("Error in suspendOrUnlinkTeamMember:", err);
    return errorCatchResponse(res, "An error occurred while unlinking the user from the agency");
  } finally {
    client.release();
  }
});

export const resetUserPassword = asyncHandler(
  async (req: Request, res: Response) => {
    const { id } = req.params; // User ID from path parameter
    const { createdBy } = req.body;

    // Input validation
    if (!createdBy || isNaN(Number(createdBy))) {
      return responseData(
        res,
        400,
        "Invalid or missing createdBy (admin userId)."
      );
    }

    if (!id || typeof id !== "string") {
      return responseData(res, 400, "Invalid user ID provided.");
    }

    const client = await db.connect(); // Acquire client for transaction

    try {
      await client.query("BEGIN");

      // 1. Validate that the user exists and get email/name
      const userRes = await client.query(
        `SELECT id, "firstName", "middleName", "lastName", email FROM prf.profile WHERE id = $1`,
        [id]
      );

      if (userRes.rowCount === 0) {
        await client.query("ROLLBACK");
        return responseData(res, 404, "User not found.");
      }

      const user = userRes.rows[0];
        const fullName = `${user.firstName || ""} ${user.middleName || ""} ${
            user.lastName || ""
        }`.trim();
      const email = user.email;

      if (!email) {
        await client.query("ROLLBACK");
        return responseData(
          res,
          400,
          "User does not have a valid email address."
        );
      }

      // 2. Generate and hash new password
      const newRandomPassword = generateSecurePassword();
      const salt = await bcrypt.genSalt(10);
      const newPasswordHash = await bcrypt.hash(newRandomPassword, salt);

      // 3. Update sec.login table
      const loginUpdateRes = await client.query(
        `UPDATE sec.login SET "passwordHash" = $1, "passwordResetAt" = NOW() WHERE "profileId" = $2`,
        [newPasswordHash, user.id]
      );

      if (loginUpdateRes.rowCount === 0) {
        await client.query("ROLLBACK");
        return responseData(res, 500, "Failed to update login password.");
      }

      // 3.1 Get loginId from sec.login table using profileId
      const loginIdRes = await client.query(
        `SELECT id FROM sec.login WHERE "profileId" = $1`,
        [user.id]
      );

      if (loginIdRes.rowCount === 0) {
        await client.query("ROLLBACK");
        return responseData(res, 404, "Login record not found for user.");
      }

      const loginId = loginIdRes.rows[0].id;

      // 4. Log password reset event in sec.auditlogs (assuming table and columns exist)
      await client.query(
        `INSERT INTO sec.auditlogs ("userId", action, "timestamp", "createdBy") VALUES ($1, $2, NOW(), $3)`,
        [loginId, "password_reset", createdBy]
      );

      await client.query("COMMIT");

      // 5. Send email with new credentials
      await sendPasswordResetEmail(email, fullName, newRandomPassword);

      return responseData(
        res,
        200,
        "Password reset successfully, credentials emailed to user."
      );
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error resetting user password:", error);
      return responseData(
        res,
        500,
        "Failed to reset user password. Internal Server Error."
      );
    } finally {
      client.release();
    }
  }
);

// Function to generate secure random password
function generateSecurePassword(): string {
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const special = "!@#$%&*?";

  // Ensure at least one character from each required set
  let password = "";
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += special[Math.floor(Math.random() * special.length)];

  // Fill the rest with random characters from all sets
  const allChars = uppercase + lowercase + numbers + special;
  const remainingLength = 8 - password.length;

  for (let i = 0; i < remainingLength; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }

  // Shuffle the password
  return password
    .split("")
    .sort(() => Math.random() - 0.5)
    .join("");
}
