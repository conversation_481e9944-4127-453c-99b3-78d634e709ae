import   { Request, Response } from "express";
import { db } from "../../config/database"; 
import {
  errorCatchResponse,
  errorResponse, 
  responseData,
} from "../../utils/response"; 
import asyncHandler from "../../middleware/trycatch";   
import { TABLE } from "../../utils/database/table";
import slugify  from "slugify"; 

export const blogCreation = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();
  
  try {
    const {
      title,
      content,
      category,
      excerpt,
      featuredImage,
      author,
      statusId, 
      tags,
      featureBlog
    } = req.body;
       const userId =req.user.id
      const profileId = Number(userId);


    await client.query("BEGIN");

    let publishDate = null;
    if (statusId === 22) {  
      publishDate = new Date(); 
    }
    
    console.log("Request body:", req.body);
    const featureBlogValue = featureBlog =="yes" ? true : false;  
    const slug =  slugify(title, {
      lower: true,
      strict: true,
      replacement: '-',
    });
  
    const result = await client.query(
      `INSERT INTO "${TABLE.BLOGS}" 
        ("title", "content", "category", "excerpt", "featuredImage", "author", "statusId", "profileId" , "publishDate" ,"featured" , "slug") 
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8 , $9, $10, $11) 
        RETURNING *`,
      [title, content, category, excerpt, featuredImage, author, statusId, profileId , publishDate,featureBlogValue, slug]
    );

    const blog = result.rows[0];

    if (tags) {
      const arrayTag = String(tags)
        .split(",")
        .map((tag: string) => tag.trim())
        .filter((tag: string) => tag.length > 0);
    
      if (arrayTag.length > 0) {
        const tagInsertQueries = arrayTag.map(tag =>
          client.query(
            `INSERT INTO "${TABLE.BLOGS_TAG}" ("blogId", "tag") VALUES ($1, $2)`,
            [blog.id, tag]
          )
        );
        await Promise.all(tagInsertQueries);
      }
    }
     
    await client.query("COMMIT");
   return responseData(res, 200, "Blog created successfully", blog);
  } catch (error) {
    await client.query("ROLLBACK");
    console.error("Error creating blog:", error);
    responseData(res, 500, "Failed to create blog");
  } finally {
    client.release();
  }
});

export const getAllBlogs = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { rows } = await client.query(`
      SELECT 
        b."id",
        b."title",
        b."category",
        b."excerpt",
        b."featuredImage",
        b."author",
        b."statusId",
        b."created_at",
        b."updated_at",
        b."publishDate",
        b."slug",
        b."readTime",
        b."featured",
        COUNT(v.*) AS "viewsCount",
        COUNT(DISTINCT l."profileId") AS "likesCount"
      FROM "${TABLE.BLOGS}" b
      LEFT JOIN "${TABLE.BLOGS_VIEW}" v ON v."blogId" = b."id"
      LEFT JOIN "${TABLE.BLOGS_LIKE}" l ON l."blogId" = b."id"
      GROUP BY b."id"
      ORDER BY b."created_at" DESC
    `);

    return responseData(res, 200, "Blogs fetched successfully", rows);
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return errorCatchResponse(res, "Failed to fetch blogs");
  } finally {
    client.release();
  }
});

export const getAllPublishedBlogs = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { rows } = await client.query(`
      SELECT 
        b."id",
        b."title",
        b."category",
        b."excerpt",
        b."featuredImage",
        b."author",
        b."statusId",
        b."created_at",
        b."updated_at",
        b."publishDate",
        b."featured",
        b."slug",
        b."readTime",
        COUNT(DISTINCT v."profileId") AS "viewsCount",
        COUNT(DISTINCT l."profileId") AS "likesCount"
      FROM "${TABLE.BLOGS}" b
      LEFT JOIN "${TABLE.BLOGS_VIEW}" v ON v."blogId" = b."id"
      LEFT JOIN "${TABLE.BLOGS_LIKE}" l ON l."blogId" = b."id"
      WHERE b."statusId" = 22
      GROUP BY b."id"
      ORDER BY b."created_at" DESC
    `);

    return responseData(res, 200, "Blogs fetched successfully", rows);
  } catch (error) {
    console.error("Error fetching blogs:", error);
    return errorCatchResponse(res, "Failed to fetch blogs");
  } finally {
    client.release();
  }
});

export const getBlogDetails = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { slug } = req.params;

    if (!slug) {
      return errorResponse(res, "Blog slug is required");
    }
 
    const rawIp = req.headers['x-forwarded-for']?.toString().split(',')[0].trim() || req.socket.remoteAddress || 'unknown';
    const profileId = rawIp === '::1' ? '127.0.0.1' : rawIp; 
    const { rows: blogRows } = await client.query(
      `SELECT * FROM "${TABLE.BLOGS}" WHERE slug = $1`,
      [slug]
    );

    if (blogRows.length === 0) {
      return errorResponse(res, "Blog not found");
    }

    const blog = blogRows[0];

    const blogId = blog.id; 
    try {
      await client.query(`
      INSERT INTO "${TABLE.BLOGS_VIEW}" ("blogId", "ip")
      SELECT $1, $2::VARCHAR
      WHERE NOT EXISTS (
        SELECT 1 FROM "${TABLE.BLOGS_VIEW}" WHERE "blogId" = $1 AND "ip" = $2::VARCHAR
      )
    `, [blogId, profileId]);

    } catch (viewError) {
      console.error("Error recording blog view:", viewError);
    }

    // Step 3: Fetch detailed blog info
    const { rows } = await client.query(`
      SELECT 
        b.*,
        COUNT(DISTINCT v."profileId") AS "viewsCount",
        COUNT(DISTINCT l."profileId") AS "likesCount",
        STRING_AGG(DISTINCT t."tag", ', ') AS "tags"
      FROM "${TABLE.BLOGS}" b
      LEFT JOIN "${TABLE.BLOGS_VIEW}" v ON v."blogId" = b."id"
      LEFT JOIN "${TABLE.BLOGS_LIKE}" l ON l."blogId" = b."id"
      LEFT JOIN "${TABLE.BLOGS_TAG}" t ON t."blogId" = b."id"
      WHERE b."id" = $1
      GROUP BY b."id"
    `, [blogId]);

    return responseData(res, 200, "Blog fetched successfully", rows[0]);

  } catch (error) {
    console.error("Error fetching blog:", error);
    return errorCatchResponse(res, "Failed to fetch blog");
  } finally {
    client.release();
  }
}); 

export const updateBlog = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();
  try {
    const { id } = req.params;
    const {
      title,
      content,
      category,
      excerpt,
      featuredImage,
      featureBlog,
      author,
      statusId, // new statusId
      tags
    } = req.body;

    const userId = req.user.id;
    const profileId = Number(userId);

    if (!id) {
      return errorResponse(res, "Blog ID is required");
    }

    // Step 1: Fetch existing blog
    const existingResult = await client.query(
      `SELECT "statusId", "publishDate" FROM "${TABLE.BLOGS}" WHERE "id" = $1 AND "profileId" = $2`,
      [id, profileId]
    );

    if (existingResult.rows.length === 0) {
      return errorResponse(res, "Blog not found");
    }

    const previousStatusId = existingResult.rows[0].statusId;
    const existingPublishDate = existingResult.rows[0].publishDate;

    // Step 2: Determine new publishDate
    let publishDate = existingPublishDate;

    if (statusId === previousStatusId && statusId === 22) {
      // Keep publishDate as is
    } else if (statusId === 5 || statusId === 23) {
      publishDate = null;
    } else if ((previousStatusId === 5 || previousStatusId === 23) && statusId === 22) {
      publishDate = new Date();
    }
    const featureBlogValue = featureBlog =="yes" ? true : false;

    // Step 3: Update the blog
    const result = await client.query(
      `UPDATE "${TABLE.BLOGS}" 
       SET "title" = $1, "content" = $2, "category" = $3, 
           "excerpt" = $4, "featuredImage" = $5, "author" = $6, 
           "statusId" = $7, "publishDate" = $8 ,"featured" = $9
       WHERE "id" = $10 AND "profileId" = $11
       RETURNING *`,
      [title, content, category, excerpt, featuredImage, author, statusId, publishDate,featureBlogValue, id, profileId]
    );

    // Step 4: Normalize tags (string or array)
    let normalizedTags: string[] = [];

    if (typeof tags === 'string') {
      normalizedTags = tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    } else if (Array.isArray(tags)) {
      normalizedTags = tags.map(tag => tag.trim()).filter(tag => tag);
    }

    // Step 5: Update tags in DB
    if (normalizedTags.length > 0) {
      await client.query(
        `DELETE FROM "${TABLE.BLOGS_TAG}" WHERE "blogId" = $1`,
        [id]
      );

      const tagInsertQueries = normalizedTags.map((tag: string) =>
        client.query(
          `INSERT INTO "${TABLE.BLOGS_TAG}" ("blogId", "tag") VALUES ($1, $2)`,
          [id, tag]
        )
      );

      await Promise.all(tagInsertQueries);
    }

    return responseData(res, 200, "Blog updated successfully", result.rows[0]);
  } catch (error) {
    console.error("Error updating blog:", error);
    return errorCatchResponse(res, "Failed to update blog");
  } finally {
    client.release();
  }
});


export const deleteBlog = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();
  try {
    const { id } = req.params;
    const userId = req.user.id
    const profileId = Number(userId);

    if (!id) {
      return errorResponse(res, "Blog ID is required");
    }

    await client.query('BEGIN'); // Start transaction

    // Delete related tags
    await client.query(
      `DELETE FROM "${TABLE.BLOGS_TAG}" WHERE "blogId" = $1`,
      [id]
    );

    // Delete related likes
    await client.query(
      `DELETE FROM "${TABLE.BLOGS_LIKE}" WHERE "blogId" = $1`,
      [id]
    );

    // Delete related views
    await client.query(
      `DELETE FROM "${TABLE.BLOGS_VIEW}" WHERE "blogId" = $1`,
      [id]
    );

    // Delete the blog itself
    const result = await client.query(
      `DELETE FROM "${TABLE.BLOGS}" 
       WHERE "id" = $1 AND "profileId" = $2 
       RETURNING *`,
      [id, profileId]
    );

    if (result.rows.length === 0) {
      await client.query('ROLLBACK');
      return errorResponse(res, "Blog not found or you do not have permission to delete this blog");
    }

    await client.query('COMMIT');

    return responseData(res, 200, "Blog and related data deleted successfully", result.rows[0]);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error("Error deleting blog:", error);
    return errorCatchResponse(res, "Failed to delete blog");
  } finally {
    client.release();
  }
});

export const likeDislikeBlog = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { id } = req.params;
     const userId =req.user.id
    const profileId = Number(userId);

    if (!id) {
      return errorResponse(res, "Blog ID is required");
    }
 
    const checkLike :any= await client.query(
      `SELECT * FROM "${TABLE.BLOGS_LIKE}" WHERE "blogId" = $1 AND "profileId" = $2`,
      [id, profileId]
    );

    if (checkLike.rowCount > 0) { 
      await client.query(
        `DELETE FROM "${TABLE.BLOGS_LIKE}" WHERE "blogId" = $1 AND "profileId" = $2`,
        [id, profileId]
      );
      return responseData(res, 200, "Blog disliked successfully", { liked: false });
    } else {
      // If not liked yet, add a like
      const result = await client.query(
        `INSERT INTO "${TABLE.BLOGS_LIKE}" ("blogId", "profileId") 
         VALUES ($1, $2) 
         RETURNING *`,
        [id, profileId]
      );
      return responseData(res, 200, "Blog liked successfully", { liked: true, like: result.rows[0] });
    }
  } catch (error) {
    console.error("Error toggling like:", error);
    return errorCatchResponse(res, "Failed to like/dislike blog");
  } finally {
    client.release();
  }
});

export const updateBlogStatus = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { id } = req.params;
     const userId = req.user.id
    const profileId = Number(userId);
    const { statusId } = req.body;
    if (!id) {
      return errorResponse(res, "Blog ID is required");
    }
   
    if(statusId=== 22){
      const publishDate = new Date();
      const  query = `UPDATE "${TABLE.BLOGS}" SET "statusId" = $1, "publishDate" = $2 WHERE "id" = $3 AND "profileId" = $4 RETURNING *`;
      await client.query(query, [statusId, publishDate, id, profileId]);
      return responseData(res, 200, "Blog status updated successfully", );
    }else if(statusId=== 23 || statusId=== 5){
      const publishDate = null;
      const  query = `UPDATE "${TABLE.BLOGS}" SET "statusId" = $1, "publishDate" = $2 WHERE "id" = $3 AND "profileId" = $4 RETURNING *`;
      await client.query(query, [statusId, publishDate, id, profileId]);
            return responseData(res, 200, "Blog status updated successfully", );
    }else {
      await client.query(
      `UPDATE "${TABLE.BLOGS}" SET "statusId" = $1 WHERE "id" = $2 AND "profileId" = $3 RETURNING *`,
      [statusId, id, profileId]
    );
          return responseData(res, 200, "Blog status updated successfully", );
    } 
 
  } catch (error) {
    console.error("Error updating blog status:", error);
    return errorCatchResponse(res, "Failed to update blog status");
  }
});


export const getRelatedBlog = asyncHandler(async (req: Request, res: Response) => {
  const client = await db.connect();

  try {
    const { id } = req.params;

    if (!id) {
      return errorResponse(res, "Blog id is required");
    }
 
    const { rows: blogRows } = await client.query(
      `SELECT id, category FROM "${TABLE.BLOGS}" WHERE id = $1`,
      [id]
    );

    if (blogRows.length === 0) {
      return errorResponse(res, "Blog not found");
    }

    const blog = blogRows[0];
    const blogId = blog.id;
    const category = blog.category;
 
    const { rows: relatedBlogs } = await client.query(`
      SELECT 
        b."id",
        b."title",
        b."category",
        b."excerpt",
        b."featuredImage",
        b."author",
        b."statusId",
        b."created_at",
        b."updated_at",
        b."publishDate",
        b."featured",
        b."slug",
        b."readTime", 
        STRING_AGG(DISTINCT t."tag", ', ') AS "tags"
      FROM "${TABLE.BLOGS}" b 
      LEFT JOIN "${TABLE.BLOGS_TAG}" t ON t."blogId" = b."id"
      WHERE b."category" = $1 AND b."id" != $2
      GROUP BY b."id"
      ORDER BY RANDOM()
      LIMIT 2
    `, [category, id]);

    return responseData(res, 200, "Related blogs fetched successfully", relatedBlogs);

  } catch (error) {
    console.error("Error fetching related blogs:", error);
    return errorCatchResponse(res, "Failed to fetch related blogs");
  } finally {
    client.release();
  }
});
