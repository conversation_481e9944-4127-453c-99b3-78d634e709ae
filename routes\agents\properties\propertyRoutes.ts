import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { PropertyController } from "../../../controller/agents/properties/PropertyController";


const router = Router();
const upload = storageData("documents");

const propertyController = new PropertyController();

const fields = [
  { name: "govtIssuedQr", maxCount: 1 },
  { name: "propertyPhotos", maxCount: 10 },
];

// GET all properties with pagination and filters
router.get("/", propertyController.getAllProperties);

// GET property by ID
router.get("/:id", propertyController.getPropertyById);

// POST create or update a property
router.post(
  "/",
  upload.fields(fields),
  propertyController.createOrUpdateProperty
);

// PUT update property status
router.put("/:id/status", upload.none(), propertyController.updateStatus);

// PUT toggle featured/verified flags
router.put("/:id/flag", upload.none(), propertyController.toggleFlag);

// DELETE property
router.delete("/:id", propertyController.deleteProperty);

// Upload property photos
router.patch(
  "/:propertyId/photos",
  upload.fields([{ name: "propertyPhotos", maxCount: 10 }]),
  propertyController.updatePhotos
);

export default router;
