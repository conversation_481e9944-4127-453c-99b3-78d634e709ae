import { Request, Response } from "express";
import { errorResponse, response, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import validate from "../../utils/validations";
import {
  createRoleSchema,
  updateRoleSchema,
} from "../../utils/validations/roles.validation";
import { db } from "../../config/database";
import { ROLE_QUERIES } from "../../utils/database/queries/roles";

// Create a new role
export const createRole = asyncHandler(async (req: Request, res: Response) => {
  const validation = await validate(createRoleSchema, req.body, res);
  if (!validation.success) return;

  const { name, descriptions, createdBy } = validation.data;
  const result = await db.query(ROLE_QUERIES.CREATE, [
    name,
    descriptions,
    createdBy,
  ]);

  responseData(res, 201, "Role created successfully", result.rows[0]);
});

// Get a role by ID
export const getRoleById = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const result = await db.query(ROLE_QUERIES.GET_BY_ID, [id]);

  if (result.rows.length === 0) {
    return response(res, 404, "Role not found");
  }

  responseData(res, 200, "Role fetched successfully", result.rows[0]);
});

// Get all roles
export const getAllRoles = asyncHandler(async (req: Request, res: Response) => {
  const result = await db.query(ROLE_QUERIES.GET_ALL);
  responseData(res, 200, "Roles fetched successfully", result.rows);
});

// Update a role
export const updateRole = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const validation = await validate(updateRoleSchema, req.body, res);
  if (!validation.success) return;

  const { name, descriptions, modifiedBy } = validation.data;
  const result = await db.query(ROLE_QUERIES.UPDATE, [
    name,
    descriptions,
    modifiedBy,
    id,
  ]);

  if (result.rows.length === 0) {
    return response(res, 404, "Role not found");
  }

  responseData(res, 200, "Role updated successfully", result.rows[0]);
});

// Delete a role
export const deleteRole = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const result = await db.query(ROLE_QUERIES.DELETE, [id]);

  if (result.rows.length === 0) {
    return response(res, 404, "Role not found");
  }

  responseData(res, 200, "Role deleted successfully", result.rows[0]);
});
