import { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  // Create reviews table
  await knex.schema.withSchema('agn').createTable('reviews', (table) => {
    table.increments('id').primary();
    
    table.integer('reviewerId').unsigned().notNullable();
    table.foreign('reviewerId').references('id').inTable('prf.profile').onDelete('CASCADE');
    
    table.integer('revieweeId').unsigned().notNullable();
    table.foreign('revieweeId').references('id').inTable('prf.profile').onDelete('CASCADE');
    
    table.text('reviewText').notNullable();
    table.integer('rating').notNullable().checkBetween([1, 5]); // 1-5 stars
 

    table.integer('statusId').unsigned().notNullable().defaultTo(1);
    table.foreign('statusId').references('id').inTable('look.status').onDelete('SET NULL');
    
    table.text('hideReason').nullable().comment('Reason for hiding the review when status is hidden (status 4)');
    
    table.timestamps(true, true);
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.integer('modifiedBy').unsigned().nullable();
    table.foreign('modifiedBy').references('id').inTable('sec.login').onDelete('SET NULL');
    
    table.unique(['reviewerId', 'revieweeId']);
  });

  await knex.schema.withSchema('agn').createTable('review_notes', (table) => {
    table.increments('id').primary();
    table.integer('reviewId').unsigned().notNullable();
    table.foreign('reviewId').references('id').inTable('agn.reviews').onDelete('CASCADE');
    table.text('note').notNullable();
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });

  await knex.schema.withSchema('agn').createTable('review_history', (table) => {
    table.increments('id').primary();
    table.integer('reviewId').unsigned().notNullable();
    table.foreign('reviewId').references('id').inTable('agn.reviews').onDelete('CASCADE');
    table.string('action', 50).notNullable();
    table.integer('previousStatus').nullable();
    table.integer('newStatus').nullable();
    table.text('notes').nullable();
    table.integer('createdBy').unsigned().notNullable();
    table.foreign('createdBy').references('id').inTable('sec.login').onDelete('CASCADE');
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema('agn').dropTableIfExists('review_history');
  await knex.schema.withSchema('agn').dropTableIfExists('review_notes');
  await knex.schema.withSchema('agn').dropTable('reviews');
}
