import { Request, Response } from "express";
import { ReviewService } from "../../../service/ReviewsService";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
import { AuthenticatedRequest } from "../../../dto/reviews/AuthenticatedRequestDTO";

export class AdminReviewController {
  private reviewService = new ReviewService();

  getAllReviews = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.reviewService.getAllReviews(req.query);
    return responseData(res, 200, "Reviews fetched successfully", result);
  });

  getReviewById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const result = await this.reviewService.getReviewById(Number(req.params.id));
      return responseData(res, 200, "Review fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review:", error);
      return response(res, 500, "Failed to fetch review");
    }
  });

  updateReviewStatus = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { status, note } = req.body;  
    const statusData = {
      status: status.trim(),
      note: note ? note.trim() : undefined,
    }; 
    try {
      if (!req.user?.id) {
        return response(res, 400, "User ID is required to update review status");
      }
      await this.reviewService.updateReviewStatus(
        Number(req.params.id),
        statusData,
        req.user.id
      );
      return response(res, 200, "Review status updated successfully");
    } catch (error) {
      console.error("Service error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to update review status";
      return response(res, 500, errorMessage);
    }
  });

  deleteReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      await this.reviewService.deleteReview(Number(req.params.id), req.user?.id!);
      return response(res, 200, "Review deleted successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error deleting review:", error);
      return response(res, 500, "Failed to delete review");
    }
  });

  getReviewsStats = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.reviewService.fetReviewStats();
    return responseData(res, 200, "Review statistics fetched successfully", result);
  });

  flagReview = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { flagged = true } = req.body;
      await this.reviewService.flagReview(Number(req.params.id), flagged);
      return response(res, 200, `Review ${flagged ? 'flagged' : 'unflagged'} successfully`);
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error flagging review:", error);
      return response(res, 500, "Failed to flag review");
    }
  });

  hideReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { reason } = req.body;
      await this.reviewService.hideReview(Number(req.params.id), reason, req?.user?.id!);
      return response(res, 200, "Review hidden successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error hiding review:", error);
      return response(res, 500, "Failed to hide review");
    }
  });

  restoreReview = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user?.id) {
        return response(res, 400, "User ID is required to restore review");
      }
      await this.reviewService.restoreReview(Number(req.params.id), req.user.id);
      return response(res, 200, "Review restored successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist")) {
        return response(res, 404, error.message);
      }
      console.error("Error restoring review:", error);
      return response(res, 500, "Failed to restore review");
    }
  });

  addReviewNote = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const { note } = req.body;
      if (!req.user?.id) {
        return response(res, 400, "User ID is required to add a note");
      }
      await this.reviewService.addReviewNote(reviewId, note, req.user.id);
      return response(res, 201, "Note added successfully");
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error adding note:", error);
      return response(res, 500, "Failed to add note");
    }
  });

  getReviewNotes = asyncHandler(async (req: Request, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const result = await this.reviewService.getReviewNotes(reviewId);
      return responseData(res, 200, "Review notes fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review notes:", error);
      return response(res, 500, "Failed to fetch review notes");
    }
  });

  getReviewHistory = asyncHandler(async (req: Request, res: Response) => {
    try {
      const reviewId = Number(req.params.id);
      const result = await this.reviewService.getReviewHistory(reviewId);
      return responseData(res, 200, "Review history fetched successfully", result);
    } catch (error: any) {
      if (error.message.includes("does not exist") || error.message.includes("not found")) {
        return response(res, 404, error.message);
      }
      console.error("Error fetching review history:", error);
      return response(res, 500, "Failed to fetch review history");
    }
  });
}
