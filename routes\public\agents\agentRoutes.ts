import { Router } from "express";
import { PublicAgentController } from "../../../controller/public/agents/PublicAgentController";

const router = Router();

const publicAgentController = new PublicAgentController();

// GET all properties with pagination and filters
router.get("/", publicAgentController.getAllAgents);

// GET Properties By Profile ID
router.get("/:profileId/properties", publicAgentController.getPropertiesByProfileId);

export default router;
