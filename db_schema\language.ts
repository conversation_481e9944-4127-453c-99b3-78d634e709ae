import { integer, serial, text, varchar, timestamp, pgSchema } from "drizzle-orm/pg-core";

export const listSchema = pgSchema("list");

export const language = listSchema.table(
  "languages",
  {
    id: serial("id").notNull().primaryKey(),
    name: text("name").notNull(),
    localName: text("localName"),
    code: varchar("code", { length: 255 }),
    statusId: integer("statusId").notNull(),
    createdOn: timestamp("createdOn", { withTimezone: true, precision: 6 }),
    modifiedOn: timestamp("modifiedOn", { withTimezone: true, precision: 6 }),
  }
);