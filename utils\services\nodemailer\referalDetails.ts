import nodemailer from "nodemailer";
import transporter from ".";
import { Response } from "express"; 
 
export const sendEmailToReferalDetails = async ( 
  firstName: string, 
  email: string,  
  csv: any,
  title: string
): Promise<void> => {
  try { 
    const mailToApplicant: nodemailer.SendMailOptions = {
      from: process.env.VERIFICATION_EMAIL as string,
      to: email,
      subject: "Your Referral Commission Earnings Report",
      html: ReferalTemplate(firstName, title), 
      attachments: [
        {
          filename: `${firstName}'s ${title}.csv`,
          content: csv,
        },
      ],
    };

    await transporter.sendMail(mailToApplicant);

    console.log("Email sent to referral successfully");
  } catch (error) {
    console.error("Error sending referral email:", error);
  }
}; 


const ReferalTemplate = (firstName: string, title: string) => `
<html lang="en">
  <head><meta charset="UTF-8" /></head>
  <body style="font-family:Arial,sans-serif;color:#333;background:#f9f9f9">
    <div style="max-width:600px;margin:40px auto;background:#fff;padding:20px;border-radius:8px;box-shadow:0 0 10px rgba(0,0,0,.1)">
      <h2 style="color:#5e9b6d;text-align:center">${title} Report - FindAnyAgent</h2>
      <p>Dear ${firstName},</p>

      <p style="font-size: 16px;">
        We’re pleased to share your latest referral commission earnings report, 
        attached as an Excel file for your review.
      </p>

      <p style="font-size: 16px;">
        Thank you for referring clients to FindAnyAgent and helping us grow our trusted network. 
        Your support means a lot, and we look forward to continuing our partnership.
      </p>

      <p style="font-size: 16px;">
        If you have any questions about the report or your earnings, feel free to reach out.
      </p>

      <p style="margin-top:30px">Best regards,</p>
      <p><strong>FindAnyAgent Team</strong></p>
      <p style="margin-top:10px"><a href="${process.env.WEBSITE_URL}" style="color:#004aad;">www.findanyagent.ae</a></p>
    </div>
  </body>
</html>
`;

 
 