import { db } from "../../config/database";
import { AUTH } from "../database/queries/auth";
const BASE_URL = process.env.BASE_URL?.replace(/\/+$/, "") || "http://localhost:5000";
export const isUsernameTaken = async (userName: string) => {
    const result = await db.query(
        AUTH.SELECT_BY_USERNAME_FROM_PROFILE,
        [userName.trim().toLowerCase()]
    );
    return result.rows.length > 0;
};

export const generateUniqueUsername = async (email: string) => {
    // Get the part before the @ and remove all dots
    let baseUsername = email.split("@")[0].replace(/\./g, "");
    let username = baseUsername;
    let counter = 1;

    // SQL: SELECT username FROM users WHERE LOWER(username) = LOWER($1)
    while (await isUsernameTaken(username)) {
        // Append a number until it's unique
        username = `${baseUsername}${counter}`;
        counter++;
        // To avoid infinite loop (practically shouldn't happen)
        if (counter > 1000) throw new Error("Could not generate a unique username");
    }

    return username;
};
export const parseCommaSeparated = (val: any) => {
    if (typeof val === "string") {
        return val.split(",").map(v => v.trim()).filter(v => v.length > 0);
    }
    return Array.isArray(val) ? val : [];
};

export const appendBaseUrlToFiles = (files: string[] | Record<string, string> | null | undefined): string[] => {
    if (!files) return [];
    
    if (Array.isArray(files)) {
        return files.map((path) =>
            path.startsWith("/") ? `${BASE_URL}${path}` : `${BASE_URL}/${path}`
        );
    }

    if (typeof files === "object" && files !== null) {
        return Object.values(files).map((path) =>
            path.startsWith("/") ? `${BASE_URL}${path}` : `${BASE_URL}/${path}`
        );
    }

    return [];
};