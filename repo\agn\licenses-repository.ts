import { sql } from "drizzle-orm";
import { PoolClient } from "pg";
import { drizzleDb } from "../../config/database";

export class LicensesRepository {

    async addLicenseInfo(
        profileId: number,
        agencyId: number,
        operationArea: string,
        licenseNumber: string,
        licenseExpiryDate: string,
        licenseFile: string[],
        loginId: number,
        dbClient: PoolClient
    ) {
        const query = `
        INSERT INTO agn.licenses (
            "profileId",
            "agencyId",
            "operationArea",
            "licenseNumber",
            "licenseExpiryDate",
            "licenseFile",
            "createdBy"
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *;
    `;

        const values = [
            profileId,
            agencyId,
            operationArea,
            licenseNumber,
            licenseExpiryDate,
            licenseFile,
            loginId
        ];

        return dbClient.query(query, values);
    }

    async getLicensesByProfileId(profileId: number) {
        const result = await drizzleDb.execute(
            sql`SELECT * FROM agn."licenses" WHERE "profileId" = ${profileId}`
        );

        return result;
    }


}