import type { Knex } from "knex";

export async function up(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").renameTable("agentspecialization", "specialization");
  await knex.schema.withSchema("agn").alterTable("specialization", (table) => {
    table.bigInteger("statusId").notNullable().defaultTo(1);
    table.timestamp("createdAt").defaultTo(knex.fn.now());
    table.timestamp("modifiedAt").defaultTo(knex.fn.now());
  });
  await knex.schema.withSchema("agn").table("specialization", (table) => {
    table.renameColumn("specialization", "name");
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.withSchema("agn").renameTable("specialization", "agentspecialization");
  await knex.schema.withSchema("agn").alterTable("agentspecialization", (table) => {
    table.dropColumn("statusId");
    table.dropColumn("createdAt");
    table.dropColumn("modifiedAt");
  });
 
  await knex.schema.withSchema("agn").table("agentspecialization", (table) => {
    table.renameColumn("name", "specialization");
  });
}
