import nodemailer from "nodemailer";
import transporter from ".";

export const sendTicketNotificationEmail = async (
  subject: string,
  message: string,
  to: string,
  attachments?: Array<{ filename: string; content: Buffer }>
): Promise<void> => {
  const mailOptions: nodemailer.SendMailOptions = {
    from: process.env.VERIFICATION_EMAIL as string,
    to,
    subject,
    html: await ticketNotificationHTMLTemplate(message),
    ...(attachments?.length ? { attachments } : {}),
  };
  try {
    await transporter.sendMail(mailOptions);
    console.log("Ticket notification email sent successfully!");
  } catch (error) {
    console.error("Error sending ticket notification email:", error);
  }
};

// Helper function to get priority color
const getPriorityColor = (priority: string): string => {
  switch (priority.toLowerCase()) {
    case 'urgent': return '#dc3545';
    case 'high': return '#fd7e14';
    case 'medium': return '#ffc107';
    case 'low': return '#28a745';
    default: return '#6c757d';
  }
};

// Enhanced function specifically for ticket creation notifications
export const sendTicketCreationNotification = async (
  agentName: string,
  agentEmail: string,
  ticketId: string,
  title: string,
  priority: string,
  category: string,
  description: string,
  isRegisteredUser: boolean = false,
  adminEmail?: string,
  adminName?: string
): Promise<void> => {
  const subject = `New Support Ticket Created - ${title}`;
  
  // Email for the agent/user
  const agentMessage = `
    <div style="font-family: Arial, sans-serif; line-height: 1.6;">
      <h2>New Support Ticket Created</h2>
      
      <p>Dear ${agentName},</p>
      
      <p>A new support ticket has been created for you with the following details:</p>
      
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
        <p><strong>Ticket ID:</strong> ${ticketId}</p>
        <p><strong>Title:</strong> ${title}</p>
        <p><strong>Priority:</strong> <span style="color: ${getPriorityColor(priority)};">${priority.toUpperCase()}</span></p>
        <p><strong>Category:</strong> ${category}</p>
        <p><strong>Description:</strong> ${description}</p>
      </div>
      
      ${isRegisteredUser ? 
        '<p>You can view and respond to this ticket by logging into your dashboard.</p>' : 
        '<p>Our support team will review your ticket and get back to you as soon as possible.</p>'
      }
      
      <p>If you have any questions, please don\'t hesitate to contact our support team.</p>
      
      <p>Best regards,<br>
      <strong>FindAnyAgent Support Team</strong></p>
    </div>
  `;

  // Send email to agent/user
  await sendTicketNotificationEmail(subject, agentMessage, agentEmail);

  // Send confirmation email to admin if admin details are provided
  if (adminEmail && adminName) {
    const adminSubject = `Support Ticket Created - Confirmation (ID: ${ticketId})`;
    
    const adminMessage = `
      <div style="font-family: Arial, sans-serif; line-height: 1.6;">
        <h2>Support Ticket Created - Confirmation</h2>
        
        <p>Dear ${adminName},</p>
        
        <p>You have successfully created a new support ticket with the following details:</p>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <p><strong>Ticket ID:</strong> ${ticketId}</p>
          <p><strong>Created For:</strong> ${agentName} (${agentEmail})</p>
          <p><strong>Title:</strong> ${title}</p>
          <p><strong>Priority:</strong> <span style="color: ${getPriorityColor(priority)};">${priority.toUpperCase()}</span></p>
          <p><strong>Category:</strong> ${category}</p>
          <p><strong>Description:</strong> ${description}</p>
          <p><strong>User Type:</strong> ${isRegisteredUser ? 'Registered User' : 'Unregistered User'}</p>
        </div>
        
        <p>You can manage this ticket through your admin dashboard.</p>
        
        <p>Best regards,<br>
        <strong>FindAnyAgent Support System</strong></p>
      </div>
    `;

    await sendTicketNotificationEmail(adminSubject, adminMessage, adminEmail);
  }
};

const ticketNotificationHTMLTemplate = async (message: string): Promise<string> => `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FindAnyAgent Ticket Notification</title>
  </head>
  <body style="margin:0;padding:0;background:#f9f9f9;">
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f9f9f9;">
      <tr>
        <td align="center" style="padding:40px 10px;">
          <table role="presentation" width="100%" cellspacing="0" cellpadding="0"
                 style="max-width:600px;background:#ffffff;border-radius:8px;
                        box-shadow:0 0 10px rgba(0,0,0,0.1);">
            <tr>
              <td style="padding:24px 32px;font-family:Arial,sans-serif;color:#333333;line-height:1.6;">
                <h1 style="margin:0 0 20px 0;font-size:22px;color:#5e9b6d;">
                  FindAnyAgent Ticket Notification
                </h1>
                <div style="font-size:16px;">${message}</div>
                <p style="margin:32px 0 0 0;font-size:14px;color:#777777;">
                  Best regards,<br />
                  <strong>FindAnyAgent Team</strong>
                </p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>`; 