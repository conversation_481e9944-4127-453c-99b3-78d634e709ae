import { Request, response, Response } from "express";
import async<PERSON>and<PERSON> from "../../../middleware/trycatch";
import { error, responseData } from "../../../utils/response";
import { ProfileService } from "../../../service/ProfileService";
import { ProfileStatus } from "../../../utils/enums/profile-status.enum";

export class ProfileController {

  private profileService = new ProfileService();

  getProfileById = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const result = await this.profileService.getProfileById(userId);
    return responseData(res, 200, "Profile fetched successfully", result);
  });


  updateProfileBasic = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { profileImage, firstName, lastName, middleName, nationality, gender } = req.body;
    const result = await this.profileService.updateProfileBasic(userId, profileImage, firstName, lastName, middleName, nationality, gender);
    return responseData(res, 200, "Profile updated successfully", result);
  });


  updateProfileContact = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { locationId, address } = req.body;
    const result = await this.profileService.updateProfileContact(userId, locationId, address);
    return responseData(res, 200, "Profile updated successfully", result);
  });


  updateProfileStatus = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { status } = req.params;

    // Validate status parameter
    if (!Object.values(ProfileStatus).includes(status as ProfileStatus)) {
      return error(res, 400, `Status must be either '${ProfileStatus.ACTIVATED}' or '${ProfileStatus.DEACTIVATED}'`);
    }

    try {
      const result = await this.profileService.updateProfileStatus(userId, status);
      return responseData(res, 200, "Profile status updated successfully", result);
    } catch (err: any) {
      const message = err?.message || "An error occurred while updating profile status.";
      return error(res, 400, message);
    }
  });

  updateProfessionalInfo = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id;
    const { details, experience, languages, specialization } = req.body;
    const result = await this.profileService.updateProfessionalInfo(userId, details, experience, languages, specialization);
    return responseData(res, 200, "Professional info updated successfully", result);
  });

}
