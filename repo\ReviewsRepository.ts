import { PoolClient } from "pg";
import {db} from "../config/database";
import { REVIEWS } from "../utils/database/queries/reviews";
import { AUTH } from "../utils/database/queries/auth";
import { STATUS } from "../utils/database/queries/status";
import { ReviewDTO } from "../dto/reviews/ReviewDTO";
import {
  GetAllReviewsResponseDTO,
  PaginationDTO,
} from "../dto/reviews/GetAllReviewsResponseDTO";
import { ReviewStatsDTO } from "../dto/reviews/ReviewStatsDTO";

export class ReviewRepository {

  // New: Get filtered reviews (with filters)
  async getFilteredReviews(
    limit: number,
    offset: number,
    filters: string[],
    queryParams: any[]
  ) {
    let paramIndex = queryParams.length + 1;
    const whereClause = filters.length > 0 ? `WHERE ${filters.join(" AND ")}` : "";
    const limitParam = `$${paramIndex}`;
    const offsetParam = `$${paramIndex + 1}`;
    queryParams.push(limit, offset);
    const mainQuery = `
      SELECT 
        r.id,
        r."reviewerId",
        r."revieweeId",
        r."reviewText",
        r.rating,
        r."statusId",
        r."hideReason",
        r."flagged",
        r."created_at",
        r."updated_at",
        reviewer."firstName" AS "reviewerFirstName",
        reviewer."lastName" AS "reviewerLastName",
        reviewer.email AS "reviewerEmail",
        reviewee."firstName" AS "revieweeFirstName",
        reviewee."lastName" AS "revieweeLastName",
        reviewee.email AS "revieweeEmail",
        reviewee."accountType" AS "revieweeType",
        CASE 
          WHEN reviewee."accountType" = 'Company/Agency/PropertyDeveloper' THEN agn.name
          ELSE NULL
        END AS "agencyName",
        s.name AS "statusName",
        COALESCE(
          (SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', rn.id,
              'note', rn.note,
              'createdAt', rn."created_at",
              'adminName', CONCAT(admin."firstName", ' ', admin."lastName")
            )
            ORDER BY rn."created_at" DESC
          )
          FROM agn.review_notes rn
          LEFT JOIN sec.login l ON rn."createdBy" = l.id
          LEFT JOIN prf.profile admin ON l."profileId" = admin.id
          WHERE rn."reviewId" = r.id),
          '[]'::json
        ) AS "notes",
        COALESCE(
          (SELECT JSON_AGG(
            JSON_BUILD_OBJECT(
              'id', rh.id,
              'action', rh.action,
              'previousStatus', rh."previousStatus",
              'newStatus', rh."newStatus",
              'previousStatusName', prev_status.name,
              'newStatusName', new_status.name,
              'notes', rh.notes,
              'createdAt', rh."created_at",
              'adminName', CONCAT(hist_admin."firstName", ' ', hist_admin."lastName")
            )
            ORDER BY rh."created_at" DESC
          )
          FROM agn.review_history rh
          LEFT JOIN sec.login hist_login ON rh."createdBy" = hist_login.id
          LEFT JOIN prf.profile hist_admin ON hist_login."profileId" = hist_admin.id
          LEFT JOIN look.status prev_status ON rh."previousStatus" = prev_status.id
          LEFT JOIN look.status new_status ON rh."newStatus" = new_status.id
          WHERE rh."reviewId" = r.id),
          '[]'::json
        ) AS "history"
      FROM agn.reviews r
      LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
      LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
      LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
      LEFT JOIN look.status s ON r."statusId" = s.id
      ${whereClause}
      ORDER BY r."created_at" DESC
      LIMIT ${limitParam} OFFSET ${offsetParam}
    `;
    return db.query(mainQuery, queryParams);
  }

  // New: Get filtered reviews count (with filters)
  async getFilteredReviewsCount(
    filters: string[],
    queryParams: any[]
  ) {
    const whereClause = filters.length > 0 ? `WHERE ${filters.join(" AND ")}` : "";
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM agn.reviews r
      LEFT JOIN prf.profile reviewer ON r."reviewerId" = reviewer.id
      LEFT JOIN prf.profile reviewee ON r."revieweeId" = reviewee.id
      LEFT JOIN agn.agencies agn ON r."revieweeId" = agn."profileId"
      ${whereClause}
    `;
    return db.query(countQuery, queryParams);
  }

  // New: Get all reviews (no filters)
  async getAllReviews(limit: number, offset: number) {
    return db.query(REVIEWS.GET_ALL_REVIEWS, [limit, offset]);
  }

  // New: Get all reviews count (no filters)
  async getAllReviewsCount() {
    return db.query(REVIEWS.GET_REVIEWS_COUNT);
  }

  async getReviewById(id: number): Promise<ReviewDTO | null> {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_BY_ID, [id]);
    return rows[0] || null;
  }

  async createReview(
    reviewerId: number,
    revieweeId: number,
    reviewText: string,
    rating: number,
    statusId: number,
    createdBy: number
  ): Promise<ReviewDTO> {
    // Only execute the query, no business logic
    const { rows } = await db.query(REVIEWS.CREATE_REVIEW, [
      reviewerId,
      revieweeId,
      reviewText,
      rating,
      statusId,
      createdBy,
    ]);
    return rows[0];
  }

  async updateReviewStatus(
    id: number,
    statusId: number,
    updatedBy: number
  ): Promise<void> {
    // Only execute the query, no business logic
    const result = await db.query(REVIEWS.UPDATE_REVIEW_STATUS, [
      statusId,
      updatedBy,
      id,
    ]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${id} does not exist.`);
    }
  }

  async deleteReview(id: number, modifiedBy: number): Promise<void> {
    const result = await db.query(REVIEWS.DELETE_REVIEW, [id]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${id} does not exist.`);
    }
  }

  async bulkUpdateReviewsStatus(
    reviewIds: number[],
    statusId: number,
    updatedBy: number
  ): Promise<void> {
    await db.query(REVIEWS.UPDATE__BLUK_REVIEW_STATUS, [
      statusId,
      updatedBy,
      reviewIds,
    ]);
  }

  async getProfileReviews(
    profileId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;

    const reviews = await db.query(REVIEWS.GET_APPROVED_REVIEWS_FOR_PROFILE, [
      profileId,
      limit,
      offset,
    ]);

    const totalCount = await db.query(REVIEWS.GET_TOTAL_COUNT, [profileId]);

    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows as ReviewDTO[],
      pagination,
    };
  }

  async getProfileRatingStats(profileId: number) {
    const { rows } = await db.query(REVIEWS.GET_PROFILE_RATING_STATS, [
      profileId,
    ]);
    return rows[0];
  }

  async getUserReviews(
    userId: number,
    page: number = 1,
    limit: number = 10
  ): Promise<GetAllReviewsResponseDTO> {
    const offset = (page - 1) * limit;

    const reviews = await db.query(REVIEWS.GET_USER_REVIEWS, [
      userId,
      limit,
      offset,
    ]);

    const totalCount = await db.query(REVIEWS.GET_ALL_COUNT, [userId]);

    const total = parseInt(totalCount.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    const pagination: PaginationDTO = {
      total,
      totalPages,
      currentPage: page,
      perPage: limit,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };

    return {
      reviews: reviews.rows as ReviewDTO[],
      pagination,
    };
  }

  async checkExistingReview(
    reviewerId: number,
    revieweeId: number
  ): Promise<boolean> {
    const { rows } = await db.query(REVIEWS.CHECK_EXISTING_REVIEW, [
      reviewerId,
      revieweeId,
    ]);
    return rows.length > 0;
  }

  async flagReview(id: number, flagged: boolean): Promise<void> {
    const result = await db.query(REVIEWS.UPDATE_TO_FLAG, [flagged, id]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${id} does not exist.`);
    }
  }

  async addReviewNote(
    reviewId: number,
    note: string,
    adminId: number
  ): Promise<void> {
    const result = await db.query(REVIEWS.ADD_REVIEW_NOTE, [
      reviewId,
      note,
      adminId,
    ]);
    if (result.rowCount === 0) {
      throw new Error(`Review with id ${reviewId} does not exist.`);
    }
  }

  async getReviewNotes(reviewId: number) {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_NOTES, [reviewId]);
    return rows;
  }

  async getReviewHistory(reviewId: number) {
    const { rows } = await db.query(REVIEWS.GET_REVIEW_HISTORY, [reviewId]);
    return rows;
  }

  async getPreviousStatusBeforeHidden(
    reviewId: number
  ): Promise<number | null> {
    const { rows } = await db.query(REVIEWS.GET_PREVIOUS_STATUS_BEFORE_HIDDEN, [
      reviewId,
    ]);
    return rows.length > 0 ? rows[0].previousStatus : null;
  }

  async getLastStatusBeforeHidden(reviewId: number): Promise<number | null> {
    const { rows } = await db.query(REVIEWS.GET_LAST_STATUS_BEFORE_HIDDEN, [
      reviewId,
    ]);
    return rows.length > 0 ? rows[0].statusId : null;
  }

  async logReviewAction(
    reviewId: number,
    action: string,
    previousStatus: number | null,
    newStatus: number,
    notes: string | null,
    adminId: number
  ): Promise<void> {
    await db.query(REVIEWS.LOG_REVIEW_ACTION, [
      reviewId,
      action,
      previousStatus,
      newStatus,
      notes,
      adminId,
    ]);
  }

  async getCurrentReviewStatus(reviewId: number): Promise<number | null> {
    const { rows } = await db.query(REVIEWS.GET_STATUS_BY_REVIEW_ID, [
      reviewId,
    ]);
    return rows.length > 0 ? rows[0].statusId : null;
  }

  async getHiddenStatusId(): Promise<number> {
    const { rows } = await db.query(STATUS.GET_HIDDEN_STATUS);
    if (rows.length > 0) {
      return rows[0].id;
    } else {
      throw new Error("Hidden status ID not found");
    }
  }

  async getStatusIdByName(statusName: string): Promise<  { id: number; name: string } | null> {
    const { rows } = await db.query(STATUS.GET_STATUS_BY_NAME, [statusName]);
    return rows[0] || null;
  }

  async getReviewStats(
    pendingStatusId: number,
    approvedStatusId: number,
    rejectedStatusId: number,
    hiddenStatusId: number
  ): Promise<{ statsRow: any; ratingRows: any[] }> {
    const statsQuery = await db.query(REVIEWS.REVIEW_STATS, [
      pendingStatusId,
      approvedStatusId,
      rejectedStatusId,
      hiddenStatusId,
    ]);

    const ratingDistribution = await db.query(REVIEWS.RATING_COUNT);

    return {
      statsRow: statsQuery.rows[0],
      ratingRows: ratingDistribution.rows,
    };
  }



    async getReviewIfExists(  revieweeId: number): Promise<{ rows: any[]; [key: string]: any }> {
     const revieweeExists = await db.query(AUTH.SELECT_BY_ID, [revieweeId]); 
    return  revieweeExists 
  }

    async getReviewerLogin( reviewerId: number   ): Promise<{ rows: any[]; [key: string]: any }> { 
    const reviewerLogin = await db.query(AUTH.SELECT_LOGIN_ID_BY_PROFILE_ID,  [reviewerId]  )
    return  reviewerLogin 
  }
    async getPendingStatus( ): Promise<{ rows: any[]; [key: string]: any }> {
   const pendingStatusQuery = await db.query(STATUS.GET_PENDING_STATUS);

    return  pendingStatusQuery 
  }



}
