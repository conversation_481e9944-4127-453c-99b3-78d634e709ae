import { TABLE } from "../table";
import { UpdateUserProfile } from "../../types/user";

export const AUTH = {
  SELECT_BY_EMAIL: `SELECT * FROM "${TABLE.PROFILE_TABLE}" WHERE LOWER(email) = LOWER($1) LIMIT 1`,
  SELECT_BY_ID: `SELECT * FROM "${TABLE.PROFILE_TABLE}" WHERE id = $1`,

  SELECT_ROLE_BY_NAME: `SELECT * FROM ${TABLE.ROLES} WHERE LOWER(name) = LOWER($1) LIMIT 1`,

  SELECT_PROFILE_WITH_LOGIN_AND_LOGIN_ROLE: `
   SELECT 
    json_build_object(
      'id', s.id,
      'packageTypeId', pt.id,
      'packageName', pt.name,
      'userType', pt."userType",
      'price', pt.price,
      'currency', pt.currency,
      'colorTheme', pt."colorTheme",
      'features', (
        SELECT json_agg(
          json_build_object(
            'featureName', fm."featureName",
            'featureValue', fv."featureValue",
            'featureType', fm."featureType",
            'featureConstant', fm."featureConstant",
            'displayOrder', fm."displayOrder"
          )
          ORDER BY fm."displayOrder"
        )
        FROM look.packagefeaturevalues fv
        LEFT JOIN look.packagefeaturesmeta fm ON fv."featureId" = fm."featureId"
        WHERE fv."packageTypeId" = pt.id
      )
    ) AS subscription,

    -- Optional: user & login details
    p.*,
    l.id AS "loginId",
    l."isActivated",
    lr."roleId"

  FROM "${TABLE.PROFILE_TABLE}" p
  LEFT JOIN "${TABLE.LOGIN_TABLE}" l ON p.id = l."profileId"
  LEFT JOIN "${TABLE.LOGIN_ROLE}" lr ON l.id = lr."loginId"
  LEFT JOIN list.subscription s ON s."profileId" = p.id AND s."statusId" = 1
  LEFT JOIN look.packagetype pt ON pt.id = s."packageTypeId"

  WHERE l.id = $1
  LIMIT 1;
  `,

  SELECT_BY_USERNAME_FROM_PROFILE: `SELECT * FROM "${TABLE.LOGIN_TABLE}" WHERE LOWER(username) = LOWER($1) ;`,

  GET_USER_BY_USERNAME: `SELECT * FROM "${TABLE.LOGIN_TABLE}" WHERE "username" = $1 AND "id" <> $2`,

  SELECT_BY_PROFILE_ID_FROM_LOGIN: `SELECT * FROM "${TABLE.LOGIN_TABLE}" WHERE "profileId" = $1`,
  SELECT_PASSWORD_BY_PROFILE_ID_FROM_LOGIN: `SELECT "passwordHash", "accountType" FROM "${TABLE.LOGIN_TABLE}" WHERE "profileId" = $1`,

  SELECT_BY_PROFILE_ID_FROM_LOGIN_WITH_OTP: `SELECT * FROM "${TABLE.LOGIN_TABLE}" WHERE "profileId" = $1 AND "otp" = $2`,
  SELECT_BY_USER_ID_FROM_TOKEN_LOGIN: `SELECT id FROM sec.logintoken WHERE "userId" = $1`,

  INSERT_INTO_TOKEN_LOGIN: `INSERT INTO "${TABLE.TOKEN_TABLE}" ("userId", token, "expiresOn") VALUES ($1, $2, NOW() + INTERVAL '3 day')`,

  UPDATE_TEMP_DATA: `UPDATE "prf"."profile" SET "tempData" = $1 WHERE id = $2 RETURNING *`,

  INSERT_INTO_LOGIN: `INSERT INTO "${TABLE.LOGIN_TABLE}" ("profileId", username, "accountType", "passwordHash", "otp", "expireOn", "otpPurpose") VALUES
  ($1, $2, $3, $4, $5, $6, $7) RETURNING *;`,

  INSERT_INTO_PROFILE: (fields: { [key: string]: any }) => {
    const keys = Object.keys(fields)
      .map((key) => `"${key}"`)
      .join(", ");
    const values = Object.values(fields);
    const placeholders = values.map((_, index) => `$${index + 1}`).join(", ");

    return {
      query: `INSERT INTO "${TABLE.PROFILE_TABLE}" (${keys}) VALUES (${placeholders}) RETURNING *;`,
      values,
    };
  },

  INSERT_INTO_LOGIN_WITH_STATUS: `INSERT INTO "${TABLE.LOGIN_TABLE}" ("profileId", username, "accountType" ,"accountId","isActivated") VALUES
  ($1, $2, $3, $4 , $5) RETURNING *;`,

  UPDATE_INTO_LOGIN: `UPDATE "${TABLE.LOGIN_TABLE}" SET "isActivated" = true WHERE "profileId" = $1 RETURNING *`,
  UPDATE_INTO_LOGIN_LAST_LOGIN: `UPDATE "${TABLE.LOGIN_TABLE}" SET "lastLogin" = NOW() WHERE "id" = $1 RETURNING *`,
  UPDATE_OTP_INTO_LOGIN: `UPDATE "${TABLE.LOGIN_TABLE}" SET "otp" = $1, "expireOn" = $2, "otpPurpose" = $3 WHERE "id" = $4 RETURNING *`,
  UPDATE_OTP_AND_STATUS_INTO_LOGIN: `UPDATE "${TABLE.LOGIN_TABLE}" SET "otp" = $1, "expireOn" = $2, "otpPurpose" = $3, "isActivated" = true WHERE "id" = $4 RETURNING *`,
  UPDATE_PASSWORD: `UPDATE "${TABLE.LOGIN_TABLE}" SET "passwordHash" = $1 WHERE "profileId" = $2;`,
  UPDATE_OTP_AND_PASSWORD_INTO_LOGIN: `UPDATE "${TABLE.LOGIN_TABLE}" SET "passwordHash" = $1, "otp" = $2, "expireOn" = $3, "otpPurpose" = $4 WHERE "id" = $5 RETURNING *`,

  UPDATE_INTO_TOKEN_LOGIN: `UPDATE "${TABLE.TOKEN_TABLE}" SET token = $1, "expiresOn" = NOW() + INTERVAL '3 day' WHERE "userId" = $2`,

  SELECT_TOKEN_LOGIN: `SELECT * FROM "${TABLE.TOKEN_TABLE}" WHERE token = $1 AND "userId" = $2;`,

  INSERT_INTO_LOGIN_ROLE: `INSERT INTO "${TABLE.LOGIN_ROLE}" ("loginId", "roleId", "createdBy") VALUES
  ($1, $2, $3) RETURNING *;`,

  UPDATE_INTO_LOGIN_ROLE: `UPDATE "${TABLE.LOGIN_ROLE}"
    SET "roleId" = $2,
        "modifiedBy" = $3,
        "modifiedOn" = NOW()
    WHERE "loginId" = $1
    RETURNING *;`,

  DELETE_INTO_TOKEN_LOGIN: `DELETE FROM "${TABLE.TOKEN_TABLE}" WHERE "userId" = $1`,

  SELECT_PROFILE_WITH_LOGIN: `
    SELECT 
      p.id AS "profileId", 
      p."statusId" AS "profileStatusId", 
      p."accountType" AS "accountRole",
      p.*, 
      l.id AS "loginId",
      l."statusId" AS "loginStatusId",
      l.*,
      lr."roleId",
      lr."statusId" AS loginRoleStatusId,
      lo."name" AS location,
      s.id,
      s.name AS "profileStatus",
      p."profileImage",

      -- Subscription JSON block
      (
        SELECT json_build_object(
          'id', sub.id,
          'packageTypeId', pt.id,
          'packageName', pt.name,
          'userType', pt."userType",
          'price', pt.price,
          'currency', pt.currency,
          'colorTheme', pt."colorTheme",
          'features', (
            SELECT json_agg(
              json_build_object(
                'featureName', fm."featureName",
                'featureValue', fv."featureValue",
                'featureType', fm."featureType",
                'featureConstant', fm."featureConstant",
                'displayOrder', fm."displayOrder"
              )
              ORDER BY fm."displayOrder"
            )
            FROM look.packagefeaturevalues fv
            LEFT JOIN look.packagefeaturesmeta fm ON fv."featureId" = fm."featureId"
            WHERE fv."packageTypeId" = pt.id
          )
        )
        FROM list.subscription sub
        LEFT JOIN look.packagetype pt ON pt.id = sub."packageTypeId"
        WHERE sub."profileId" = p.id AND sub."statusId" = 1
        LIMIT 1
      ) AS subscription

    FROM "${TABLE.PROFILE_TABLE}" p
    LEFT JOIN "${TABLE.LOGIN_TABLE}" l ON p.id = l."profileId"
    LEFT JOIN "${TABLE.STATUSES}" s ON p."statusId" = s.id
    LEFT JOIN "${TABLE.LOGIN_ROLE}" lr ON l.id = lr."loginId"
    LEFT JOIN list."${TABLE.LOCATION}" lo ON lo.id = p."locationId"
    WHERE l.id = $1;
  `,

  SELECT_PROFILE_WITH_LOGIN_AND_LOGINROLE: `SELECT 
    p.id AS "profileId", 
    p."statusId" AS "profileStatusId", 
    p.*, 
    l.id AS "loginId",
    l."statusId" AS "loginStatusId",
    l.*,
    lr."roleId",
    lr."statusId" AS loginRoleStatusId,
    lo."name" AS location,
    s.id,
    s.name AS "profileStatus",
    p."profileImage"
  FROM "${TABLE.PROFILE_TABLE}" p
  LEFT JOIN "${TABLE.LOGIN_TABLE}" l ON p.id = l."profileId"
  LEFT JOIN "${TABLE.STATUSES}" s ON p."statusId" = s.id
  LEFT JOIN "${TABLE.LOGIN_ROLE}" lr ON l.id = lr."loginId"
  LEFT JOIN list."${TABLE.LOCATION}" lo ON lo.id = p."locationId"
  WHERE l.id = $1;`,

  UPDATE_USER_DETAIL: (fields: UpdateUserProfile) => {
    const setClause = Object.keys(fields)
      .filter((key) => key !== "id")
      .map((key, index) => `"${key}" = $${index + 1}`)
      .join(", ");

    return `UPDATE "${TABLE.PROFILE_TABLE}" SET ${setClause} WHERE id = $${
      Object.keys(fields).length
    } RETURNING *;`;
  },

  SELECT_ACCOUNT_STATUS: (statuses: string[]) => {
    const placeholders = statuses
      .map((_, index) => `LOWER($${index + 1})`)
      .join(", ");
    return `SELECT * FROM "${TABLE.STATUSES}" WHERE LOWER(name) IN (${placeholders});`;
  },

  UPDATE_PROFILE_STATUS: `UPDATE "${TABLE.PROFILE_TABLE}" SET "statusId" = $1 WHERE id = $2`,
  UPDATE_PROFILE_STATUS_COMPLETED: `UPDATE "${TABLE.PROFILE_TABLE}" SET "isProfileCompleted" = $1 WHERE id = $2`,
  UPDATE_PROFILE_STATUS_COMPLETED_COMPANY: ` UPDATE "${TABLE.PROFILE_TABLE}" SET "isProfileCompleted" = TRUE WHERE "id" = $1`,

  SELECT_LOGIN_ROLE_BY_ID: `SELECT *
    FROM "${TABLE.LOGIN_TABLE}" l
    LEFT JOIN "${TABLE.LOGIN_ROLE}" lr ON l.id = lr."loginId"
    LEFT JOIN "${TABLE.ROLES}" r ON lr."roleId" = r.id
    WHERE l.id = $1
    LIMIT 1;`,

  SELECT_STATUS_BY_ID: `SELECT * FROM "${TABLE.STATUS}" WHERE id = $1 LIMIT 1;`,

  SELECT_LOGIN_ID_BY_PROFILE_ID:  `SELECT id FROM sec.login WHERE "profileId" = $1`
};
