export const LOOKUP = {
  STATUS: `look.status`,
};

export type FilterParams = {
  search: string | null;
  planId: number | null;
  listing: string | null; // reserved for future unions
  type: string | null; // reserved for future unions
  statusId: number | null;
  dateFrom: string | null;
  dateTo: string | null;
  profileId: number | null; // NEW filter
  sortBy: "date" | "amount" | "status";
  sortDir: "asc" | "desc";
  limit: number;
  offset: number;
};

export const PaymentQueries = {
  GET_FILTERED_PAYMENTS: (p: FilterParams) => `
    WITH subs AS (
      SELECT
        s.id,
        CONCAT('PAY-', LPAD(s.id::text, 3, '0')) AS payment_id,
        s."createdOn"::timestamptz AS date,
        'Subscription'::text AS type,
        pt.name AS description,
        s.price::numeric AS amount_value,
        s.currency::text AS currency,
        ps.id   AS status_id,
        ps.name AS status_name,
        NULL::text AS invoice_no,
        pt.id   AS package_type_id,
        pt.name AS package_type_name,
        pt.interval AS interval,

        -- profile payload
        CASE
          WHEN LOWER(p."accountType") = LOWER('Individual') THEN
            json_build_object(
              'id',           p.id,
              'firstName',    p."firstName",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
          ELSE
            json_build_object(
              'id',           a.id,
              'firstName',    a."name",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
        END AS profile

      FROM list.subscription s
      LEFT JOIN ${LOOKUP.STATUS}   ps ON ps.id  = s."paymentStatusId"
      LEFT JOIN look."packagetype" pt ON pt.id  = s."packageTypeId"
      LEFT JOIN prf.profile        p  ON p.id   = s."profileId"
      LEFT JOIN agn.agencies       a  ON a."profileId" = s."profileId"
      WHERE 1=1
        -- exclude free plans
        AND s.price::numeric > 0
        -- search
        AND (
          $1::text IS NULL OR (
            CONCAT('PAY-', LPAD(s.id::text, 3, '0')) ILIKE ('%' || $1::text || '%')
            OR pt.name ILIKE ('%' || $1::text || '%')
          )
        )
        -- plan
        AND ($2::int  IS NULL OR s."packageTypeId" = $2::int)
        -- keep $3 and $4 "used" with explicit casts so PG knows their type (no-op)
        AND ($3::text IS NULL OR TRUE)
        AND ($4::text IS NULL OR TRUE)
        -- status
        AND ($5::int IS NULL OR ps.id = $5::int)
        -- dates
        AND ($6::date IS NULL OR s."createdOn"::date >= $6::date)
        AND ($7::date IS NULL OR s."createdOn"::date <= $7::date)
        -- profile filter
        AND ($10::int IS NULL OR s."profileId" = $10::int)
    ),
    unified AS (SELECT * FROM subs)
    SELECT
      u.*,
      TRIM(u.currency || ' ' || to_char(u.amount_value, 'FM999999999.00')) AS amount_formatted,
      COUNT(*) OVER() AS total_count
    FROM unified u
    ORDER BY
      ${
        p.sortBy === "amount"
          ? `u.amount_value`
          : p.sortBy === "status"
          ? `u.status_name`
          : `u.date`
      } ${p.sortDir}
    LIMIT $8::int OFFSET $9::int
  `,

  GET_PAYMENT_DETAIL_BY_ID: `
    WITH base AS (
      SELECT
        s.id,
        CONCAT('PAY-', LPAD(s.id::text, 3, '0')) AS paymentId,
        s."createdOn"::timestamptz AS date,
        'Subscription'::text AS type,
        pt.name AS description,
        s.price::numeric AS amountValue,
        s.currency::text AS currency,
        s."subscriptionId",
        ps.id AS statusId,
        ps.name AS statusName,

        NULL::text AS invoiceNo,
        pt.id AS packageTypeId,
        pt.name AS packageTypeName,
        pt.interval AS interval,

        -- profile payload
        CASE
          WHEN LOWER(p."accountType") = LOWER('Individual') THEN
            json_build_object(
              'id',           p.id,
              'firstName',    p."firstName",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
          ELSE
            json_build_object(
              'id',           a.id,
              'firstName',    a."name",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
        END AS profile

      FROM list.subscription s
      LEFT JOIN ${LOOKUP.STATUS}   ps ON ps.id = s."paymentStatusId"
      LEFT JOIN look."packagetype" pt ON pt.id = s."packageTypeId"
      LEFT JOIN prf.profile        p  ON p.id  = s."profileId"
      LEFT JOIN agn.agencies       a  ON a."profileId" = s."profileId"
      WHERE s.id = $1::int
        AND s.price::numeric > 0 -- exclude free plans
      LIMIT 1
    )
    SELECT
      id,
      paymentId AS "paymentId",
      to_char(date, 'YYYY-MM-DD"T"HH24:MI:SSZ') AS "date",
      type,
      description,
      TRIM(currency || ' ' || to_char(amountValue, 'FM999999999.00')) AS "amount",
      amountValue AS "amountValue",
      currency,
      "subscriptionId",
      statusId   AS "statusId",
      statusName AS "statusName",
      invoiceNo  AS "invoiceNo",
      packageTypeId   AS "packageTypeId",
      packageTypeName AS "packageTypeName",
      interval        AS "interval",
      profile
    FROM base
  `,

  GET_FILTERED_PAYMENTS_PARAMS: (p: FilterParams) => [
    p.search, // $1 ::text
    p.planId, // $2 ::int
    p.listing, // $3 ::text
    p.type, // $4 ::text
    p.statusId, // $5 ::text
    p.dateFrom, // $6 ::date
    p.dateTo, // $7 ::date
    p.limit, // $8 ::int
    p.offset, // $9 ::int
    p.profileId, // $10 ::int
  ],

  GET_PAYMENT_STATUS_COUNTS: (_p: FilterParams) => `
    WITH subs AS (
      SELECT
        s.id,
        ps.id AS status_id,
        ps.name AS status_name
      FROM list.subscription s
      LEFT JOIN ${LOOKUP.STATUS} ps ON ps.id = s."paymentStatusId"
      LEFT JOIN look.packageType pt ON pt.id = s."packageTypeId"
      WHERE 1=1
        -- exclude free plans
        AND s.price::numeric > 0
        AND (
          $1::text IS NULL OR (
            CONCAT('PAY-', LPAD(s.id::text, 3, '0')) ILIKE ('%' || $1::text || '%')
            OR pt.name ILIKE ('%' || $1::text || '%')
          )
        )
        AND ($2::int  IS NULL OR s."packageTypeId" = $2::int)
        AND ($3::text IS NULL OR TRUE)
        AND ($4::text IS NULL OR TRUE)
        AND ($5::text IS NULL OR LOWER(ps.name) = LOWER($5::text))
        AND ($6::date IS NULL OR s."createdOn"::date >= $6::date)
        AND ($7::date IS NULL OR s."createdOn"::date <= $7::date)
        -- profile filter
        AND ($8::int IS NULL OR s."profileId" = $8::int)
    ),
    unified AS (SELECT * FROM subs)
    SELECT status_id, status_name, COUNT(*)::int AS count
    FROM unified
    GROUP BY status_id, status_name
    ORDER BY status_name
  `,

  GET_PAYMENT_STATUS_COUNTS_PARAMS: (p: FilterParams) => [
    p.search, // $1 ::text
    p.planId, // $2 ::int
    p.listing, // $3 ::text
    p.type, // $4 ::text
    p.statusId, // $5 ::text
    p.dateFrom, // $6 ::date
    p.dateTo, // $7 ::date
    p.profileId, // $8 ::int
  ],

  GET_PLANS: `
    SELECT
      id,
      name,
      price::numeric,
      currency,
      interval
    FROM look.packageType
    ORDER BY name
  `,

  GET_PAYMENT_TYPES: `
    SELECT DISTINCT type
    FROM (
      SELECT 'Subscription'::text AS type
    ) t
    ORDER BY type
  `,

  GET_LISTING_TYPES: `
    SELECT 
      0 AS id, 
      0 AS "displayOrder", 
      'All'::text AS listing
    UNION ALL
    SELECT DISTINCT 
      fm."featureId" AS id,
      fm."displayOrder",
      fm."featureName" AS listing
    FROM look.packagefeaturesmeta fm
    ORDER BY "displayOrder"
  `,

  GET_FILTERED_INVOICES: (p: FilterParams) => `
    WITH subs AS (
      SELECT
        s.id,
        CONCAT('INV-', LPAD(s.id::text, 3, '0')) AS payment_id,
        s."createdOn"::timestamptz AS date,
        'Subscription'::text AS type,
        pt.name AS description,
        s.price::numeric AS amount_value,
        s.currency::text AS currency,
        ps.id   AS status_id,
        ps.name AS status_name,
        NULL::text AS invoice_no,
        pt.id   AS package_type_id,
        pt.name AS package_type_name,
        pt.interval AS interval,
        s."subscriptionId",

        -- profile payload ...
        CASE
          WHEN LOWER(p."accountType") = LOWER('Individual') THEN
            json_build_object(
              'id',           p.id,
              'firstName',    p."firstName",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
          ELSE
            json_build_object(
              'id',           a.id,
              'firstName',    a."name",
              'middleName',   p."middleName",
              'lastName',     p."lastName",
              'phone',        p."phone",
              'email',        p."email",
              'profileImage', p."profileImage"
            )
        END AS profile

      FROM list.subscription s
      LEFT JOIN ${LOOKUP.STATUS}   ps ON ps.id  = s."paymentStatusId"
      LEFT JOIN look."packagetype" pt ON pt.id  = s."packageTypeId"
      LEFT JOIN prf.profile        p  ON p.id   = s."profileId"
      LEFT JOIN agn.agencies       a  ON a."profileId" = s."profileId"
      WHERE 1=1
        AND s.price::numeric > 0
        AND (
          $1::text IS NULL OR (
            CONCAT('INV-', LPAD(s.id::text, 3, '0')) ILIKE ('%' || $1::text || '%')
            OR pt.name ILIKE ('%' || $1::text || '%')
          )
        )
        AND ($2::int  IS NULL OR s."packageTypeId" = $2::int)
        AND ($3::text IS NULL OR TRUE)
        AND ($4::text IS NULL OR TRUE)
        AND ($5::text IS NULL OR LOWER(ps.name) = LOWER($5::text))
        AND ($6::date IS NULL OR s."createdOn"::date >= $6::date)
        AND ($7::date IS NULL OR s."createdOn"::date <= $7::date)
        AND ($10::int IS NULL OR s."profileId" = $10::int)
    ),
    unified AS (SELECT * FROM subs)
    SELECT
      u.*,
      TRIM(u.currency || ' ' || to_char(u.amount_value, 'FM999999999.00')) AS amount_formatted,
      COUNT(*) OVER() AS total_count
    FROM unified u
    ORDER BY
      ${
        p.sortBy === "amount"
          ? `u.amount_value`
          : p.sortBy === "status"
          ? `u.status_name`
          : `u.date`
      } ${p.sortDir}
    LIMIT $8::int OFFSET $9::int
  `,

  GET_INVOICES_STATUS_COUNTS: (_p: FilterParams) => `
    WITH subs AS (
      SELECT
        s.id,
        ps.id AS status_id,
        ps.name AS status_name
      FROM list.subscription s
      LEFT JOIN ${LOOKUP.STATUS} ps ON ps.id = s."paymentStatusId"
      LEFT JOIN look.packageType pt ON pt.id = s."packageTypeId"
      WHERE 1=1
        -- exclude free plans
        AND s.price::numeric > 0
        AND (
          $1::text IS NULL OR (
            CONCAT('INV-', LPAD(s.id::text, 3, '0')) ILIKE ('%' || $1::text || '%')  -- 👈 changed
            OR pt.name ILIKE ('%' || $1::text || '%')
          )
        )
        AND ($2::int  IS NULL OR s."packageTypeId" = $2::int)
        AND ($3::text IS NULL OR TRUE)
        AND ($4::text IS NULL OR TRUE)
        AND ($5::text IS NULL OR LOWER(ps.name) = LOWER($5::text))
        AND ($6::date IS NULL OR s."createdOn"::date >= $6::date)
        AND ($7::date IS NULL OR s."createdOn"::date <= $7::date)
        -- profile filter
        AND ($8::int IS NULL OR s."profileId" = $8::int)
    ),
    unified AS (SELECT * FROM subs)
    SELECT status_id, status_name, COUNT(*)::int AS count
    FROM unified
    GROUP BY status_id, status_name
    ORDER BY status_name
  `,

  GET_ACTIVE_SUBSCRIPTION_BY_PROFILE: `
    SELECT
      s.id,
      s."subscriptionId",
      s.price::numeric       AS amount_value,
      s.currency::text       AS currency,
      s."createdOn"::timestamptz AS created_on,
      pt.name                AS package_type_name,
      pt.interval            AS interval,
      s."statusId",
      ps.name                AS status_name,
      p."customerId"         AS stripe_customer_id
    FROM list.subscription s
    JOIN look."packagetype" pt ON pt.id = s."packageTypeId"
    LEFT JOIN ${LOOKUP.STATUS} ps ON ps.id = s."statusId"
    LEFT JOIN prf.profile p ON p.id = s."profileId"
    WHERE s."profileId" = $1::int
    AND s."statusId" = $2::int
      AND s.price::numeric > 0
    ORDER BY s."createdOn" DESC
    LIMIT 1
  `,
};
