import { Router } from "express";
import { PublicPropertyController } from "../../../controller/public/properties/PublicPropertyController";

const router = Router();

const publicPropertyController = new PublicPropertyController();

// GET all properties with pagination and filters
router.get("/featured", publicPropertyController.getFeaturedProperties);

// GET Properties By Profile ID
router.get("/search", publicPropertyController.searchProperties);

// GET Properties By Profile ID
router.get("/:slug", publicPropertyController.searchProperties);

export default router;
