import { db } from "../../config/database";
import { AUTH } from "../database/queries/auth";

export async function upsertToken(
  userId: number,
  token: string
): Promise<void> {
  try {
    const selectResult = await db.query(
      AUTH.SELECT_BY_USER_ID_FROM_TOKEN_LOGIN,
      [userId]
    );

    if (selectResult.rows.length > 0) {
      await db.query(AUTH.UPDATE_INTO_TOKEN_LOGIN, [token, userId]);
      console.log("Token updated successfully");
    } else {
      await db.query(AUTH.INSERT_INTO_TOKEN_LOGIN, [userId, token]);
      console.log("Token inserted successfully");
    }
  } catch (error) {
    console.error("Database operation failed:", error);
  }
}
