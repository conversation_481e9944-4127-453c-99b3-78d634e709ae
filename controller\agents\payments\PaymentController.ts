import { Request, Response } from "express";
import asyncHand<PERSON> from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";
import { PaymentService } from "../../../service/payments/PaymentService";
import { idSchema } from "../../../dto/payment/VaidationDTO";

export class PaymentController {
  private paymentService = new PaymentService();

  // List + filters (search, plan, listing, type, status, date range, sort, pagination)
  getAll = asyncHandler(async (req: Request, res: Response) => {
    try {
      const query = {
        ...req.query,
        profileId: req.user?.id ?? null,
      };
      const result = await this.paymentService.getAll(query);
      return responseData(res, 200, "Payments fetched successfully", result);
    } catch (err: any) {
      console.error("PaymentController.getAll error:", err);
      return response(
        res,
        err?.statusCode || 500,
        err?.message || "Failed to fetch payments."
      );
    }
  });

  // Full detail for a single payment (good for Invoice drawer/page)
  getById = asyncHandler(async (req: Request, res: Response) => {
    try {
      const { id } = idSchema.parse(req.params);
      const result = await this.paymentService.getById(id);
      if (!result) {
        return response(res, 404, "Payment not found.");
      }

      return responseData(res, 200, "Payment fetched successfully", result);
    } catch (err: any) {
      console.error("PaymentController.getById error:", err);
      return response(
        res,
        err?.statusCode || 500,
        err?.message || "Failed to fetch payment."
      );
    }
  });

  // Return filter options (plans, types, listings) precomputed from DB
  getFilterOptions = asyncHandler(async (_req: Request, res: Response) => {
    try {
      const result = await this.paymentService.getFilterOptions();
      return responseData(res, 200, "Payment filter options fetched", result);
    } catch (err: any) {
      console.error("PaymentController.getFilterOptions error:", err);
      return response(
        res,
        err?.statusCode || 500,
        err?.message || "Failed to fetch payment filter options."
      );
    }
  });

  getInvoices = asyncHandler(async (req: Request, res: Response) => {
    try {
      const query = {
        ...req.query,
        profileId: req.user?.id ?? null,
      };
      const result = await this.paymentService.getAllInvoices(query);
      return responseData(res, 200, "Invoices fetched successfully", result);
    } catch (err: any) {
      console.error("PaymentController.getInvoices error:", err);
      return response(
        res,
        err?.statusCode || 500,
        err?.message || "Failed to fetch invoices."
      );
    }
  });

  getCurrentSubscription = asyncHandler(async (req: Request, res: Response) => {
    try {
      const profileId = req.user?.id;
      if (!profileId) return response(res, 401, "Unauthorized.");

      const result = await this.paymentService.getCurrentSubscription(
        profileId
      );
      return responseData(res, 200, "Current subscription fetched", result);
    } catch (err: any) {
      console.error("PaymentController.getCurrentSubscription error:", err);
      return response(
        res,
        err?.statusCode || 500,
        err?.message || "Failed to fetch current subscription."
      );
    }
  });
}
