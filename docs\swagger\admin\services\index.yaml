/admin/services:
  get:
    tags:
      - Admin Services
    summary: Get filtered services
    operationId: getFilteredServices
    description: Fetch services with filters like status, pricing, and search.
    security:
      - cookieAuth: []
    parameters:
      - name: page
        in: query
        schema:
          type: integer
          default: 1
      - name: pageSize
        in: query
        schema:
          type: integer
          default: 10
      - name: search
        in: query
        schema:
          type: string
      - name: pricing
        in: query
        schema:
          type: string
          enum: [free, paid, ""]
      - name: statusId
        in: query
        schema:
          type: integer
          default: 20
    responses:
      "200":
        description: Services fetched successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: object
                  properties:
                    services:
                      type: array
                      items:
                        $ref: "#/components/schemas/Service"
                    pagination:
                      $ref: "#/components/schemas/Pagination"

/admin/services/{id}:
  get:
    tags:
      - Admin Services
    summary: Get service by ID
    operationId: getServiceById
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Service found
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Service"
      "404":
        description: Service not found

  delete:
    tags:
      - Admin Services
    summary: Delete a service
    operationId: deleteService
    security:
      - cookieAuth: []
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "204":
        description: Deleted successfully
      "404":
        description: Service not found

/admin/services/{id}/status:
  put:
    tags:
      - Admin Services
    summary: Update service status
    operationId: updateServiceStatus
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              status:
                type: integer
                default: 20
                description: select status 20 "Active" or 32 "Blocked"
              reason:
                type: string
                description: Optional reason for blocking the service (will be included in email notification when service is blocked/restricted)
    responses:
      "200":
        description: Status updated

/admin/services/get/status-locations-and-types:
  get:
    tags:
      - Admin Services
    summary: Get Locations, Status and types
    operationId: getFilterOptionsAndTypes
    description: Returns available locations, statuses and types
    responses:
      "200":
        description: Locations, statuses and types list
        content:
          application/json:
            schema:
              type: array
              items:
                type: object
                properties:
                  id:
                    type: integer
                  name:
                    type: string

/admin/services/note/{id}:
  get:
    tags:
      - Admin Services
    summary: Get notes for a service
    operationId: getNotes
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    responses:
      "200":
        description: Notes fetched
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: integer
                success:
                  type: boolean
                message:
                  type: string
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      note:
                        type: string
                      created_at:
                        type: string
                        format: date-time
                      created_by:
                        type: integer

/admin/services/{id}/notes:
  post:
    tags:
      - Admin Services
    summary: Create a note for a service
    operationId: createNote
    security:
      - cookieAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
    requestBody:
      required: true
      content:
        multipart/form-data:
          schema:
            type: object
            properties:
              note:
                type: string
    responses:
      "201":
        description: Note created successfully
      "400":
        description: Note cannot be empty

components:
  schemas:
    Service:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        isRemote:
          type: boolean
        locations:
          type: array
          items:
            type: object
            properties:
              value:
                type: integer
              label:
                type: string
        services:
          type: array
          items:
            type: object
            properties:
              value:
                type: integer
              label:
                type: string
        duration:
          type: object
          properties:
            id:
              type: integer
            name:
              type: string
        isFree:
          type: boolean
        price:
          type: number
        specialOffer:
          type: string
        description:
          type: string
        websiteUrl:
          type: string
        statusName:
          type: string
        images:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
              url:
                type: string
